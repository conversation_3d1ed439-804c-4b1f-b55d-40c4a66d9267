{"name": "iso-message-builder-lib", "version": "0.0.0", "private": true, "description": "Angular library for ISO20022 message handling", "type": "module", "workspaces": ["projects/*"], "scripts": {"setup": "npm install && npm run lib:build", "start": "npm run test-app:start", "test": "npm run test-app:test", "generate-rules": "npm run internal:generate-rules && npm run lib:build", "build": "npm run lib:build", "reset": "npm run internal:reset-all", "lib:build": "npm run --workspace projects/iso20022-lib build", "test-app:start": "npm run --workspace projects/test-app start", "test-app:test": "npm run --workspace projects/test-app e2e", "internal:generate-rules": "npm run internal:generate-pacs008-rules && npm run internal:generate-pacs009-rules && npm run internal:generate-pacs002-rules && npm run internal:generate-pacs004-rules", "internal:generate-pacs008-rules": "tsc -p projects/rule-generation/tsconfig.rule-generation.json --noEmit && tsx --tsconfig projects/rule-generation/tsconfig.rule-generation.json projects/rule-generation/scripts/main.ts projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.json projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.xsd projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/bah.xsd ../input/pacs.008/pacs.008.001.08_cbprplus/custom_rules projects/rule-generation/input/pacs.008/form-setup.json --output-folder projects/iso20022-lib/rules/generated/pacs008 --intermediate-results projects/rule-generation/intermediary-results/pacs_008_001_08", "internal:generate-pacs009-rules": "tsc -p projects/rule-generation/tsconfig.rule-generation.json --noEmit && tsx --tsconfig projects/rule-generation/tsconfig.rule-generation.json projects/rule-generation/scripts/main.ts projects/rule-generation/input/pacs.009/pacs.009.001.08_cbprplus/schema.json projects/rule-generation/input/pacs.009/pacs.009.001.08_cbprplus/schema.xsd projects/rule-generation/input/pacs.009/pacs.009.001.08_cbprplus/bah.xsd ../input/pacs.009/pacs.009.001.08_cbprplus/custom_rules projects/rule-generation/input/pacs.009/form-setup.json --output-folder projects/iso20022-lib/rules/generated/pacs009 --intermediate-results projects/rule-generation/intermediary-results/pacs_009_001_08", "internal:generate-pacs002-rules": "tsc -p projects/rule-generation/tsconfig.rule-generation.json --noEmit && tsx --tsconfig projects/rule-generation/tsconfig.rule-generation.json projects/rule-generation/scripts/main.ts projects/rule-generation/input/pacs.002/pacs.002.001.10_cbprplus/schema.json projects/rule-generation/input/pacs.002/pacs.002.001.10_cbprplus/schema.xsd projects/rule-generation/input/pacs.002/pacs.002.001.10_cbprplus/bah.xsd ../input/pacs.002/pacs.002.001.10_cbprplus/custom_rules projects/rule-generation/input/pacs.002/form-setup.json --output-folder projects/iso20022-lib/rules/generated/pacs002 --intermediate-results projects/rule-generation/intermediary-results/pacs_002_001_10", "internal:generate-pacs004-rules": "tsc -p projects/rule-generation/tsconfig.rule-generation.json --noEmit && tsx --tsconfig projects/rule-generation/tsconfig.rule-generation.json projects/rule-generation/scripts/main.ts projects/rule-generation/input/pacs.004/pacs.004.001.09_cbprplus/schema.json projects/rule-generation/input/pacs.004/pacs.004.001.09_cbprplus/schema.xsd projects/rule-generation/input/pacs.004/pacs.004.001.09_cbprplus/bah.xsd ../input/pacs.004/pacs.004.001.09_cbprplus/custom_rules projects/rule-generation/input/pacs.004/form-setup.json --output-folder projects/iso20022-lib/rules/generated/pacs004 --intermediate-results projects/rule-generation/intermediary-results/pacs_004_001_09", "internal:reset-all": "rm -rf node_modules package-lock.json dist projects/**/.angular/cache", "dev:export-changes": "tsc -P scripts/tsconfig.scripts.json --noEmit && tsx --tsconfig scripts/tsconfig.scripts.json scripts/export-import-changes/export-changes.ts", "dev:import-changes": "tsc -P scripts/tsconfig.scripts.json --noEmit && tsx --tsconfig scripts/tsconfig.scripts.json scripts/export-import-changes/import-changes.ts"}, "devDependencies": {"tsx": "^4.20.4", "typescript": "^5.8.3"}}