# ISO20022-Angular-Lib

## Projects

- iso20022-lib (form components, form rules directive, generated rules, types, utilities)
- test-app (small Angular app for testing/developing iso20022-lib)
- rule-generation (scripts for generating rules)

## Scripts

This repository uses a manual monorepo setup with user-facing scripts defined in the root `package.json`.

### Main Commands

| Command                  | Description                                                |
| ------------------------ | ---------------------------------------------------------- |
| `npm run setup`          | Installs all dependencies and builds the library           |
| `npm run start`          | Starts the test app for development                        |
| `npm run test`           | Runs headless end to end tests for the test app            |
| `npm run generate-rules` | Regenerates ISO 20022 rules and rebuilds the lib           |
| `npm run build`          | Rebuilds the lib                                           |
| `npm run reset`          | Deletes all `node_modules`, lock files, and Angular caches |

### Git Export/Import Changes Scripts

Scripts for transferring Git changes between environments when only text content can be copied.

#### Usage

##### Exporting Changes

```bash
# Export only uncommitted changes
npm run dev:export-changes

# Export last 2 commits + uncommitted changes
npm run dev:export-changes -- 2
```

Creates `git-changes-export.json` in project root.

##### Importing Changes

1. Copy content from `git-changes-export.json`
2. Paste into `git-changes-export.json` in target environment
3. Run: `npm run dev:import-changes`

#### Features

- Preserves commit messages, timestamps, and order
- Handles file creation, modification, and deletion
- Validates timestamps to prevent importing old changes
- Creates directory structures automatically
- Blocks import if uncommitted changes exist

## Develop lib

- All components/directves etc. in the `iso20022-lib` project need their own ng-package.json, public-api.ts, and index.ts
- All lib components must be imported via `@helaba/iso20022-lib` (even within the lib).

## Use the `Rule` types and rules in a client project

```bash
npm install <path/to/lib>/dist/helaba-iso20022-lib-0.0.1.tgz
```

## E2E tests

Before being able to run the Playwright E2E tests, you need to have installed dependencies (`npm i`) and you need to install the Playwright browsers (`npx playwright install`.)