import { Page, Locator, expect } from '@playwright/test';
import { LABELS } from '../src/app/app.form';
import { ERROR_MESSAGES } from '../src/app/error-messages';

export async function gotoHome(page: Page): Promise<void> {
  await page.goto('/');
  await expect(page.getByRole('heading', { name: 'Test Form' })).toBeVisible();
}

export async function goToPage2(page: Page): Promise<void> {
  await page.getByRole('button', { name: 'Next' }).click();
}

export async function goToPage1(page: Page): Promise<void> {
  await page.getByRole('button', { name: 'Back' }).click();
}

export function errorByRule(
  page: Page,
  ruleId: keyof typeof ERROR_MESSAGES
): Locator {
  return page.getByText(ERROR_MESSAGES[ruleId]);
}

export async function getInputByLabel(
  page: Page | Locator,
  fieldName: keyof typeof LABELS
): Promise<Locator> {
  const input = page.getByLabel(LABELS[fieldName], { exact: true });
  await expect(input).toBeVisible();
  return input;
}

export async function getFirstInputFromSimpleArray(
  page: Page,
  fieldName: keyof typeof LABELS
): Promise<Locator> {
  const fieldset = page.getByRole('group', { name: LABELS[fieldName] });
  await expect(fieldset).toBeVisible();

  const firstInput = fieldset.getByRole('textbox').first();
  await expect(firstInput).toBeVisible();

  return firstInput;
}

export async function getButtonByLabel(
  page: Page | Locator,
  label: string
): Promise<Locator> {
  const button = page.getByRole('button', { name: label });
  await expect(button).toBeVisible();
  return button;
}

export async function clickOneOfButton(
  page: Page | Locator,
  fieldName: keyof typeof LABELS,
  oneOfGroupText: string
) {
  // PrimeNG does not render accessible buttons for the one-of options, so we take the detour via the group.
  const oneOfGroup = page.getByRole('group', {
    name: oneOfGroupText,
  });
  await expect(oneOfGroup).toBeVisible();
  const oneOfOption = oneOfGroup.getByText(LABELS[fieldName], {
    exact: true,
  });
  await expect(oneOfOption).toBeVisible();
  await oneOfOption.click();
}

export async function testMaxLength(
  page: Page,
  input: Locator,
  maxLength: number,
  ruleId: string
) {
  // Initially, no error
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Type something valid
  await input.fill('a'.repeat(maxLength));
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Type something too long
  await input.fill('a'.repeat(maxLength + 1));
  await expect(errorByRule(page, ruleId)).toHaveCount(1);

  // Type something valid again
  await input.fill('a'.repeat(maxLength));
  await expect(errorByRule(page, ruleId)).toHaveCount(0);
}

export async function testRequired(
  page: Page,
  input: Locator,
  ruleId: string,
  shouldShowInitialError = false
) {
  if (shouldShowInitialError) {
    await expect(errorByRule(page, ruleId)).toHaveCount(1);
  } else {
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  }

  // Type something – no error expected
  await typeValue(input, 'some value');
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Clear the input – error should appear
  await input.clear();
  await expect(errorByRule(page, ruleId)).toHaveCount(1);
}

export async function testProhibited(
  page: Page,
  input: Locator,
  ruleId: string
) {
  // Initially, no error
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Set a value -> error visible
  await typeValue(input, 'some value');
  await expect(errorByRule(page, ruleId)).toHaveCount(1);

  // Clear value -> error disappears
  await input.clear();
  await expect(errorByRule(page, ruleId)).toHaveCount(0);
}

export async function testValue(
  page: Page,
  input: Locator,
  ruleId: string,
  value: string,
  isEqual: boolean,
  shouldShowInitialError: boolean,
  expectedErrorCount = 1
) {
  if (shouldShowInitialError) {
    await expect(errorByRule(page, ruleId)).toHaveCount(expectedErrorCount);
  } else {
    // Initially, no error should be shown
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  }

  if (isEqual) {
    // Type something invalid – error expected
    await typeValue(
      input,
      isNaN(parseFloat(value)) ? 'some invalid value' : '9999'
    );
    await expect(errorByRule(page, ruleId)).toHaveCount(expectedErrorCount);

    // Type the valid value – error should disappear
    await typeValue(input, value);
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  } else {
    // Type the value – error expected
    await typeValue(input, value);
    await expect(errorByRule(page, ruleId)).toHaveCount(expectedErrorCount);

    // Type something else – error should disappear
    await typeValue(
      input,
      isNaN(parseFloat(value)) ? 'some other value' : '9999'
    );
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  }
}

export async function testContains(
  page: Page,
  input: Locator,
  otherInput: Locator,
  ruleId: string,
  shouldContain: boolean
) {
  // Initially, no error should be shown
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  await otherInput.fill('test');

  if (shouldContain) {
    await expect(errorByRule(page, ruleId)).toHaveCount(1);
  } else {
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  }

  await input.fill('test');

  if (shouldContain) {
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  } else {
    await expect(errorByRule(page, ruleId)).toHaveCount(1);
  }

  await input.fill('something else');

  if (shouldContain) {
    await expect(errorByRule(page, ruleId)).toHaveCount(1);
  } else {
    await expect(errorByRule(page, ruleId)).toHaveCount(0);
  }
}

export async function testPattern(
  page: Page,
  input: Locator,
  ruleId: string,
  validValue: string,
  invalidValue: string
) {
  // Initially, no error should be shown
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Invalid value => pattern error
  await typeValue(input, invalidValue);
  await expect(errorByRule(page, ruleId)).toBeVisible();

  // Valid value => no error
  await typeValue(input, validValue);
  await expect(errorByRule(page, ruleId)).toHaveCount(0);
}

export async function testMaxItems(
  page: Page,
  ruleId: string,
  maxItems: number,
  addItemButtonLabel: string,
  removeItemButtonLabel: string
) {
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Add multiple items
  const addItemButton = await getButtonByLabel(page, addItemButtonLabel);
  for (let i = 0; i < maxItems; i++) {
    await addItemButton.click();
  }
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  await addItemButton.click();

  await expect(errorByRule(page, ruleId)).toHaveCount(1);

  // Remove one item again
  const removeItemButtons = await getButtonByLabel(
    page,
    `${removeItemButtonLabel} ${maxItems + 1}`
  );
  await removeItemButtons.click();

  await expect(errorByRule(page, ruleId)).toHaveCount(0);
}

export async function testNoError(
  page: Page,
  input: Locator,
  ruleId: string,
  testValue: string
) {
  // Initially, no error
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Set a value -> no error
  await typeValue(input, testValue);
  await expect(errorByRule(page, ruleId)).toHaveCount(0);

  // Clear value -> no error
  await input.clear();
  await expect(errorByRule(page, ruleId)).toHaveCount(0);
}

export async function typeValue(input: Locator, value: string) {
  await input.clear();
  // Give Angular enough time to call the 'valueChanges' subscription.
  await input.pressSequentially(value, { delay: 10 });
}

export async function debugLocators(locator: Locator) {
  const count = await locator.count();
  console.log(`Found ${count} elements:`);

  for (let i = 0; i < count; i++) {
    const element = locator.nth(i);
    const tagName = await element.evaluate((el) => el.tagName);
    const text = await element.innerText();
    const ariaLabel = await element.getAttribute('aria-label');
    console.log(
      `Element ${i}: tagName="${tagName}", innerText="${text}", aria-label="${ariaLabel}"`
    );
  }
}

export function enableBrowserLogs(page: Page) {
  page.on('console', (msg) => {
    console.log(`BROWSER LOG: ${msg.text()}`);
  });
}

export { LABELS, ERROR_MESSAGES };
