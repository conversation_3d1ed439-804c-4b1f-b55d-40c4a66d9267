import { test, expect } from '@playwright/test';
import {
  gotoHome,
  getInputByLabel,
  getButtonByLabel,
  LABELS,
  clickOneOfButton,
  testMaxLength,
  testRequired,
  testValue,
  testMaxItems,
  testProhibited,
  testPattern,
  testContains,
  getFirstInputFromSimpleArray,
  goToPage1,
  goToPage2,
  testNoError,
  enableBrowserLogs,
  typeValue,
} from './helpers';

test.describe('Validation', () => {
  test.beforeEach(async ({ page }) => {
    await gotoHome(page);
  });

  test('test-field1__required', async ({ page }) => {
    const ruleId = 'test-field1__required';
    const target = 'test-field1';

    const input = await getInputByLabel(page, target);

    await testRequired(page, input, ruleId);
  });

  test('test-field2-test__prohibited', async ({ page }) => {
    const ruleId = 'test-field2-test__prohibited';
    const target = 'test-field2-test';

    const input = await getInputByLabel(page, target);

    await testProhibited(page, input, ruleId);
  });

  test('test-field3__required', async ({ page }) => {
    const ruleId = 'test-field3__required';
    const target = 'test-field3';

    await clickOneOfButton(
      page,
      target,
      `${LABELS[target]} and ${LABELS['test-field4-flat']}`
    );

    const input = await getInputByLabel(page, target);

    await testRequired(page, input, ruleId);
  });

  test('test-field3__maxLength', async ({ page }) => {
    const ruleId = 'test-field3__maxLength';
    const target = 'test-field3';

    await clickOneOfButton(
      page,
      target,
      `${LABELS[target]} and ${LABELS['test-field4-flat']}`
    );

    const input = await getInputByLabel(page, target);

    await testMaxLength(page, input, 5, ruleId);
  });

  test('test-field4__pattern', async ({ page }) => {
    const ruleId = 'test-field4__pattern';
    const target = 'test-field4-flat';

    await clickOneOfButton(
      page,
      target,
      `${LABELS['test-field3']} and ${LABELS[target]}`
    );

    const input = await getInputByLabel(page, target);

    await testPattern(page, input, ruleId, 'abc123', 'abc$');
  });

  test('test-field4-group-nestedField1__maxLength', async ({ page }) => {
    const ruleId = 'test-field4-group-nestedField1__maxLength';
    const target = 'test-field4-group-nestedField1';

    // Select the one-of option for Field 4 to reveal Field 4 Group
    await clickOneOfButton(
      page,
      'test-field4-flat',
      `${LABELS['test-field3']} and ${LABELS['test-field4-flat']}`
    );

    // Add one item to Field 4 Group
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field4-group']}`
    );
    await addItemButton.click();

    const input = await getInputByLabel(page, target);

    await testMaxLength(page, input, 3, ruleId);
  });

  test('test-field4-group-nestedField4__maxLength', async ({ page }) => {
    const ruleId = 'test-field4-group-nestedField4__maxLength';
    const target = 'test-field4-group-nestedField4';

    // Select the one-of option for Field 4 to reveal Field 4 Group
    await clickOneOfButton(
      page,
      'test-field4-flat',
      `${LABELS['test-field3']} and ${LABELS['test-field4-flat']}`
    );

    // Add one item to Field 4 Group
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field4-group']}`
    );
    await addItemButton.click();

    // Select the one-of option for Nested Field 4 to reveal the input
    await clickOneOfButton(
      page,
      target,
      `${LABELS['test-field4-group-nestedField3']} and ${LABELS[target]}`
    );

    const input = await getInputByLabel(page, target);

    await testMaxLength(page, input, 4, ruleId);
  });

  test('test-field5__value', async ({ page }) => {
    const ruleId = 'test-field5__value';
    const target = 'test-field5';

    const input = await getInputByLabel(page, target);

    await testValue(page, input, ruleId, 'test', true, false);
  });

  test('test-field6__value', async ({ page }) => {
    const ruleId = 'test-field6__value';
    const target = 'test-field6';

    const input = await getInputByLabel(page, target);

    await testValue(page, input, ruleId, 'test', false, false);
  });

  test('test-field7__maxItems', async ({ page }) => {
    const target = 'test-field7';
    const ruleId = 'test-field7__maxItems';

    await testMaxItems(
      page,
      ruleId,
      3,
      `Add ${LABELS[target]} item`,
      `Remove ${LABELS[target]} item`
    );
  });

  test('test-field7__pattern', async ({ page }) => {
    const ruleId = 'test-field7__pattern';
    const target = 'test-field7';

    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add ${LABELS[target]} item`
    );
    await addItemButton.click();

    const firstInput = await getFirstInputFromSimpleArray(page, target);

    await testPattern(page, firstInput, ruleId, 'abc123', 'abc$');
  });

  test('test-field8__contains', async ({ page }) => {
    const ruleId = 'test-field8__contains';
    const target = 'test-field8';
    const otherField = 'test-field5';

    const input = await getInputByLabel(page, target);
    const otherInput = await getInputByLabel(page, otherField);

    await testContains(page, input, otherInput, ruleId, true);
  });

  test('test-field8__contains2', async ({ page }) => {
    const ruleId = 'test-field8__contains2';
    const target = 'test-field8';
    const otherField = 'test-field6';

    const input = await getInputByLabel(page, target);
    const otherInput = await getInputByLabel(page, otherField);

    await testContains(page, input, otherInput, ruleId, false);
  });

  test('test-field9__conditional-required+test-field10__conditional-prohibited', async ({
    page,
  }) => {
    // Fulfill conditions
    const conditionTarget = 'test-field1';
    const conditionInput = await getInputByLabel(page, conditionTarget);
    await conditionInput.fill('test');

    // Rules should now be active
    const rule1Id = 'test-field9__required-conditional';
    const rule1Target = 'test-field9';
    await goToPage2(page);
    const rule1Input = await getInputByLabel(page, rule1Target);
    await testRequired(page, rule1Input, rule1Id, true);

    const rule2Id = 'test-field10__prohibited-conditional';
    const rule2Target = 'test-field10';
    await goToPage1(page);
    const rule2Input = await getInputByLabel(page, rule2Target);
    await testProhibited(page, rule2Input, rule2Id);

    // Remove condition fulfillment
    await conditionInput.fill('no match');

    // Rules should now be inactive
    await goToPage2(page);
    await testNoError(page, rule1Input, rule1Id, 'test');
    await goToPage1(page);
    await testNoError(page, rule2Input, rule2Id, 'test');
  });

  test('test-field9__conditional-maxLength+test-field10__conditional-value', async ({
    page,
  }) => {
    // Fulfill conditions
    const conditionTarget = 'test-field7';
    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add ${LABELS[conditionTarget]} item`
    );
    await addItemButton.click();
    const conditionInput = await getFirstInputFromSimpleArray(
      page,
      conditionTarget
    );
    await conditionInput.fill('test');

    // Rules should now be active
    const rule1Id = 'test-field9__maxLength-conditional';
    const rule1Target = 'test-field9';
    await goToPage2(page);
    const rule1Input = await getInputByLabel(page, rule1Target);
    await testMaxLength(page, rule1Input, 3, rule1Id);

    const rule2Id = 'test-field10__value-conditional';
    const rule2Target = 'test-field10';
    await goToPage1(page);
    const rule2Input = await getInputByLabel(page, rule2Target);
    await testValue(page, rule2Input, rule2Id, 'test10', true, false);

    // Remove condition fulfillment
    await conditionInput.fill('');

    // Rules should now be inactive
    await goToPage2(page);
    await testNoError(page, rule1Input, rule1Id, 'test');
    await goToPage1(page);
    await testNoError(page, rule2Input, rule2Id, 'test');
  });

  test('test-field11__maxItems', async ({ page }) => {
    const target = 'test-field11';
    const ruleId = 'test-field11__maxItems';

    await testMaxItems(
      page,
      ruleId,
      2,
      `Add Item to ${LABELS[target]}`,
      `Remove ${LABELS[target]} item`
    );
  });

  test('test-field11-nestedField2__pattern', async ({ page }) => {
    const ruleId = 'test-field11-nestedField2__pattern';
    const target = 'test-field11-nestedField2';

    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();

    const input = await getInputByLabel(page, target);

    await testPattern(page, input, ruleId, 'TEST1', 'test');
  });

  test('test-field11-nestedField3__maxLength', async ({ page }) => {
    const ruleId = 'test-field11-nestedField3__maxLength';
    const target = 'test-field11-nestedField3';

    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();

    const input = await getInputByLabel(page, target);

    await testMaxLength(page, input, 5, ruleId);
  });

  test('test-field11-nestedField2__conditional-value', async ({ page }) => {
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();
    await addItemButton.click();
    const formGroupArray = page.getByRole('group', {
      name: LABELS['test-field11'],
    });
    await expect(formGroupArray).toBeVisible();
    const formGroups = formGroupArray.locator('.formArrayItem');
    await expect(formGroups).toHaveCount(2);
    const firstFormGroup = formGroups.first();
    const secondFormGroup = formGroups.nth(1);

    // Fulfill conditions
    const conditionTarget = 'test-field11-nestedField2';
    const conditionInput = await getInputByLabel(
      firstFormGroup,
      conditionTarget
    );
    await conditionInput.fill('TEST1');

    // Rule should now be active
    const ruleId =
      'test-field11-nestedField2__conditional-value+test-field11-nestedField2__value-conditional';
    const ruleTarget = 'test-field11-nestedField2';
    const ruleInput = await getInputByLabel(secondFormGroup, ruleTarget);
    await testValue(page, ruleInput, ruleId, 'TEST2', false, false, 2);

    // Remove condition fulfillment
    await conditionInput.fill('');

    // Rule should now be inactive
    await testNoError(page, ruleInput, ruleId, 'TEST2');
  });

  test('test-field11-nestedField3__conditional-value', async ({ page }) => {
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();
    await addItemButton.click();
    const formGroupArray = page.getByRole('group', {
      name: LABELS['test-field11'],
    });
    await expect(formGroupArray).toBeVisible();
    const formGroups = formGroupArray.locator('.formArrayItem');
    await expect(formGroups).toHaveCount(2);
    const firstFormGroup = formGroups.first();
    const secondFormGroup = formGroups.nth(1);

    // Fulfill conditions
    const conditionTarget = 'test-field11-nestedField2';
    const conditionInput = await getInputByLabel(
      firstFormGroup,
      conditionTarget
    );
    await conditionInput.fill('TEST1');

    // Rule should now be active
    const ruleId =
      'test-field11-nestedField3__conditional-value+test-field11-nestedField3__value-conditional';
    const ruleTarget = 'test-field11-nestedField3';
    const ruleInput = await getInputByLabel(secondFormGroup, ruleTarget);
    await testValue(page, ruleInput, ruleId, 'test', true, false, 1);

    // Remove condition fulfillment
    await conditionInput.clear();

    // Rule should now be inactive
    await testNoError(page, ruleInput, ruleId, 'test');
  });

  test('test-field11-nestedField4-deepNestedField1__conditional-required', async ({
    page,
  }) => {
    await clickOneOfButton(
      page,
      'test-field4-flat',
      `${LABELS['test-field3']} and ${LABELS['test-field4-flat']}`
    );
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field4-group']}`
    );
    await addItemButton.click();
    await addItemButton.click();
    const formGroupArray = page.getByRole('group', {
      name: LABELS['test-field4-group'],
    });
    await expect(formGroupArray).toBeVisible();
    const formGroups = formGroupArray.locator('.formArrayItem');
    await expect(formGroups).toHaveCount(2);
    const firstFormGroup = formGroups.first();
    const secondFormGroup = formGroups.nth(1);

    // Type 'TEST' into the first form group. Does not fulfill the condition yet because of 'requireEveryArrayItem: true'.
    const conditionTarget = 'test-field4-group-nestedField4';
    await clickOneOfButton(
      firstFormGroup,
      conditionTarget,
      `${LABELS['test-field4-group-nestedField3']} and ${LABELS[conditionTarget]}`
    );
    const conditionInput = await getInputByLabel(
      firstFormGroup,
      conditionTarget
    );
    await conditionInput.fill('TEST');

    // Rule should not be active yet
    const ruleId =
      'test-field11-nestedField4-deepNestedField1__conditional-required+test-field11-nestedField4-deepNestedField1__required-conditional';
    const ruleTarget = 'test-field11-nestedField4-deepNestedField1';

    const addRuleItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addRuleItemButton.click();
    const ruleFormGroupArray = page.getByRole('group', {
      name: LABELS['test-field11'],
    });
    await expect(formGroupArray).toBeVisible();
    const ruleFormGroups = ruleFormGroupArray.locator('.formArrayItem');
    await expect(ruleFormGroups).toHaveCount(1);
    const firstRuleFormGroup = ruleFormGroups.first();

    const addNestedRuleItemButton = await getButtonByLabel(
      firstRuleFormGroup,
      `Add Item to ${LABELS['test-field11-nestedField4']}`
    );
    await addNestedRuleItemButton.click();
    const nestedRuleFormGroupArray = page.getByRole('group', {
      name: LABELS['test-field11-nestedField4'],
      exact: true,
    });
    await expect(nestedRuleFormGroupArray).toBeVisible();
    const nestedRuleFormGroups =
      nestedRuleFormGroupArray.locator('.formArrayItem');
    await expect(nestedRuleFormGroups).toHaveCount(1);
    const firstNestedRuleFormGroup = nestedRuleFormGroups.first();
    const ruleInput = await getInputByLabel(
      firstNestedRuleFormGroup,
      ruleTarget
    );
    await testNoError(page, ruleInput, ruleId, 'test');

    // Fulfill the condition by typing 'TEST' into the second form group
    await clickOneOfButton(
      secondFormGroup,
      conditionTarget,
      `${LABELS['test-field4-group-nestedField3']} and ${LABELS[conditionTarget]}`
    );
    const conditionInput2 = await getInputByLabel(
      secondFormGroup,
      conditionTarget
    );
    await conditionInput2.fill('TEST');

    // Rule should now be active
    await testRequired(page, ruleInput, ruleId, true);

    // Remove condition fulfillment
    await conditionInput.fill('');

    // Rule should now be inactive
    await testNoError(page, ruleInput, ruleId, 'test');
  });

  test('test-field11-nestedField3__value__not-WRONG', async ({ page }) => {
    const ruleId = 'test-field11-nestedField3__value__not-WRONG';
    const target = 'test-field11-nestedField3';

    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();

    const input = await getInputByLabel(page, target);

    await testValue(page, input, ruleId, 'WRONG', false, false);
  });

  test('test-field11-nestedField3__value__not-WRONG2', async ({ page }) => {
    const ruleId = 'test-field11-nestedField3__value__not-WRONG2';
    const target = 'test-field11-nestedField3';

    // Add an item
    const addItemButton = await getButtonByLabel(
      page,
      `Add Item to ${LABELS['test-field11']}`
    );
    await addItemButton.click();

    const input = await getInputByLabel(page, target);

    await testValue(page, input, ruleId, 'WRONG2', false, false);
  });

  test('test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)', async ({
    page,
  }) => {
    // Fulfill conditions
    const condition1Target = 'test-field1';
    const conditionInput = await getInputByLabel(page, condition1Target);
    await conditionInput.fill('match');
    const condition1OtherTarget = 'test-field2-test';
    const condition1OtherInput = await getInputByLabel(
      page,
      condition1OtherTarget
    );
    await condition1OtherInput.fill('match');
    const condition2Target = 'test-field3';
    await clickOneOfButton(
      page,
      condition2Target,
      `${LABELS[condition2Target]} and ${LABELS['test-field4-flat']}`
    );
    const condition2Input = await getInputByLabel(page, condition2Target);
    await condition2Input.fill('test');
    await goToPage2(page);
    const condition3Target = 'test-field12';
    const condition3Input = await getInputByLabel(page, condition3Target);
    await typeValue(condition3Input, '1');

    // Rule should now be active
    const ruleId =
      'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)+test-field14__value';
    const ruleTarget = 'test-field14';
    const ruleInput = await getInputByLabel(page, ruleTarget);
    await testValue(page, ruleInput, ruleId, '0', false, true);

    // Remove condition fulfillment
    await goToPage1(page);
    await conditionInput.fill('');

    // Rule should now be inactive
    await goToPage2(page);
    await testNoError(page, ruleInput, ruleId, '0');
  });

  test('test-field15__required', async ({ page }) => {
    const ruleId = 'test-field15__required';
    const target = 'test-field15';

    await goToPage2(page);
    const input = await getInputByLabel(page, target);

    await testRequired(page, input, ruleId);
  });

  test('test-field15__pattern', async ({ page }) => {
    const ruleId = 'test-field15__pattern';
    const target = 'test-field15';

    await goToPage2(page);
    const input = await getInputByLabel(page, target);

    await testPattern(page, input, ruleId, '2025-12-31', 'abc');
  });
});
