import {
  Component,
  ChangeDetectionStrategy,
  inject,
  signal,
  OnInit,
  OnDestroy,
  computed,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormDataService,
  FormErrorsService,
  FormSetupService,
  FormValidationTriggerService,
} from '@helaba/iso20022-lib/services';
import {
  affectedFields,
  errorMessages,
  testValidationRules,
} from './test-validation-rules';
import {
  getPage1BahFormGroup,
  getPage1DocumentFormGroup,
  getPage2BahFormGroup,
  getPage2DocumentFormGroup,
  LABELS,
} from './app.form';
import { ButtonModule } from 'primeng/button';
import { StepperModule } from 'primeng/stepper';
import clsx from 'clsx';
import { Page1Component } from './page-1';
import { Page2Component } from './page-2';

@Component({
  selector: 'app-root',
  imports: [
    ReactiveFormsModule,
    ButtonModule,
    StepperModule,
    Page1Component,
    Page2Component,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit, OnDestroy {
  clsx = clsx;

  formSetupService = inject(FormSetupService);
  formValidationTriggerService = inject(FormValidationTriggerService);
  formDataService = inject(FormDataService);
  formErrorsService = inject(FormErrorsService);

  labels = LABELS;
  erroneousPages = this.formErrorsService.erroneousPages;

  isEditMode = signal(true);

  ngOnInit(): void {
    this.formSetupService.initializeMultiPageForm(
      testValidationRules,
      [
        {
          key: 'page-1',
          label: 'Page 1',
          documentFormGroupInitializer: getPage1DocumentFormGroup,
          bahFormGroupInitializer: getPage1BahFormGroup,
          component: Page1Component,
        },
        {
          key: 'page-2',
          label: 'Page 2',
          documentFormGroupInitializer: getPage2DocumentFormGroup,
          bahFormGroupInitializer: getPage2BahFormGroup,
          component: Page2Component,
        },
      ],
      affectedFields
    );
    this.formErrorsService.setErrorMessages(errorMessages);
  }

  ngOnDestroy(): void {
    this.formSetupService.cleanupFormSetup();
  }

  onSubmit() {
    const allFormGroups = this.formDataService.allFormGroups();
    for (const { documentFormGroup, bahFormGroup } of Object.values(
      allFormGroups
    )) {
      this.formValidationTriggerService.validateForm(documentFormGroup);
      this.formValidationTriggerService.validateForm(bahFormGroup);
    }

    if (
      Object.values(allFormGroups).every(
        ({ documentFormGroup, bahFormGroup }) =>
          documentFormGroup.valid && bahFormGroup.valid
      )
    ) {
      const formData = {
        ...allFormGroups['page-1'].documentFormGroup.getRawValue(),
        ...allFormGroups['page-1'].bahFormGroup.getRawValue(),
        ...allFormGroups['page-2'].documentFormGroup.getRawValue(),
        ...allFormGroups['page-2'].bahFormGroup.getRawValue(),
      };
      console.log('Submitting', formData);
    }
  }
}
