<div class="formContent">
  <h2>Test Form</h2>
  <div class="editToggle">
    @if (isEditMode()) {
    <p-button label="Click to view" (onClick)="isEditMode.set(false)" />
    } @else {
    <p-button label="Click to edit" (onClick)="isEditMode.set(true)" />
    }
  </div>

  @if (isEditMode()) {

  <p-stepper [value]="1">
    <p-step-list>
      <p-step [value]="1"
        >Page 1{{ erroneousPages().has("page-1") ? " (ERROR!)" : "" }}</p-step
      >
      <p-step [value]="2"
        >Page 2{{ erroneousPages().has("page-2") ? " (ERROR!)" : "" }}</p-step
      >
    </p-step-list>
    <p-step-panels>
      <p-step-panel [value]="1">
        <ng-template #content let-activateCallback="activateCallback">
          <app-page-1 [isReadOnly]="false" [labels]="labels" />
          <div class="buttonRow">
            <p-button
              label="Next"
              icon="pi pi-arrow-right"
              iconPos="right"
              (onClick)="activateCallback(2)"
              class="nextButton"
            />
          </div>
        </ng-template>
      </p-step-panel>
      <p-step-panel [value]="2">
        <ng-template #content let-activateCallback="activateCallback">
          <app-page-2 [isReadOnly]="false" [labels]="labels" />
          <div class="buttonRow">
            <p-button
              label="Back"
              severity="secondary"
              icon="pi pi-arrow-left"
              (onClick)="activateCallback(1)"
            />
          </div>
        </ng-template>
      </p-step-panel>
    </p-step-panels>
  </p-stepper>

  <hr />
  <p-button label="Submit" (onClick)="onSubmit()" />
  } @else {
  <app-page-1 [isReadOnly]="true" [labels]="labels" />
  <app-page-2 [isReadOnly]="true" [labels]="labels" />
  }
</div>
