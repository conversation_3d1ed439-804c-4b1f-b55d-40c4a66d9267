<app-form-radio-button-group
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldTemplate]="fieldTemplate"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [fieldPrefix]="fieldPrefix()"
  [options]="options()"
/>

<ng-template #fieldTemplate let-fieldId let-optionValue="optionValue">
  <!-- Cannot use p-radiobutton here because in nested form arrays PrimeNG seems to not respect the form hierarchy when generating the [name] attribute causing the selected option to be synced between array items. -->
  <input
    type="radio"
    [id]="fieldId"
    [value]="optionValue"
    [formControlName]="fieldName()"
  />
</ng-template>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldId]="fieldId" [pageKey]="pageKey()" />
</ng-template>

<ng-template #formValueFieldTemplate let-fieldId let-formControl="formControl">
  <app-test-app-form-value [fieldId]="fieldId" [control]="formControl" />
</ng-template>
