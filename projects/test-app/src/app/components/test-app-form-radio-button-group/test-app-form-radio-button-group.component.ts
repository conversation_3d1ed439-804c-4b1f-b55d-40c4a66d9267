import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormRadioButtonGroupComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import type { SelectOption } from '@helaba/iso20022-lib/types';

@Component({
  selector: 'app-test-app-form-radio-button-group',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
    FormRadioButtonGroupComponent,
  ],
  templateUrl: './test-app-form-radio-button-group.component.html',
  styleUrl: './test-app-form-radio-button-group.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormRadioButtonGroupComponent<T extends string> {
  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  options = input.required<SelectOption<T>[]>();
}
