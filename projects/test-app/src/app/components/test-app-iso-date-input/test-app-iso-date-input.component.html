<app-iso-date-input
  [formControlName]="fieldName()"
  [fieldId]="fieldId()"
  [dateInputTemplate]="dateInputTemplate"
  [showTime]="showTime()"
  [timeOnly]="timeOnly()"
  [dateTimePattern]="dateTimePattern()"
/>

<ng-template
  #dateInputTemplate
  let-fieldId
  let-showTime="showTime"
  let-timeOnly="timeOnly"
  let-onInput="onInput"
  let-currentDateValue="currentDateValue"
>
  <p-datepicker
    [inputId]="fieldId"
    dateFormat="yy-mm-dd"
    [showTime]="showTime"
    [showSeconds]="showTime"
    [timeOnly]="timeOnly"
    hourFormat="24"
    [showIcon]="true"
    [showClear]="true"
    [ngModel]="currentDateValue"
    [ngModelOptions]="{ standalone: true }"
    (onSelect)="onInput($event)"
    (onClear)="onInput($event)"
    (onInput)="onInput($event)"
    (onBlur)="onInput($event)"
  />
</ng-template>
