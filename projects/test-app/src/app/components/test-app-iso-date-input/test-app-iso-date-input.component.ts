import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { ISODateInputComponent } from '@helaba/iso20022-lib/components';
import { DatePattern } from '@helaba/iso20022-lib/types';
import { DatePickerModule } from 'primeng/datepicker';

@Component({
  selector: 'app-test-app-iso-date-input',
  imports: [
    DatePickerModule,
    ReactiveFormsModule,
    FormsModule,
    ISODateInputComponent,
  ],
  viewProviders: [
    {
      provide: ControlContainer,
      useExisting: FormGroupDirective, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
    },
  ],
  templateUrl: './test-app-iso-date-input.component.html',
  styleUrl: './test-app-iso-date-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppISODateInputComponent {
  fieldName = input.required<string>();
  fieldId = input.required<string>();
  showTime = input<boolean>(false);
  timeOnly = input<boolean>(false);

  // This must match the format as seen by the user in the date input implementation (PrimeNG <p-datepicker /> in this case). Add a new 'DatePattern' enum value if necessary.
  dateTimePattern = computed<DatePattern>(() => {
    const showTime = this.showTime();
    const timeOnly = this.timeOnly();

    if (timeOnly && !showTime) {
      throw new Error(`'showTime' must be true if 'timeOnly' is true`);
    }

    if (timeOnly) {
      return DatePattern.HH_MM_SS;
    }

    return showTime ? DatePattern.YYYY_MM_DD_HH_MM_SS : DatePattern.YYYY_MM_DD;
  });
}
