import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import clsx from 'clsx';

@Component({
  selector: 'app-test-app-form-text-input',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormSingleInputComponent,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
  ],
  templateUrl: './test-app-form-text-input.component.html',
  styleUrl: './test-app-form-text-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormTextInputComponent {
  clsx = clsx;

  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
}
