import { ChangeDetectionStrategy, Component, input, Type } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import { TestAppStringNumberInputComponent } from '../test-app-string-number-input';

@Component({
  selector: 'app-test-app-form-number-input',
  imports: [
    ReactiveFormsModule,
    InputNumberModule,
    FormSingleInputComponent,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
    TestAppStringNumberInputComponent,
  ],
  templateUrl: './test-app-form-number-input.component.html',
  styleUrl: './test-app-form-number-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormNumberInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  maxFractionDigits = input<number>(0);
}
