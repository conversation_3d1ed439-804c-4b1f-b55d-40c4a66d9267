import { ChangeDetectionStrategy, Component, input, Type } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { FormSingleInputComponent } from '@helaba/iso20022-lib/components';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import { TestAppISODateInputComponent } from '../test-app-iso-date-input';

@Component({
  selector: 'app-test-app-form-date-input',
  imports: [
    ReactiveFormsModule,
    InputNumberModule,
    FormSingleInputComponent,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
    TestAppISODateInputComponent,
  ],
  templateUrl: './test-app-form-date-input.component.html',
  styleUrl: './test-app-form-date-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormDateInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  showTime = input<boolean>(false);
  timeOnly = input<boolean>(false);
}
