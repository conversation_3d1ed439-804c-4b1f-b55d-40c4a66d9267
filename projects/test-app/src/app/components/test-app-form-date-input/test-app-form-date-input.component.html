<app-form-single-input
  [label]="label()"
  [fieldName]="fieldName()"
  [isReadOnly]="isReadOnly()"
  [fieldTemplate]="fieldTemplate"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [fieldPrefix]="fieldPrefix()"
/>

<ng-template #fieldTemplate let-fieldId let-hasError="hasError">
  <app-test-app-iso-date-input
    [fieldName]="fieldName()"
    [fieldId]="fieldId"
    [showTime]="showTime()"
    [timeOnly]="timeOnly()"
  />
</ng-template>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldId]="fieldId" [pageKey]="pageKey()" />
</ng-template>

<ng-template #formValueFieldTemplate let-fieldId let-formControl="formControl">
  <app-test-app-form-value [fieldId]="fieldId" [control]="formControl" />
</ng-template>
