<app-form-group-array
  [label]="label()"
  [fieldName]="fieldName()"
  [pageKey]="pageKey()"
  [isReadOnly]="isReadOnly()"
  [fieldPrefix]="fieldPrefix()"
  [errorTemplate]="errorTemplate"
  [formValueFieldTemplate]="formValueFieldTemplate"
  [addItemButtonTemplate]="addItemButtonTemplate"
  [removeItemButtonTemplate]="removeItemButtonTemplate"
  [groupFactory]="groupFactory()"
  [itemTemplate]="itemTemplate()"
/>

<ng-template #errorTemplate let-fieldId>
  <app-form-field-errors [fieldId]="fieldId" [pageKey]="pageKey()" />
</ng-template>

<ng-template #formValueFieldTemplate let-fieldId let-formControl="formControl">
  <app-test-app-form-value [fieldId]="fieldId" [control]="formControl" />
</ng-template>

<ng-template #addItemButtonTemplate let-addItem>
  <p-button
    icon="pi pi-plus"
    (click)="addItem()"
    label="Add Item"
    [ariaLabel]="`Add Item to ${label()}`"
  />
</ng-template>

<ng-template #removeItemButtonTemplate let-removeItem let-index="index">
  <p-button
    icon="pi pi-times"
    [rounded]="true"
    [text]="true"
    severity="danger"
    (click)="removeItem(index)"
    [ariaLabel]="`Remove ${label()} item ${index + 1}`"
  />
</ng-template>
