import {
  ChangeDetectionStrategy,
  Component,
  input,
  TemplateRef,
  Type,
} from '@angular/core';
import {
  ControlContainer,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import { FormGroupArrayComponent } from '@helaba/iso20022-lib/components';

@Component({
  selector: 'app-test-app-form-group-array',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
    FormGroupArrayComponent,
    ButtonModule,
  ],
  templateUrl: './test-app-form-group-array.component.html',
  styleUrl: './test-app-form-group-array.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormGroupArrayComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
  groupFactory = input.required<() => FormGroup>();
  itemTemplate = input.required<TemplateRef<unknown>>();
}
