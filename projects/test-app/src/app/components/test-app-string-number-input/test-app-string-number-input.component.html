<app-string-number-input
  [formControlName]="fieldName()"
  [fieldId]="fieldId()"
  [numberInputTemplate]="numberInputTemplate"
  [maxFractionDigits]="maxFractionDigits()"
/>

<ng-template
  #numberInputTemplate
  let-fieldId
  let-maxFractionDigits="maxFractionDigits"
  let-onInput="onInput"
  let-currentNumericValue="currentNumericValue"
>
  <p-inputnumber
    [inputId]="fieldId"
    [showButtons]="true"
    [min]="0"
    [maxFractionDigits]="maxFractionDigits"
    [ngModel]="currentNumericValue"
    [ngModelOptions]="{ standalone: true }"
    (onInput)="onInput($event)"
  />
</ng-template>
