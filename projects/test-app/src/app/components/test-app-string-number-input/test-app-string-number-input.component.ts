import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { StringNumberInputComponent } from '@helaba/iso20022-lib/components';

@Component({
  selector: 'app-test-app-string-number-input',
  imports: [
    InputNumberModule,
    ReactiveFormsModule,
    FormsModule,
    StringNumberInputComponent,
  ],
  viewProviders: [
    {
      provide: ControlContainer,
      useExisting: FormGroupDirective, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
    },
  ],
  templateUrl: './test-app-string-number-input.component.html',
  styleUrl: './test-app-string-number-input.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppStringNumberInputComponent {
  fieldName = input.required<string>();
  fieldId = input.required<string>();
  maxFractionDigits = input<number>(0);
}
