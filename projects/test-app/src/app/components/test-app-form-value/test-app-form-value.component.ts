import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-test-app-form-value',
  imports: [ReactiveFormsModule],
  templateUrl: './test-app-form-value.component.html',
  styleUrl: './test-app-form-value.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormValueComponent {
  fieldId = input.required<string>();
  control = input.required<FormControl<string | null | undefined>>();
}
