@if (shouldShowOption1() && shouldShowOption2()) {
<div class="oneOfSelectorContainer">
  <p-fieldset [legend]="header()" class="oneOfSelectorFieldset">
    <div class="oneOfSelectButton">
      <app-select-button
        [options]="selectButtonOptions()"
        [isReadOnly]="isReadOnly()"
        [defaultValue]="
          !hasFilledOutOption1Fields() && !hasFilledOutOption2Fields()
            ? defaultOption()
            : hasFilledOutOption1Fields()
            ? OPTION1
            : hasFilledOutOption2Fields()
            ? OPTION2
            : null
        "
        (onChange)="onChangeSelectedOption($event)"
      />
    </div>

    <div class="oneOfContent">
      @if (selectedOption() === OPTION1) {
      <p-panel>
        <ng-container *ngTemplateOutlet="option1Content" />
      </p-panel>
      } @if (selectedOption() === OPTION2) {
      <p-panel>
        <ng-container *ngTemplateOutlet="option2Content" />
      </p-panel>
      } @if (isReadOnly() && !hasFilledOutOption1Fields() &&
      !hasFilledOutOption2Fields()) {
      <p-panel> No option selected. </p-panel>
      }
    </div>

    <!--  Only show errors if no option is selected. If an option is selected, the individual fields are shown, displaying their own errors. -->
    @if (!selectedOption()) {
    <div>
      @for (fieldId of allFieldIds(); track fieldId) {
      <app-form-field-errors [fieldId]="fieldId" [pageKey]="pageKey()" />
      }
    </div>
    }
  </p-fieldset>
</div>
} @else { @if (shouldShowOption1()) {
<ng-container *ngTemplateOutlet="option1Content" />
} @if (shouldShowOption2()) {
<ng-container *ngTemplateOutlet="option2Content" />
} }

<ng-template #option1Content>
  <app-form-fields-container>
    <ng-content select="[slot='option1']" />
  </app-form-fields-container>
</ng-template>
<ng-template #option2Content>
  <app-form-fields-container>
    <ng-content select="[slot='option2']" />
  </app-form-fields-container>
</ng-template>
