import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  signal,
} from '@angular/core';
import { FieldsetModule } from 'primeng/fieldset';
import { PanelModule } from 'primeng/panel';
import { SelectButtonModule } from 'primeng/selectbutton';
import { FormGroup, FormsModule } from '@angular/forms';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { getFieldId } from '@helaba/iso20022-lib/util';
import type { SelectOption } from '@helaba/iso20022-lib/types';
import { SelectButtonComponent } from '../select-button';
import {
  OneOfSelectorOption,
  OPTION1_VALUE,
  OPTION2_VALUE,
} from './one-of-selector.types';
import { NgTemplateOutlet } from '@angular/common';
import { FormFieldsContainerComponent } from '../form-fields-container';
import {
  AdvancedModeService,
  FormContextProviderService,
} from '@helaba/iso20022-lib/services';
import { getFieldIdsFromFormGroup } from '@helaba/iso20022-lib/angular-utils';

function resetField(fieldName: string, formGroup: FormGroup) {
  const fieldControl = formGroup.get(fieldName);
  if (!fieldControl) {
    throw new Error(
      `Cannot find control for field name "${fieldName}" in form group.`
    );
  }
  fieldControl.reset();
  fieldControl.markAsTouched();
}

@Component({
  selector: 'app-one-of-selector',
  imports: [
    FieldsetModule,
    SelectButtonModule,
    FormsModule,
    FormFieldErrorsComponent,
    SelectButtonComponent,
    PanelModule,
    NgTemplateOutlet,
    FormFieldsContainerComponent,
  ],
  templateUrl: './one-of-selector.component.html',
  styleUrl: './one-of-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OneOfSelectorComponent {
  readonly OPTION1 = OPTION1_VALUE;
  readonly OPTION2 = OPTION2_VALUE;

  header = input.required<string>();
  option1Label = input.required<string>();
  option2Label = input.required<string>();
  option1FieldNames = input.required<string[]>();
  option2FieldNames = input.required<string[]>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>(''); // Contains the upstream form structure including dots and array indices.
  defaultOption = input<OneOfSelectorOption | null>(null);

  formContextProvider = inject(FormContextProviderService);
  advancedModeService = inject(AdvancedModeService);

  option1FieldIds = computed(() => {
    return this.option1FieldNames().map((fieldName) =>
      getFieldId(this.fieldPrefix(), fieldName)
    );
  });
  option2FieldIds = computed(() => {
    return this.option2FieldNames().map((fieldName) =>
      getFieldId(this.fieldPrefix(), fieldName)
    );
  });

  formGroup = computed<FormGroup>(() => {
    const fg = this.formContextProvider.currentFormContext()?.formGroup();
    if (!fg) {
      throw new Error('FormGroup is not set in FormContextProviderService');
    }
    return fg;
  });

  presentFieldIds = computed(() => {
    return (
      this.formContextProvider.currentFormContext()?.presentFieldIds() ||
      new Set<string>()
    );
  });

  // Compute field IDs from the current form group for the field names of the one of selector.
  allFieldIds = computed<string[]>(() => {
    const formGroup = this.formGroup();
    const option1FieldNames = this.option1FieldNames();
    const option2FieldNames = this.option2FieldNames();
    const currentFieldNames = [...option1FieldNames, ...option2FieldNames];
    const currentFieldIds = currentFieldNames.map((fieldName) =>
      getFieldId(this.fieldPrefix(), fieldName)
    );

    const currentFormGroupFieldIds = getFieldIdsFromFormGroup(formGroup);

    return currentFormGroupFieldIds.filter((fieldId) =>
      currentFieldIds.includes(fieldId)
    );
  });

  allFieldIdsToShowInFormGroup = computed(() => {
    const currentFormContext = this.formContextProvider.currentFormContext();
    const isReadOnly = this.isReadOnly();
    const isAdvancedMode = this.advancedModeService.isAdvancedMode();
    const advancedModeFields = this.advancedModeService.advancedModeFields();

    if (!currentFormContext) {
      return new Set<string>();
    }

    return currentFormContext.getFieldIdsToShow(
      isReadOnly,
      isAdvancedMode,
      advancedModeFields
    );
  });

  // Compute whether the user has filled out option 1 or option 2 fields (or none) for readonly display.
  hasFilledOutOption1Fields = computed(() => {
    const presentFieldIds = this.presentFieldIds();
    return this.option1FieldIds().some((fieldId) =>
      presentFieldIds.has(fieldId)
    );
  });

  hasFilledOutOption2Fields = computed(() => {
    const presentFieldIds = this.presentFieldIds();
    return this.option2FieldIds().some((fieldId) =>
      presentFieldIds.has(fieldId)
    );
  });

  shouldShowOption1 = computed(() => {
    const option1FieldIds = this.option1FieldIds();
    return option1FieldIds.some((fieldId) =>
      this.allFieldIdsToShowInFormGroup().has(fieldId)
    );
  });

  shouldShowOption2 = computed(() => {
    const option2FieldIds = this.option2FieldIds();
    return option2FieldIds.some((fieldId) =>
      this.allFieldIdsToShowInFormGroup().has(fieldId)
    );
  });

  selectButtonOptions = computed<SelectOption<string>[]>(() => {
    return [
      {
        key: this.option1Label(),
        label: this.option1Label(),
        value: this.OPTION1,
      },
      {
        key: this.option2Label(),
        label: this.option2Label(),
        value: this.OPTION2,
      },
    ];
  });

  selectedOption = signal<string | null>(null);

  onChangeSelectedOption(newSelectedOption: string) {
    const formGroup = this.formGroup();

    this.selectedOption.set(newSelectedOption);

    if (newSelectedOption === this.OPTION1) {
      for (const fieldName of this.option2FieldNames()) {
        resetField(fieldName, formGroup);
      }
    } else if (newSelectedOption === this.OPTION2) {
      for (const fieldName of this.option1FieldNames()) {
        resetField(fieldName, formGroup);
      }
    }
  }
}
