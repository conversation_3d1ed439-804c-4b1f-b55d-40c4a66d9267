import { ChangeDetectionStrategy, Component, input, Type } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { FormFieldErrorsComponent } from '../form-field-errors';
import { TestAppFormValueComponent } from '../test-app-form-value';
import { FormArrayInputComponent } from '@helaba/iso20022-lib/components';

@Component({
  selector: 'app-test-app-form-array-input',
  imports: [
    ReactiveFormsModule,
    InputTextModule,
    FormFieldErrorsComponent,
    TestAppFormValueComponent,
    FormArrayInputComponent,
    ButtonModule,
  ],
  templateUrl: './test-app-form-array-input.component.html',
  styleUrl: './test-app-form-array-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TestAppFormArrayInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();
  fieldPrefix = input<string>('');
}
