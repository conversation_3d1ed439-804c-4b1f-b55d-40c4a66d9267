// To let <PERSON><PERSON> have access to the error messages, we cannot have them in a file that imports modules that require 'bundler' module resolution so we put them in this separate file.
// <PERSON><PERSON> uses ts-node for execution, which requires explicit imports.
export const ERROR_MESSAGES: Record<string, string> = {
  'test-field1__required': 'Field 1 is required',
  'test-field2-test__prohibited': 'Field 2 is prohibited',
  'test-field3__required': 'Field 3 is required',
  'test-field3__maxLength': 'Field 3 has a maximum length of 5 characters',
  'test-field4__pattern': 'Field 4 should only contain alphanumeric characters',
  'test-field4-group-nestedField1__maxLength':
    'Field 4 - Nested Field 1 has a maximum length of 3 characters',
  'test-field4-group-nestedField4__maxLength':
    'Field 4 - Nested Field 4 has a maximum length of 4 characters',
  'test-field5__value': "Field 5 must have the value 'test'",
  'test-field6__value': "Field 6 must not have the value 'test'",
  'test-field7__maxItems': 'Field 7 should have a maximum of 3 items',
  'test-field7__pattern': 'Field 7 should only contain alphanumeric characters',
  'test-field8__contains': 'Field 8 must contain the value from Field 5',
  'test-field8__contains2':
    'Field 8 must not contain the value from Field 6 or Field 7',
  'test-field9__required-conditional':
    "Field 9 is required if Field 1 is 'test'",
  'test-field10__prohibited-conditional':
    "Field 10 is prohibited if Field 1 is 'test'",
  'test-field9__maxLength-conditional':
    'Field 9 has a maximum length of 3 if Field 7 is present',
  'test-field10__value-conditional':
    "Field 10 must have the value 'test10' if Field 7 is present",
  'test-field11__maxItems': 'Field 11 should have a maximum of 2 items',
  'test-field11-nestedField2__pattern':
    'Field 11 - Nested Field 2 must be one of "TEST1", "TEST2".',
  'test-field11-nestedField3__maxLength':
    'Field 11 - Nested Field 3 has a maximum length of 5 characters.',
  'test-field11-nestedField4__required':
    'Field 11 - Nested Field 4 is required.',
  'test-field11-nestedField2__conditional-value+test-field11-nestedField2__value-conditional':
    'Field 11 - Nested Field 2 must not have the value "TEST2" if Nested Field 2 is "TEST1"',
  'test-field11-nestedField3__conditional-value+test-field11-nestedField3__value-conditional':
    'Field 11 - Nested Field 3 must have the value "test" if Nested Field 2 is "TEST1".',
  'test-field11-nestedField4-deepNestedField1__conditional-required+test-field11-nestedField4-deepNestedField1__required-conditional':
    'Field 11 - Nested Field 4 - Deep Nested Field1 must be present if each of Field 4 Group - Nested Field 4 has the value "TEST"',
  'test-field11-nestedField3__value__not-WRONG':
    'Field 11 - Nested Field 3 must not have the value "WRONG".',
  'test-field11-nestedField3__value__not-WRONG2':
    'Field 11 - Nested Field 3 must not have the value "WRONG2".',
  'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)+test-field14__required':
    "Field 14 is required if Field 1 equals Field 2 and Field 3 is 'test' and Field 12 is greater than Field 13",
  'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)+test-field14__value':
    "Field 14 must not have the value '0' if Field 1 equals Field 2 and Field 3 is 'test' and Field 12 is greater than Field 13",
  'test-field15__required': 'Field 15 is required',
  'test-field15__pattern': 'Field 15 must be a valid ISO date',
};
