import { type Rule } from '@helaba/iso20022-lib/rules';
import {
  computeAffectedFieldsForFieldValueChange,
  mapToObject,
} from '@helaba/iso20022-lib/rules';
import { SIMPLIFIED_SCHEMA } from './app.form';
import { ERROR_MESSAGES } from './error-messages';

export const testValidationRules: Rule<string, undefined>[] = [
  {
    id: 'test-field1__required',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'required',
    target: 'test-field1',
  },
  {
    id: 'test-field2-test__prohibited',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'prohibited',
    target: 'test-field2-test',
  },
  {
    id: 'test-field3__required',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'required',
    target: 'test-field3',
  },
  {
    id: 'test-field3__maxLength',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxLength',
    target: 'test-field3',
    value: 5,
  },
  {
    id: 'test-field4__pattern',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'pattern',
    target: 'test-field4-flat',
    value: '^[a-zA-Z0-9]*$',
  },
  {
    id: 'test-field4-group-nestedField1__maxLength',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxLength',
    target: 'test-field4-group-nestedField1',
    value: 3,
  },
  {
    id: 'test-field4-group-nestedField4__maxLength',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxLength',
    target: 'test-field4-group-nestedField4',
    value: 4,
  },
  {
    id: 'test-field5__value',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    target: 'test-field5',
    value: 'test',
    isEqual: true,
  },
  {
    id: 'test-field6__value',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    target: 'test-field6',
    value: 'test',
    isEqual: false,
  },
  {
    id: 'test-field7__maxItems',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxItems',
    target: 'test-field7',
    value: 3,
  },
  {
    id: 'test-field7__pattern',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'pattern',
    target: 'test-field7',
    value: '^[a-zA-Z0-9]*$',
  },
  {
    id: 'test-field8__contains',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'contains',
    target: 'test-field8',
    contains: true,
    value: ['test-field5'],
  },
  {
    id: 'test-field8__contains2',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'contains',
    target: 'test-field8',
    contains: false,
    value: ['test-field6', 'test-field7'],
  },
  {
    id: 'test-field9__conditional-required+test-field10__conditional-prohibited',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        field: 'test-field1',
        type: 'value',
        value: 'test',
      },
    ],
    rules: [
      {
        id: 'test-field9__required-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'test-field9',
      },
      {
        id: 'test-field10__prohibited-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'test-field10',
      },
    ],
  },
  {
    id: 'test-field9__conditional-maxLength+test-field10__conditional-value',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        field: 'test-field7',
        type: 'present',
        value: true,
      },
    ],
    rules: [
      {
        id: 'test-field9__maxLength-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxLength',
        target: 'test-field9',
        value: 3,
      },
      {
        id: 'test-field10__value-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value',
        target: 'test-field10',
        value: 'test10',
        isEqual: true,
      },
    ],
  },
  {
    id: 'test-field11__maxItems',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxItems',
    target: 'test-field11',
    value: 2,
  },
  {
    id: 'test-field11-nestedField2__pattern',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'pattern',
    value: '^(TEST1|TEST2)?$',
    target: 'test-field11-nestedField2',
  },
  {
    id: 'test-field11-nestedField3__maxLength',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'maxLength',
    value: 5,
    target: 'test-field11-nestedField3',
  },
  {
    id: 'test-field11-nestedField2__conditional-value',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'TEST1',
        field: 'test-field11-nestedField2',
      },
    ],
    rules: [
      {
        id: 'test-field11-nestedField2__conditional-value+test-field11-nestedField2__value-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value',
        value: 'TEST2',
        isEqual: false,
        target: 'test-field11-nestedField2',
      },
    ],
  },
  {
    id: 'test-field11-nestedField3__conditional-value',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'TEST1',
        field: 'test-field11-nestedField2',
      },
    ],
    rules: [
      {
        id: 'test-field11-nestedField3__conditional-value+test-field11-nestedField3__value-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value',
        value: 'test',
        isEqual: true,
        target: 'test-field11-nestedField3',
      },
    ],
  },
  {
    id: 'test-field11-nestedField4-deepNestedField1__conditional-required',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        field: 'test-field4-group-nestedField4',
        value: 'TEST',
        requireEveryArrayItem: true,
      },
    ],
    rules: [
      {
        id: 'test-field11-nestedField4-deepNestedField1__conditional-required+test-field11-nestedField4-deepNestedField1__required-conditional',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'test-field11-nestedField4-deepNestedField1',
      },
    ],
  },
  {
    id: 'test-field11-nestedField3__value__not-WRONG',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'WRONG',
    isEqual: false,
    target: 'test-field11-nestedField3',
  },
  {
    id: 'test-field11-nestedField3__value__not-WRONG2',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'WRONG2',
    isEqual: false,
    target: 'test-field11-nestedField3',
  },
  {
    id: 'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'equal',
        value: true,
        field: 'test-field1',
        otherField: 'test-field2-test',
      },
      {
        type: 'value',
        value: 'test',
        field: 'test-field3',
      },
      {
        type: 'greaterThan',
        field: 'test-field12',
        otherField: 'test-field13',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: 'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)+test-field14__required',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'test-field14',
      },
      {
        id: 'test-field1+test-field2-test+test-field3+test-field14__conditional-(required+value)+test-field14__value',
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value',
        value: '0',
        isEqual: false,
        target: 'test-field14',
      },
    ],
    rulesConnector: 'and',
  },
  {
    id: 'test-field15__required',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'required',
    target: 'test-field15',
  },
  {
    id: 'test-field15__pattern',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'pattern',
    value:
      '^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$',
    target: 'test-field15',
  },
];

export const errorMessages: Map<string, string> = new Map(
  Object.entries(ERROR_MESSAGES)
);

const affectedFieldsMap = computeAffectedFieldsForFieldValueChange(
  testValidationRules,
  SIMPLIFIED_SCHEMA
);

export const affectedFields = mapToObject(affectedFieldsMap);
