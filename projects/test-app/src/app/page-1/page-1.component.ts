import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { LabelIdentifier } from '../app.form';
import { ReactiveFormsModule } from '@angular/forms';
import { Page1DocumentFormComponent } from './page-1-document-form';

@Component({
  selector: 'app-page-1',
  imports: [ReactiveFormsModule, Page1DocumentFormComponent],
  templateUrl: './page-1.component.html',
  styleUrl: './page-1.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Page1Component {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<LabelIdentifier, string>>();

  pageKey = 'page-1';
}
