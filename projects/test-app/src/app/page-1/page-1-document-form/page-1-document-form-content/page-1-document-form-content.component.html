@if (shouldShow().has('test-field1')) {
<app-test-app-form-text-input
  [label]="labels()['test-field1']"
  fieldName="test-field1"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>} @if (shouldShow().has('test-field2-test')) {
<app-test-app-form-text-input
  [label]="labels()['test-field2-test']"
  fieldName="test-field2-test"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field3') || shouldShow().has('test-field4-flat')
|| shouldShow().has('test-field4-group')) {
<app-one-of-selector
  header="Select between field 3 and field 4"
  [option1Label]="labels()['test-field3']"
  [option2Label]="labels()['test-field4-flat']"
  [option1FieldNames]="['test-field3']"
  [option2FieldNames]="['test-field4-flat', 'test-field4-group']"
  [pageKey]="pageKey()"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    @if (shouldShow().has('test-field3')) {
    <app-test-app-form-text-input
      [label]="labels()['test-field3']"
      fieldName="test-field3"
      [isReadOnly]="isReadOnly()"
      [pageKey]="pageKey()"
    />
    }
  </ng-container>
  <ng-container slot="option2">
    @if (shouldShow().has('test-field4-flat')) {
    <app-test-app-form-text-input
      [label]="labels()['test-field4-flat']"
      fieldName="test-field4-flat"
      [isReadOnly]="isReadOnly()"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has('test-field4-group')) {
    <app-test-app-form-group-array
      [label]="labels()['test-field4-group']"
      fieldName="test-field4-group"
      [isReadOnly]="isReadOnly()"
      [groupFactory]="field4GroupFactory"
      [itemTemplate]="field4GroupTemplate"
      [pageKey]="pageKey()"
    />
    }
  </ng-container>
</app-one-of-selector>
} @if (shouldShow().has('test-field5')) {
<app-test-app-form-text-input
  [label]="labels()['test-field5']"
  fieldName="test-field5"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field6')) {
<app-test-app-form-text-input
  [label]="labels()['test-field6']"
  fieldName="test-field6"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field7')) {
<app-test-app-form-array-input
  [label]="labels()['test-field7']"
  fieldName="test-field7"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field8')) {
<app-test-app-form-text-input
  [label]="labels()['test-field8']"
  fieldName="test-field8"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field10')) {
<app-test-app-form-text-input
  [label]="labels()['test-field10']"
  fieldName="test-field10"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field11')) {
<app-test-app-form-group-array
  [label]="labels()['test-field11']"
  fieldName="test-field11"
  [isReadOnly]="isReadOnly()"
  [groupFactory]="field11GroupFactory"
  [itemTemplate]="field11Template"
  [pageKey]="pageKey()"
/>
}
<ng-template #field4GroupTemplate let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField1'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field4-group-nestedField1']"
      fieldName="nestedField1"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField2'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field4-group-nestedField2']"
      fieldName="nestedField2"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField3')) ||
    shouldShow().has(getFieldId(fieldPrefix, 'nestedField4'))) {
    <app-one-of-selector
      header="Select between nested field 3 and nested field 4"
      [option1Label]="labels()['test-field4-group-nestedField3']"
      [option2Label]="labels()['test-field4-group-nestedField4']"
      [option1FieldNames]="['nestedField3']"
      [option2FieldNames]="['nestedField4']"
      [pageKey]="pageKey()"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
    >
      <ng-container slot="option1">
        @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField3'))) {
        <app-test-app-form-text-input
          [label]="labels()['test-field4-group-nestedField3']"
          fieldName="nestedField3"
          [isReadOnly]="isReadOnly()"
          [fieldPrefix]="fieldPrefix"
          [pageKey]="pageKey()"
        />}</ng-container
      >
      <ng-container slot="option2"
        >@if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField4'))) {
        <app-test-app-form-text-input
          [label]="labels()['test-field4-group-nestedField4']"
          fieldName="nestedField4"
          [isReadOnly]="isReadOnly()"
          [fieldPrefix]="fieldPrefix"
          [pageKey]="pageKey()"
        />}</ng-container
      >
    </app-one-of-selector>
    }
  </div>
</ng-template>

<ng-template #field11Template let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField1'))) {
    <app-test-app-form-radio-button-group
      [label]="labels()['test-field11-nestedField1']"
      fieldName="nestedField1"
      [isReadOnly]="isReadOnly()"
      [options]="radioButtonGroupSelectOptions"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField2'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field11-nestedField2']"
      fieldName="nestedField2"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField3'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field11-nestedField3']"
      fieldName="nestedField3"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'nestedField4'))) {
    <app-test-app-form-group-array
      [label]="labels()['test-field11-nestedField4']"
      fieldName="nestedField4"
      [isReadOnly]="isReadOnly()"
      [groupFactory]="nestedField4GroupFactory"
      [itemTemplate]="nestedField4Template"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    }
  </div>
</ng-template>

<ng-template #nestedField4Template let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    @if (shouldShow().has(getFieldId(fieldPrefix, 'deepNestedField1'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field11-nestedField4-deepNestedField1']"
      fieldName="deepNestedField1"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    } @if (shouldShow().has(getFieldId(fieldPrefix, 'deepNestedField2'))) {
    <app-test-app-form-text-input
      [label]="labels()['test-field11-nestedField4-deepNestedField2']"
      fieldName="deepNestedField2"
      [isReadOnly]="isReadOnly()"
      [fieldPrefix]="fieldPrefix"
      [pageKey]="pageKey()"
    />
    }
  </div>
</ng-template>
