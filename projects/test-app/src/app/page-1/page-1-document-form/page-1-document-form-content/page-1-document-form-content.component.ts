import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import {
  OneOfSelectorComponent,
  TestAppFormArrayInputComponent,
  TestAppFormGroupArrayComponent,
  TestAppFormRadioButtonGroupComponent,
  TestAppFormTextInputComponent,
} from '../../../components';
import {
  createField11Group,
  createField4Group,
  createNestedField4Group,
  LabelIdentifier,
} from '../../../app.form';
import type { SelectOption } from '@helaba/iso20022-lib/types';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import {
  AdvancedModeService,
  FormContextProviderService,
} from '@helaba/iso20022-lib/services';
import { getFieldId } from '@helaba/iso20022-lib/util';

const radioButtonGroupOptions = ['Option1', 'Option2'] as const;
type RadioButtonGroupOption = (typeof radioButtonGroupOptions)[number];

@Component({
  selector: 'app-page-1-document-form-content',
  imports: [
    ReactiveFormsModule,
    TestAppFormTextInputComponent,
    OneOfSelectorComponent,
    TestAppFormGroupArrayComponent,
    TestAppFormArrayInputComponent,
    TestAppFormRadioButtonGroupComponent,
  ],
  templateUrl: './page-1-document-form-content.component.html',
  styleUrl: './page-1-document-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Page1DocumentFormContentComponent {
  fb = inject(NonNullableFormBuilder);
  formContextProvider = inject(FormContextProviderService);
  advancedModeService = inject(AdvancedModeService);

  getFieldId = getFieldId;

  isReadOnly = input.required<boolean>();
  labels = input.required<Record<LabelIdentifier, string>>();
  pageKey = input.required<string>();

  field4GroupFactory = () => createField4Group(this.fb);

  field11GroupFactory = () => createField11Group(this.fb);

  nestedField4GroupFactory = () => createNestedField4Group(this.fb);

  radioButtonGroupSelectOptions: SelectOption<RadioButtonGroupOption>[] =
    radioButtonGroupOptions.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));

  shouldShow = computed<Set<string>>(() => {
    const currentFormContext = this.formContextProvider.currentFormContext();
    const isReadOnly = this.isReadOnly();
    const isAdvancedMode = this.advancedModeService.isAdvancedMode();
    const advancedModeFields = this.advancedModeService.advancedModeFields();

    if (!currentFormContext) {
      return new Set<string>();
    }

    return currentFormContext.getFieldIdsToShow(
      isReadOnly,
      isAdvancedMode,
      advancedModeFields
    );
  });
}
