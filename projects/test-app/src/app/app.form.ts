import {
  FormArray,
  FormControl,
  FormGroup,
  NonNullableFormBuilder,
} from '@angular/forms';
import { XsdElement } from '@helaba/iso20022-lib/rules';

// NOTE: When changing the form schema, make sure to also update the 'SIMPLIFIED_SCHEMA' and 'LABELS' below
export function getPage1DocumentFormSchema(fb: NonNullableFormBuilder) {
  return {
    'test-field1': fb.control(''),
    'test-field2-test': fb.control(''),
    'test-field3': fb.control(''),
    'test-field4-flat': fb.control(''),
    'test-field4-group': fb.array<
      FormGroup<{
        nestedField1: FormControl<string>;
        nestedField2: FormControl<string>;
        nestedField3: FormControl<string>;
        nestedField4: FormControl<string>;
      }>
    >([]),
    'test-field5': fb.control(''),
    'test-field6': fb.control(''),
    'test-field7': fb.array<FormControl<string>>([]),
    'test-field8': fb.control(''),
    'test-field10': fb.control(''),
    'test-field11': fb.array<
      FormGroup<{
        nestedField1: FormControl<string>;
        nestedField2: FormControl<string>;
        nestedField3: FormControl<string>;
        nestedField4: FormArray<
          FormGroup<{
            deepNestedField1: FormControl<string>;
            deepNestedField2: FormControl<string>;
          }>
        >;
      }>
    >([]),
  };
}

export function getPage1BahFormSchema(fb: NonNullableFormBuilder) {
  return {};
}

export function getPage2DocumentFormSchema(fb: NonNullableFormBuilder) {
  return {
    'test-field9': fb.control(''),
    'test-field12': fb.control(''),
    'test-field13': fb.control('0'),
    'test-field14': fb.control('0'),
    'test-field15': fb.control(''),
  };
}

export function getPage2BahFormSchema(fb: NonNullableFormBuilder) {
  return {};
}

export function createField4Group(fb: NonNullableFormBuilder) {
  return fb.group({
    nestedField1: fb.control(''),
    nestedField2: fb.control(''),
    nestedField3: fb.control(''),
    nestedField4: fb.control(''),
  });
}

export function createField11Group(fb: NonNullableFormBuilder) {
  return fb.group({
    nestedField1: fb.control(''),
    nestedField2: fb.control(''),
    nestedField3: fb.control(''),
    nestedField4: fb.array<
      FormGroup<{
        deepNestedField1: FormControl<string>;
        deepNestedField2: FormControl<string>;
      }>
    >([]),
  });
}

export function createNestedField4Group(fb: NonNullableFormBuilder) {
  return fb.group({
    deepNestedField1: fb.control(''),
    deepNestedField2: fb.control(''),
  });
}

export const LABELS = {
  'test-field1': 'Field 1',
  'test-field2-test': 'Field 2',
  'test-field3': 'Field 3',
  'test-field4-flat': 'Field 4',
  'test-field4-group': 'Field 4 Group',
  'test-field5': 'Field 5',
  'test-field6': 'Field 6',
  'test-field7': 'Field 7',
  'test-field8': 'Field 8',
  'test-field9': 'Field 9',
  'test-field10': 'Field 10',
  'test-field11': 'Field 11',
  'test-field4-group-nestedField1': 'Nested Field 1',
  'test-field4-group-nestedField2': 'Nested Field 2',
  'test-field4-group-nestedField3': 'Nested Field 3',
  'test-field4-group-nestedField4': 'Nested Field 4',
  'test-field11-nestedField1': 'Nested Field 1',
  'test-field11-nestedField2': 'Nested Field 2',
  'test-field11-nestedField3': 'Nested Field 3',
  'test-field11-nestedField4': 'Nested Field 4',
  'test-field11-nestedField4-deepNestedField1': 'Deep Nested Field 1',
  'test-field11-nestedField4-deepNestedField2': 'Deep Nested Field 2',
  'test-field12': 'Field 12',
  'test-field13': 'Field 13',
  'test-field14': 'Field 14',
  'test-field15': 'Field 15',
};
export type LabelIdentifier = keyof typeof LABELS;

export const SIMPLIFIED_SCHEMA: XsdElement = {
  fullName: 'Test',
  abbrName: 'test',
  nestedAbbrName: 'test',
  isArray: false,
  children: [
    {
      fullName: 'Field 1',
      abbrName: 'field1',
      nestedAbbrName: 'test-field1',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 2',
      abbrName: 'field2',
      nestedAbbrName: 'test-field2',
      isArray: false,
      children: [
        {
          fullName: 'Test',
          abbrName: 'test',
          nestedAbbrName: 'test-field2-test',
          isArray: false,
          children: [],
        },
      ],
    },
    {
      fullName: 'Field 3',
      abbrName: 'field3',
      nestedAbbrName: 'test-field3',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 4',
      abbrName: 'field4',
      nestedAbbrName: 'test-field4',
      isArray: false,
      children: [
        {
          fullName: 'Flat',
          abbrName: 'flat',
          nestedAbbrName: 'test-field4-flat',
          isArray: false,
          children: [],
        },
        {
          fullName: 'Group',
          abbrName: 'group',
          nestedAbbrName: 'test-field4-group',
          isArray: true,
          children: [
            {
              fullName: 'Nested Field 1',
              abbrName: 'nestedField1',
              nestedAbbrName: 'test-field4-group-nestedField1',
              isArray: false,
              children: [],
            },
            {
              fullName: 'Nested Field 2',
              abbrName: 'nestedField2',
              nestedAbbrName: 'test-field4-group-nestedField2',
              isArray: false,
              children: [],
            },
            {
              fullName: 'Nested Field 3',
              abbrName: 'nestedField3',
              nestedAbbrName: 'test-field4-group-nestedField3',
              isArray: false,
              children: [],
            },
            {
              fullName: 'Nested Field 4',
              abbrName: 'nestedField4',
              nestedAbbrName: 'test-field4-group-nestedField4',
              isArray: false,
              children: [],
            },
          ],
        },
      ],
    },
    {
      fullName: 'Field 5',
      abbrName: 'field5',
      nestedAbbrName: 'test-field5',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 6',
      abbrName: 'field6',
      nestedAbbrName: 'test-field6',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 7',
      abbrName: 'field7',
      nestedAbbrName: 'test-field7',
      isArray: true,
      children: [],
    },
    {
      fullName: 'Field 8',
      abbrName: 'field8',
      nestedAbbrName: 'test-field8',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 9',
      abbrName: 'field9',
      nestedAbbrName: 'test-field9',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 10',
      abbrName: 'field10',
      nestedAbbrName: 'test-field10',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 11',
      abbrName: 'field11',
      nestedAbbrName: 'test-field11',
      isArray: true,
      children: [
        {
          fullName: 'Nested Field 1',
          abbrName: 'nestedField1',
          nestedAbbrName: 'test-field11-nestedField1',
          isArray: false,
          children: [],
        },
        {
          fullName: 'Nested Field 2',
          abbrName: 'nestedField2',
          nestedAbbrName: 'test-field11-nestedField2',
          isArray: false,
          children: [],
        },
        {
          fullName: 'Nested Field 3',
          abbrName: 'nestedField3',
          nestedAbbrName: 'test-field11-nestedField3',
          isArray: false,
          children: [],
        },
        {
          fullName: 'Nested Field 4',
          abbrName: 'nestedField4',
          nestedAbbrName: 'test-field11-nestedField4',
          isArray: true,
          children: [
            {
              fullName: 'Deep Nested Field 1',
              abbrName: 'deepNestedField1',
              nestedAbbrName: 'test-field11-nestedField4-deepNestedField1',
              isArray: false,
              children: [],
            },
            {
              fullName: 'Deep Nested Field 2',
              abbrName: 'deepNestedField2',
              nestedAbbrName: 'test-field11-nestedField4-deepNestedField2',
              isArray: false,
              children: [],
            },
          ],
        },
      ],
    },
    {
      fullName: 'Field 12',
      abbrName: 'field12',
      nestedAbbrName: 'test-field12',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 13',
      abbrName: 'field13',
      nestedAbbrName: 'test-field13',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 14',
      abbrName: 'field14',
      nestedAbbrName: 'test-field14',
      isArray: false,
      children: [],
    },
    {
      fullName: 'Field 15',
      abbrName: 'field15',
      nestedAbbrName: 'test-field15',
      isArray: false,
      children: [],
    },
  ],
};

export type AppPage1DocumentFormSchema = ReturnType<
  typeof getPage1DocumentFormSchema
>;
export type AppPage1BahFormSchema = ReturnType<typeof getPage1BahFormSchema>;
export type AppPage2DocumentFormSchema = ReturnType<
  typeof getPage2DocumentFormSchema
>;
export type AppPage2BahFormSchema = ReturnType<typeof getPage2BahFormSchema>;

export function getPage1DocumentFormGroup(fb: NonNullableFormBuilder) {
  const schema = getPage1DocumentFormSchema(fb);
  return fb.group(schema);
}
export function getPage1BahFormGroup(fb: NonNullableFormBuilder) {
  const schema = getPage1BahFormSchema(fb);
  return fb.group(schema);
}
export function getPage2DocumentFormGroup(fb: NonNullableFormBuilder) {
  const schema = getPage2DocumentFormSchema(fb);
  return fb.group(schema);
}
export function getPage2BahFormGroup(fb: NonNullableFormBuilder) {
  const schema = getPage2BahFormSchema(fb);
  return fb.group(schema);
}
