import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { LabelIdentifier } from '../app.form';
import { ReactiveFormsModule } from '@angular/forms';
import { Page2DocumentFormComponent } from './page-2-document-form';

@Component({
  selector: 'app-page-2',
  imports: [ReactiveFormsModule, Page2DocumentFormComponent],
  templateUrl: './page-2.component.html',
  styleUrl: './page-2.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Page2Component {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<LabelIdentifier, string>>();

  pageKey = 'page-2';
}
