import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import {
  TestAppFormTextInputComponent,
  TestAppFormNumberInputComponent,
} from '../../../components';
import { LabelIdentifier } from '../../../app.form';
import { ReactiveFormsModule } from '@angular/forms';
import { TestAppFormDateInputComponent } from '../../../components/test-app-form-date-input';
import {
  AdvancedModeService,
  FormContextProviderService,
} from '@helaba/iso20022-lib/services';

@Component({
  selector: 'app-page-2-document-form-content',
  imports: [
    ReactiveFormsModule,
    TestAppFormTextInputComponent,
    TestAppFormNumberInputComponent,
    TestAppFormDateInputComponent,
  ],
  templateUrl: './page-2-document-form-content.component.html',
  styleUrl: './page-2-document-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Page2DocumentFormContentComponent {
  formContextProvider = inject(FormContextProviderService);
  advancedModeService = inject(AdvancedModeService);

  isReadOnly = input.required<boolean>();
  labels = input.required<Record<LabelIdentifier, string>>();
  pageKey = input.required<string>();

  shouldShow = computed<Set<string>>(() => {
    const currentFormContext = this.formContextProvider.currentFormContext();
    const isReadOnly = this.isReadOnly();
    const isAdvancedMode = this.advancedModeService.isAdvancedMode();
    const advancedModeFields = this.advancedModeService.advancedModeFields();

    if (!currentFormContext) {
      return new Set<string>();
    }

    return currentFormContext.getFieldIdsToShow(
      isReadOnly,
      isAdvancedMode,
      advancedModeFields
    );
  });
}
