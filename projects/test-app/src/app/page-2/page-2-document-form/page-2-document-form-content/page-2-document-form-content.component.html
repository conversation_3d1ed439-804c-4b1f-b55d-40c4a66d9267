@if (shouldShow().has('test-field9')) {
<app-test-app-form-text-input
  [label]="labels()['test-field9']"
  fieldName="test-field9"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field12')) {
<app-test-app-form-number-input
  [label]="labels()['test-field12']"
  fieldName="test-field12"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field13')) {
<app-test-app-form-number-input
  [label]="labels()['test-field13']"
  fieldName="test-field13"
  [isReadOnly]="isReadOnly()"
  [maxFractionDigits]="2"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field14')) {
<app-test-app-form-number-input
  [label]="labels()['test-field14']"
  fieldName="test-field14"
  [isReadOnly]="isReadOnly()"
  [maxFractionDigits]="2"
  [pageKey]="pageKey()"
/>
} @if (shouldShow().has('test-field15')) {
<app-test-app-form-date-input
  [label]="labels()['test-field15']"
  fieldName="test-field15"
  [isReadOnly]="isReadOnly()"
  [pageKey]="pageKey()"
/>
}
