import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
} from '@angular/core';
import { LabelIdentifier } from '../../app.form';
import { ReactiveFormsModule } from '@angular/forms';
import { Page2DocumentFormContentComponent } from './page-2-document-form-content';
import {
  FormContextProviderService,
  FormDataService,
} from '@helaba/iso20022-lib/services';

@Component({
  selector: 'app-page-2-document-form',
  imports: [ReactiveFormsModule, Page2DocumentFormContentComponent],
  templateUrl: './page-2-document-form.component.html',
  styleUrl: './page-2-document-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FormContextProviderService],
})
export class Page2DocumentFormComponent {
  formDataService = inject(FormDataService);
  formContextProviderService = inject(FormContextProviderService);

  isReadOnly = input.required<boolean>();
  labels = input.required<Record<LabelIdentifier, string>>();
  pageKey = input.required<string>();

  allFormGroups = this.formDataService.allFormGroups;
  allFormContexts = this.formDataService.allFormContexts;

  formGroup = computed(() => {
    const allFormGroups = this.allFormGroups();
    return allFormGroups[this.pageKey()].documentFormGroup;
  });

  formContext = computed(() => {
    const allFormContexts = this.allFormContexts();
    return allFormContexts[this.pageKey()].documentContext;
  });

  constructor() {
    effect(() => {
      const formContext = this.formContext();
      this.formContextProviderService.setCurrentFormContext(formContext);
    });
  }
}
