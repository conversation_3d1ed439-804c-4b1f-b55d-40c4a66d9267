import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  // Run tests in parallel for speed
  fullyParallel: true,
  // Expect the dev server on 4201 as per package.json
  use: {
    baseURL: 'http://localhost:4201',
  },
  // Automatically start the dev server for the test-app
  webServer: {
    command: 'npm run start',
    url: 'http://localhost:4201',
    timeout: 120 * 1000,
    reuseExistingServer: true,
  },
  // Keep it minimal: only run on Chromium by default
  projects: [
    { name: 'e2e', use: { ...devices['Desktop Chrome'], headless: true } },
    {
      name: 'e2e:headed',
      use: {
        ...devices['Desktop Chrome'],
        headless: false,
        launchOptions: {
          slowMo: 500,
        },
      },
    },
  ],
  outputDir: './tests/results',
});
