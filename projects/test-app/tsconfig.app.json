{
  "extends": "../../tsconfig.json",
  "files": ["src/main.ts"],
  "include": ["src/**/*.ts", "src/**/*.d.ts"],
  "compilerOptions": {
    // There is some issue with resolving paths from the symlinked iso20022-lib package in the root node_modules, not sure if that is only TypeScript or something in Angular's dev server build process so we link to the lib directly.
    "paths": {
      "@helaba/iso20022-lib/*": ["../iso20022-lib/*"]
    }
  }
}
