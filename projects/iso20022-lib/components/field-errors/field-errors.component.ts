import {
  ChangeDetectionStrategy,
  Component,
  computed,
  contentChild,
  inject,
  input,
  Signal,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { AbstractControl, FormGroup } from '@angular/forms';
import {
  FormContextProviderService,
  FormErrorsService,
} from '@helaba/iso20022-lib/services';
import { getFieldIdsFromFormGroup } from '@helaba/iso20022-lib/angular-utils';

@Component({
  selector: 'app-field-errors',
  imports: [NgTemplateOutlet],
  templateUrl: './field-errors.component.html',
  styleUrl: './field-errors.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldErrorsComponent {
  fieldId = input.required<string>();
  pageKey = input.required<string>();

  formContextProvider = inject(FormContextProviderService);
  formErrorsService = inject(FormErrorsService);

  formGroup = computed<FormGroup>(() => {
    const fg = this.formContextProvider.currentFormContext()?.formGroup();
    if (!fg) {
      throw new Error('FormGroup is not set in FormContextProviderService');
    }
    return fg;
  });

  control = computed<AbstractControl>(() => {
    const formGroup = this.formGroup();
    const formControl = formGroup.get(this.fieldId());
    if (!formControl) {
      throw new Error(
        `FieldErrorsComponent: No control found for fieldId "${this.fieldId()}" in form group with field ids ${getFieldIdsFromFormGroup(
          formGroup
        )}. Ensure that the fieldId matches a control in the form group.`
      );
    }

    return formControl;
  });

  errorTemplate = contentChild<TemplateRef<{ $implicit: string }>>(TemplateRef);

  errorMessages: Signal<string[]> = computed(() => {
    const pageFieldErrors =
      this.formErrorsService.fieldErrors()[this.pageKey()];
    const fieldId = this.fieldId();
    const fieldErrors = pageFieldErrors?.document.has(fieldId)
      ? pageFieldErrors.document.get(fieldId)
      : pageFieldErrors?.bah.has(this.fieldId())
      ? pageFieldErrors.bah.get(this.fieldId())
      : undefined;

    const ruleIds = fieldErrors ? Object.keys(fieldErrors) : [];
    // We use the Set to remove duplicates. This can happen e.g. if field names are concatenated in the description.
    return [
      ...new Set(
        ruleIds.map(
          (ruleId: string) =>
            this.formErrorsService.errorMessages().get(ruleId) || ruleId
        )
      ),
    ];
  });
}
