<app-base-field-structure
  [label]="label()"
  [fieldName]="fieldName()"
  [fieldId]="fieldId()"
  [errorTemplate]="formGroupArrayErrorTemplate"
  [useLabel]="false"
>
  <div class="formArrayContainer">
    @if (isReadOnly()) {
    <app-form-value
      [fieldId]="fieldId()"
      [fieldTemplate]="formValueFieldTemplate()"
    />
    } @else {
    <div [formArrayName]="fieldName()">
      @for (control of controls(); let index = $index; track control;) {
      <div [formGroupName]="index" class="formArrayItem">
        <div class="formArrayItemHeader">
          <h4>Item {{ index + 1 }}</h4>
          <ng-container
            *ngTemplateOutlet="
              removeItemButtonTemplate();
              context: { $implicit: removeItem, index: index }
            "
          />
        </div>
        <div>
          <!-- Custom template content -->
          <ng-container
            *ngTemplateOutlet="
            itemTemplate();
            context: {
              $implicit: control,
              fieldPrefix: `${fieldPrefix()}${fieldPrefix() ? '.' : ''}${fieldName()}.${index}`
            }
          "
          />
        </div>
      </div>
      }
    </div>
    <ng-container
      *ngTemplateOutlet="
        addItemButtonTemplate();
        context: { $implicit: addItem }
      "
    />

    }
  </div>
</app-base-field-structure>

<ng-template #formGroupArrayErrorTemplate let-fieldId="fieldId">
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: {
        $implicit: fieldId,
      }
    "
  />
</ng-template>
