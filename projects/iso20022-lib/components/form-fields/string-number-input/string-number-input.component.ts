import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  forwardRef,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-string-number-input',
  imports: [ReactiveFormsModule, FormsModule, NgTemplateOutlet],
  templateUrl: './string-number-input.component.html',
  styleUrl: './string-number-input.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => StringNumberInputComponent),
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StringNumberInputComponent implements ControlValueAccessor {
  numberInputTemplate = input.required<TemplateRef<unknown>>();
  fieldId = input.required<string>();
  maxFractionDigits = input<number>(0);

  cdr = inject(ChangeDetectorRef);

  // Internal state
  numericValue: number | null = null;
  private stringValue = '';

  // Arrow function to preserve 'this' context
  onInputImplementationInput = ($event: unknown) => {
    if (typeof $event !== 'object' || $event === null || !('value' in $event)) {
      throw new Error('Unexpected event type from number input implementation');
    }
    const newValue = $event.value;

    if (newValue === null || newValue === undefined || newValue === '') {
      // User cleared the input
      this.updateFormWithString('');
      return;
    }

    if (typeof newValue === 'number') {
      // Convert the number back to string
      const newValueString = newValue.toFixed(this.maxFractionDigits());
      this.updateFormWithString(newValueString);
      return;
    }

    if (typeof newValue === 'string') {
      this.updateFormWithString(newValue);
      return;
    }

    throw new Error('Number input implementation should return number values');
  };

  private updateFormWithString(value: string) {
    if (this.stringValue !== value) {
      this.stringValue = value;
      this.onChange(value);
    }
  }

  // ControlValueAccessor methods
  onChange = (value: string) => {};
  onTouched = () => {};

  // Called when the form wants to update the value, e.g. for setting the initial value
  writeValue(value: string | null): void {
    this.stringValue = value ?? '0';

    // Convert string to number for display in the input implementation
    const num = parseFloat(this.stringValue);
    this.numericValue = isNaN(num) ? 0 : num;

    // Required for when 'writeValue' is called because of a 'patchValue' as Angular does not trigger change detection in that case as it would for a full 'reset'.
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
}
