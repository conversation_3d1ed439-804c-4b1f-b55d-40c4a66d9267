import {
  Component,
  computed,
  inject,
  InputSignal,
  TemplateRef,
} from '@angular/core';
import { FormErrorsService } from '@helaba/iso20022-lib/services';
import { getFieldId } from '@helaba/iso20022-lib/util';

@Component({
  template: '',
})
export abstract class BaseFieldComponent {
  abstract readonly label: InputSignal<string>;
  abstract readonly fieldName: InputSignal<string>; // Identifier for the field in the current form group, never contains dots or array indices.
  abstract readonly isReadOnly: InputSignal<boolean>;
  abstract readonly fieldPrefix: InputSignal<string>; // Contains the upstream form structure including dots and array indices excluding the current field name. Empty by default, only used in form group arrays.
  abstract readonly errorTemplate: InputSignal<TemplateRef<unknown>>;
  abstract readonly formValueFieldTemplate: InputSignal<TemplateRef<unknown>>;

  protected readonly formErrorsService = inject(FormErrorsService);

  // Identifier for the field that is unique within the parent form group.
  protected fieldId = computed<string>(() => {
    const fieldPrefix = this.fieldPrefix();
    const fieldName = this.fieldName();
    return getFieldId(fieldPrefix, fieldName);
  });

  protected hasError = computed<boolean>(() => {
    if (this.isReadOnly()) {
      return false;
    }

    const erroneousScopes = this.formErrorsService.erroneousScopes();

    if (erroneousScopes.has(this.fieldId())) {
      return true;
    }
    return false;
  });
}
