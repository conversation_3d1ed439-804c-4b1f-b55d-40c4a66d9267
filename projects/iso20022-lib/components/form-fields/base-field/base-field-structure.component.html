<div class="formField">
  @if (useLabel()) {
  <div class="formFieldWithLabel">
    <label [for]="fieldId()" class="formFieldLabel">{{ label() }}</label>
    <ng-container *ngTemplateOutlet="contentTemplate" />
  </div>
  } @else {
  <fieldset class="formFieldFieldset">
    <legend class="formFieldLabel">{{ label() }}</legend>
    <ng-container *ngTemplateOutlet="contentTemplate" />
  </fieldset>
  }
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: { $implicit: fieldName(), fieldId: fieldId() }
    "
  />
</div>

<ng-template #contentTemplate>
  <ng-content />
</ng-template>
