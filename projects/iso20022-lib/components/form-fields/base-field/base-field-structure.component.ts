import { NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  input,
  TemplateRef,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-base-field-structure',
  imports: [ReactiveFormsModule, NgTemplateOutlet],
  templateUrl: './base-field-structure.component.html',
  styleUrl: './base-field-structure.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseFieldStructureComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  fieldId = input.required<string>();
  errorTemplate = input.required<TemplateRef<unknown>>();
  useLabel = input.required<boolean>(); // Set to 'false' for cases where the input is actually comprised of multiple inputs, e.g. a radio button group has multiple inputs but only one field name which cannot be used in the "for" attribute of the label as it would refer to none of the inputs.
}
