import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  forwardRef,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { NgTemplateOutlet } from '@angular/common';
import {
  isValidDate,
  formatDateToIsoOffset,
  isCompleteDateString,
  skipCircularStringify,
  parseIsoDateWithOffset,
} from '@helaba/iso20022-lib/util';
import { DatePattern } from '@helaba/iso20022-lib/types';

@Component({
  selector: 'app-iso-date-input',
  imports: [ReactiveFormsModule, FormsModule, NgTemplateOutlet],
  templateUrl: './iso-date-input.component.html',
  styleUrl: './iso-date-input.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ISODateInputComponent),
      multi: true,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ISODateInputComponent implements ControlValueAccessor {
  dateInputTemplate = input.required<TemplateRef<unknown>>();
  fieldId = input.required<string>();
  showTime = input<boolean>(false);
  timeOnly = input<boolean>(false);
  dateTimePattern = input.required<DatePattern>();

  cdr = inject(ChangeDetectorRef);

  // Internal state
  dateValue: Date | null = null;
  private stringValue = '';

  // Arrow function to preserve 'this' context
  // We allow various inputs here, e.g. the Prime NG calender outputs a Date object when the user selects a date via the picker but outputs an InputEvent when the user types in a date manually
  onInputImplementationInput = (newValueOrEvent: unknown) => {
    if (
      newValueOrEvent === null ||
      newValueOrEvent === undefined ||
      newValueOrEvent === ''
    ) {
      // User cleared the input
      this.updateFormWithString('');
      return;
    }

    if (isValidDate(newValueOrEvent)) {
      const newValueString = formatDateToIsoOffset(
        newValueOrEvent,
        this.showTime(),
        this.timeOnly()
      );
      this.updateFormWithString(newValueString);
      return;
    }

    // 'newValueOrEvent' is propably an event object from manual input, e.g. 'InputEvent' or 'BlurEvent'. We use a timeout to allow the input implementation to update its internal state before we read the value from the event target. E.g. Prime NG fills up missing digits in the input field on blur, so we need to wait for that to happen before we read the value.
    setTimeout(() => {
      if (
        typeof newValueOrEvent === 'object' &&
        'target' in newValueOrEvent &&
        typeof newValueOrEvent.target === 'object' &&
        newValueOrEvent.target !== null &&
        'value' in newValueOrEvent.target &&
        typeof newValueOrEvent.target.value === 'string'
      ) {
        const newValue = newValueOrEvent.target.value;

        // User typed in a value manually. If it is complete (matches the pattern defined for the display in the date input implementation), we convert it to a Date object and derive the ISO string from that to store in the form group. Otherwise, we just store the typed in string.
        if (isCompleteDateString(newValue, this.dateTimePattern())) {
          const date = new Date(newValue);
          // Throws an error if the date is invalid which means that something is wrong with the way the date input component allows users to enter dates. The entered date cannot be parsed to a 'Date' object.
          const newValueString = formatDateToIsoOffset(
            date,
            this.showTime(),
            this.timeOnly()
          );
          this.updateFormWithString(newValueString);
        } else {
          this.updateFormWithString(newValue);
        }

        return;
      }

      throw new Error(
        `Unexpected output from date input implementation: ${skipCircularStringify(
          newValueOrEvent
        )}`
      );
    }, 100);
  };

  private updateFormWithString(value: string) {
    if (this.stringValue !== value) {
      this.stringValue = value;
      this.onChange(value);
    }
  }

  // ControlValueAccessor methods
  onChange = (value: string) => {};
  onTouched = () => {};

  // Called when the form wants to update the value, e.g. for setting the initial value
  writeValue(value: string | null): void {
    this.stringValue = value ?? '';

    // Convert the string to a Date object for the input implementation
    const date = value ? parseIsoDateWithOffset(value) : null;
    this.dateValue = isValidDate(date) ? date : null;

    // Required for when 'writeValue' is called because of a 'patchValue' as Angular does not trigger change detection in that case as it would for a full 'reset'.
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
}
