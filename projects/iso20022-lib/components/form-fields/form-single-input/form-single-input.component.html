<app-base-field-structure
  [label]="label()"
  [fieldName]="fieldName()"
  [fieldId]="fieldId()"
  [errorTemplate]="formSingleInputErrorTemplate"
  [useLabel]="true"
>
  @if (isReadOnly()) {
  <app-form-value
    [fieldId]="fieldId()"
    [fieldTemplate]="formValueFieldTemplate()"
  />
  } @else {<ng-container
    *ngTemplateOutlet="
      fieldTemplate();
      context: {
        $implicit: fieldId(),
        hasError: hasError()
      }
    "
  />}
</app-base-field-structure>

<ng-template #formSingleInputErrorTemplate let-fieldId="fieldId">
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: {
        $implicit: fieldId,
      }
    "
  />
</ng-template>
