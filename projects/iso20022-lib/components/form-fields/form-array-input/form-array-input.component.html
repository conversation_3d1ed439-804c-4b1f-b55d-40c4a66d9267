<app-base-field-structure
  [label]="label()"
  [fieldName]="fieldName()"
  [fieldId]="fieldId()"
  [errorTemplate]="formArrayInputErrorTemplate"
  [useLabel]="false"
>
  @if (isReadOnly()) {
  <app-form-value
    [fieldId]="fieldId()"
    [fieldTemplate]="formValueFieldTemplate()"
  />
  } @else {
  <div class="formArrayItemsAndButton">
    <div [formArrayName]="fieldName()" class="formArrayItems">
      <!-- Possible performance improvement: Introduce a unique id per control and track that instead of the entire control -->
      @for (control of controls(); let index = $index; track control;) {
      <div class="formArrayItem">
        <div class="formArrayItemInputContainer">
          <ng-container
            *ngTemplateOutlet="fieldTemplate(); context: { $implicit: `${fieldId()}.${index}`, index: index, formGroup: control.parent}"
          />
        </div>
        <ng-container
          *ngTemplateOutlet="
            removeItemButtonTemplate();
            context: { $implicit: removeItem, index: index }
          "
        />
      </div>
      }
    </div>
    <ng-container
      *ngTemplateOutlet="
        addItemButtonTemplate();
        context: { $implicit: addItem }
      "
    />
  </div>
  }
</app-base-field-structure>

<ng-template #formArrayInputErrorTemplate let-fieldId="fieldId">
  <ng-container
    *ngTemplateOutlet="
      errorTemplate();
      context: {
        $implicit: fieldId,
      }
    "
  />
</ng-template>
