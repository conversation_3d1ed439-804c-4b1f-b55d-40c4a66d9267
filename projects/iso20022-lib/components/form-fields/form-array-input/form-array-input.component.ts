import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  TemplateRef,
  Type,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  ControlContainer,
  FormArray,
  FormGroup,
  FormGroupDirective,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent, BaseFieldStructureComponent } from '../base-field';
import { FormValueComponent } from '../form-value';
import { isFormArray } from '@helaba/iso20022-lib/angular-utils';
import { FormContextProviderService } from '@helaba/iso20022-lib/services';

@Component({
  selector: 'app-form-array-input',
  imports: [
    ReactiveFormsModule,
    BaseFieldStructureComponent,
    NgTemplateOutlet,
    FormValueComponent,
  ],
  templateUrl: './form-array-input.component.html',
  styleUrl: './form-array-input.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormArrayInputComponent extends BaseFieldComponent {
  override label = input.required<string>();
  override fieldName = input.required<string>();
  override isReadOnly = input.required<boolean>();
  override fieldPrefix = input.required<string>();
  override errorTemplate = input.required<TemplateRef<unknown>>();
  override formValueFieldTemplate = input.required<TemplateRef<unknown>>();

  fieldTemplate = input.required<TemplateRef<unknown>>();
  addItemButtonTemplate = input.required<TemplateRef<unknown>>();
  removeItemButtonTemplate = input.required<TemplateRef<unknown>>();

  fb = inject(NonNullableFormBuilder);
  formContextProvider = inject(FormContextProviderService);

  formGroup = computed<FormGroup>(() => {
    const fg = this.formContextProvider.currentFormContext()?.formGroup();
    if (!fg) {
      throw new Error('FormGroup is not set in FormContextProviderService');
    }
    return fg;
  });

  controls = computed(() => {
    const field = this.formGroup().get(this.fieldId());
    if (!(field && field instanceof FormArray)) {
      throw new Error(
        `FormArrayInputComponent: Expected a FormArray for field "${this.fieldName()}", but got ${
          field ? field.constructor.name : 'undefined'
        }.`
      );
    }

    return field.controls;
  });

  // Arrow function to preserve 'this' context
  addItem = () => {
    const fieldArray = this.formGroup()?.get(this.fieldName());
    if (!(fieldArray && fieldArray instanceof FormArray)) {
      throw new Error(
        `FormArrayInputComponent: Expected a FormArray for field "${this.fieldName()}", but got ${
          fieldArray ? fieldArray.constructor.name : 'undefined'
        }.`
      );
    }
    fieldArray.push(this.fb.control(''));
  };

  removeItem = (index: number) => {
    const fieldArray = this.formGroup()?.get(this.fieldName());
    if (!(fieldArray && isFormArray(fieldArray))) {
      throw new Error(
        `FormArrayInputComponent: Expected a FormArray for field "${this.fieldName()}", but got ${
          fieldArray ? fieldArray.constructor.name : 'undefined'
        }.`
      );
    }
    fieldArray.removeAt(index);
  };
}
