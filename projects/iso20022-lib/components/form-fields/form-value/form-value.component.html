@if (leafControl(); as leafControl) { @if (addLabels()) {
<label [for]="fieldId()" class="formLabel">
  {{ labelForCurrentField() }}
</label>
}
<ng-container
  *ngTemplateOutlet="
    fieldTemplate();
    context: { $implicit: fieldId(), formControl: leafControl }
  "
/>
} @if (formGroupControls(); as formGroupControls) {
<div class="formGroupContainer">
  @for (key of Object.keys(formGroupControls); track key) {
  <div class="formGroupItem">
    <app-form-value
      [fieldId]="`${fieldId()}.${key}`"
      [addLabels]="addLabels()"
      [fieldTemplate]="fieldTemplate()"
      [level]="level() + 1"
      [attr.data-level]="level() + 1"
    />
  </div>
  }
</div>
} @if (formArray(); as formArray) { @if (addLabels()) {
<label [for]="fieldId()" class="formLabel">
  {{ labelForCurrentField() }}
</label>
}
<div class="formArrayContainer">
  @if (formArray.controls.length === 0) {
  <div class="emptyArray">TODO: Inject from client (No items)</div>
  } @else if (formArray.controls.length === 1) {
  <div class="arrayCount">TODO: Inject from client (1 item)</div>
  } @else {
  <div class="arrayCount">
    {{ formArray.controls.length }} TODO: Inject from client (items)
  </div>
  }
  <div class="formArrayItems">
    @for (control of formArray.controls; let index = $index; track control) {
    <div class="formArrayItem" [attr.data-index]="index">
      <div class="formArrayItemHeader">
        <span class="formArrayItemIndex"
          >TODO: Inject from client (Item) {{ index + 1 }}</span
        >
      </div>
      <app-form-value
        [fieldId]="`${fieldId()}.${index}`"
        [addLabels]="!isPrimitiveFormArray()"
        [fieldTemplate]="fieldTemplate()"
        [level]="level() + 1"
        [attr.data-level]="level() + 1"
      />
    </div>
    }
  </div>
</div>
}
