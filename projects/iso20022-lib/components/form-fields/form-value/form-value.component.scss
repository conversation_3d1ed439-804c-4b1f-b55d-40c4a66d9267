.formLabel {
  display: block;
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;

  // Reduce label size for deeper levels
  :host([data-level="2"]) &,
  :host([data-level="3"]) &,
  :host([data-level="4"]) & {
    font-size: 0.8125rem;
  }
}

.formGroupContainer {
  // Lighter background for nested groups
  :host([data-level="1"]) & {
    background: var(--p-stone-25);
  }

  :host([data-level="2"]) &,
  :host([data-level="3"]) &,
  :host([data-level="4"]) & {
    background: white;
    padding: 0.5rem;
  }
}

.formGroupItem {
  &:not(:last-child) {
    margin-bottom: 1rem;

    // Tighter spacing for nested items
    :host([data-level="2"]) &,
    :host([data-level="3"]) &,
    :host([data-level="4"]) & {
      margin-bottom: 0.75rem;
    }
  }
}

.formArrayContainer {
  background: var(--p-panel-background);
  border: 1px solid var(--p-panel-border-color);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.5rem 0;

  :host([data-level="1"]) & {
    background: var(--p-stone-25);
  }

  :host([data-level="2"]) &,
  :host([data-level="3"]) &,
  :host([data-level="4"]) & {
    background: var(--p-stone-50);
    padding: 0.5rem;
  }
}

.arrayCount {
  color: var(--p-stone-500);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}

.emptyArray {
  font-style: italic;
  color: var(--p-stone-500);
  text-align: center;
  padding: 1.5rem;
  background: var(--p-stone-25);
  border-radius: 0.5rem;
  border: 1px dashed var(--p-stone-200);
}

.formArrayItems {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  // Tighter spacing for nested arrays
  :host([data-level="2"]) &,
  :host([data-level="3"]) &,
  :host([data-level="4"]) & {
    gap: 0.75rem;
  }
}

.formArrayItem {
  border: 1px solid var(--p-stone-200);
  padding: 0.5rem;
  border-radius: 0.25rem;

  // Different accent colors for different levels
  :host([data-level="0"]) &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--p-blue-400);
  }

  :host([data-level="1"]) &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--p-green-400);
  }

  :host([data-level="2"]) &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--p-purple-400);
  }

  position: relative;

  &Header {
    margin-bottom: 0.5rem;

    :host([data-level="2"]) &,
    :host([data-level="3"]) &,
    :host([data-level="4"]) & {
      padding: 0.375rem 0.5rem;
    }
  }

  &Index {
    font-size: 0.8125rem;
    font-weight: 600;
    color: var(--p-stone-500);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
}
