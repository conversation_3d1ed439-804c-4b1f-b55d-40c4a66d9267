import {
  ChangeDetectionStrategy,
  Component,
  Signal,
  computed,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  AbstractControl,
  ControlContainer,
  FormArray,
  FormControl,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { ISO_LIB_LABELS, Labels } from '@helaba/iso20022-lib/config';
import {
  isFormArray,
  isFormGroup,
  isLeafControl,
} from '@helaba/iso20022-lib/angular-utils';
import { FormContextProviderService } from '../../../services';
import {
  flattenFieldId,
  skipCircularStringify,
} from '@helaba/iso20022-lib/util';
import { AdvancedModeService } from '@helaba/iso20022-lib/services';

@Component({
  selector: 'app-form-value',
  imports: [ReactiveFormsModule, NgTemplateOutlet],
  templateUrl: './form-value.component.html',
  styleUrl: './form-value.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormValueComponent {
  fieldId = input.required<string>();
  fieldTemplate = input.required<TemplateRef<unknown>>();
  addLabels = input<boolean>(false); // Required when using this component to show array values. For basic values, the label comes from the BaseFieldStructure.
  level = input<number>(0); // For indentation/styling purposes

  formContextProvider = inject(FormContextProviderService);
  advancedModeService = inject(AdvancedModeService);

  // Inject effective labels signal from the library config
  labels: Signal<Labels> = inject(ISO_LIB_LABELS);

  // Expose 'Object' for template usage
  Object = Object;

  parentFormGroup = computed<FormGroup>(() => {
    const fg = this.formContextProvider.currentFormContext()?.formGroup();
    if (!fg) {
      throw new Error('FormGroup is not set in FormContextProviderService');
    }

    return fg;
  });

  labelForCurrentField = computed(() => {
    const fieldId = this.fieldId();
    const labelIdentifier = flattenFieldId(fieldId);

    return this.labels()[labelIdentifier];
  });

  fieldControl = computed<AbstractControl<any, any>>(() => {
    const parentFormGroup = this.parentFormGroup();

    const control = parentFormGroup.get(this.fieldId());
    if (!control) {
      throw new Error(`Control not found for fieldId: ${this.fieldId()}`);
    }
    const controlIsLeafControl = isLeafControl(control);
    const controlIsFormGroup = isFormGroup(control);
    const controlIsFormArray = isFormArray(control);
    if (
      [controlIsLeafControl, controlIsFormGroup, controlIsFormArray].filter(
        Boolean
      ).length !== 1
    ) {
      throw new Error(
        `Expected a FormControl, FormGroup, or FormArray for fieldId ${this.fieldId()}`
      );
    }

    return control;
  });

  // Narrow to a leaf FormControl if applicable
  leafControl = computed<FormControl<string | null | undefined> | null>(() => {
    const control = this.fieldControl();
    if (isLeafControl(control)) {
      return control as FormControl<string | null | undefined>;
    }
    return null;
  });

  // Retrieve the formGroup controls if applicable
  formGroupControls = computed<Record<string, AbstractControl> | null>(() => {
    const control = this.fieldControl();
    const shouldShow = this.shouldShow();

    if (isFormGroup(control)) {
      const controls = control.controls;

      // Filter to only those controls that should be shown
      return Object.fromEntries(
        Object.entries(controls).filter(([key, _]) =>
          shouldShow.has(`${this.fieldId()}.${key}`)
        )
      );
    }

    return null;
  });

  // Narrow to a FormArray if applicable
  formArray = computed<FormArray | null>(() => {
    const control = this.fieldControl();
    if (isFormArray(control)) {
      return control as FormArray;
    }
    return null;
  });

  isPrimitiveFormArray = computed<boolean>(() => {
    const formArray = this.formArray();
    if (!formArray) {
      return false;
    }
    // Check if all controls in the array are leaf controls
    return formArray.controls.every((control) => isLeafControl(control));
  });

  shouldShow = computed<Set<string>>(() => {
    const currentFormContext = this.formContextProvider.currentFormContext();
    const isReadOnly = true; // This component is only shown in read-only mode
    const isAdvancedMode = this.advancedModeService.isAdvancedMode();
    const advancedModeFields = this.advancedModeService.advancedModeFields();

    if (!currentFormContext) {
      return new Set<string>();
    }

    return currentFormContext.getFieldIdsToShow(
      isReadOnly,
      isAdvancedMode,
      advancedModeFields
    );
  });
}
