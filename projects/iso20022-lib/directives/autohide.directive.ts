import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[autohide]',
})
export class AutohideDirective implements OnInit {
  @Input('autohide') controlName?: string;

  ngOnInit(): void {
    console.debug('Audohide for:', this.controlName);
  }

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  show() {
    this.renderer.setStyle(this.el.nativeElement, 'display', '');
  }

  hide() {
    this.renderer.setStyle(this.el.nativeElement, 'display', 'none');
  }

  setVisible(isVisible: boolean) {
    if (isVisible) {
      this.show();
    } else {
      this.hide();
    }
  }
}

// ---- From form-rules.directive.ts ----
// @ContentChildren(AutohideDirective, { descendants: true })
// autohideChildren?: QueryList<AutohideDirective>;

//private inputMap: { [key: string]: AutohideDirective } = {};

/**
 * Map all autohide elements to their corresponding form input
 */
// console.debug('AutohideDirectives', this.autohideChildren);
// this.autohideChildren?.forEach((child) => {
//   if (child.controlName) {
//     this.inputMap[child.controlName] = child;
//   }
// });
// console.debug('Found Autohide:', this.inputMap);

/**
 * Set default visibility for autohide form-inputs
 */
// for (const field in this.formGroup.controls) {
//   const targetRules = this.rules.filter((r) => r.target === field);
//   console.debug('Init Autohide:', field);
//   if (targetRules.length > 0) {
//     for (const rule of targetRules) {
//       if (!this.inputMap[rule.target]) {
//         continue;
//       }
//       if (rule.type === 'displayed') {
//         this.inputMap[rule.target]?.setVisible(rule.value);
//       } else if (rule.type === 'condition') {
//         const visibilityRules = this.getVisibilityRule(rule.rules);
//         if (visibilityRules === null || visibilityRules.length <= 0) {
//           continue;
//         }
//         const defaultVisibility = this.getDefaultVisibility(targetRules);
//         this.updateVisibility(
//           this.inputMap[rule.target],
//           rule.conditions,
//           visibilityRules,
//           defaultVisibility
//         );
//       }
//     }
//   }
// }

/**
 * Update the autohide form-inputs for every input
 * Same reason as above
 */
// this.subscribtions.push(
//   this.formGroup.valueChanges.subscribe((values) => {
//     Object.keys(this.formGroup!.controls).forEach((field) => {
//       if (this.inputMap[field]) {
//         const targetRules = this.rules.filter((r) => r.target === field);
//         for (const rule of targetRules) {
//           if (rule.type !== 'condition') {
//             continue;
//           }
//           const visibilityRules = this.getVisibilityRule(rule.rules);
//           if (visibilityRules === null || visibilityRules.length <= 0) {
//             continue;
//           }
//           const defaultVisibility = this.getDefaultVisibility(targetRules);
//           this.updateVisibility(
//             this.inputMap[rule.target],
//             rule.conditions,
//             visibilityRules,
//             defaultVisibility
//           );
//         }
//       }
//     });
//   })
// );
// private getDefaultVisibility(rules: Array<Rule>): boolean {
//   const visRules = rules.filter((r) => r.type === 'displayed');
//   if (visRules.length > 0) {
//     return visRules[visRules.length - 1].value;
//   } else {
//     return true;
//   }
// }

// private getVisibilityRule(rules: Array<Rule>): Rule[] | null {
//   return rules.filter((r) => r.type === 'displayed');
// }

// private updateVisibility(
//   target: AutohideDirective,
//   conditions: Array<Condition>,
//   visibilityRules: Array<Rule>,
//   defaultVisibility = true
// ) {
//   console.debug('UpdateVisibility:', target.controlName);
//   console.debug('\tTarget:', target);
//   console.debug('\tConditions:', conditions);
//   console.debug('\tVisabilityRules:', visibilityRules);
//   console.debug('\tDefaultVisability:', defaultVisibility);
//   let allConditionsMet = false;
//   for (const condition of conditions) {
//     const relatedControl = this.formGroup!.get(condition.field);
//     if (!relatedControl) {
//       continue;
//     }
//     const relatedValue = relatedControl.value;
//     let conditionMet = false;

//     switch (condition.type) {
//       case 'value':
//         conditionMet = relatedValue === condition.value;
//         break;
//       case 'present':
//         conditionMet = relatedValue !== null;
//         break;
//       case 'notEqual':
//         const otherField = this.formGroup!.get(condition.otherField);
//         conditionMet = relatedValue !== otherField?.value;
//     }

//     allConditionsMet = conditionMet;
//     if (!conditionMet) {
//       // we don't have to check every condition, since all must be met
//       break;
//     }
//   }
//   console.debug('\tAll conditions met:', allConditionsMet);
//   if (allConditionsMet) {
//     for (const rule of visibilityRules) {
//       if (rule.type === 'displayed') {
//         target.setVisible(rule.value);
//       } else {
//         console.warn('Got a wrong rule in updateVisability: ', rule);
//       }
//     }
//   } else {
//     target.setVisible(defaultVisibility);
//   }
// }
