[{"id": "generated__Document-FIToFIPmtStsRpt-GrpHdr-MsgId__required", "description": "GrpHdr-MsgId is required.", "descriptionTranslationProposal": "GrpHdr-MsgId ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-GrpHdr-MsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-GrpHdr-MsgId__maxLength", "description": "GrpHdr-MsgId must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-MsgId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-GrpHdr-MsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-GrpHdr-MsgId__pattern__86207ffe", "description": "GrpHdr-MsgId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-<PERSON>gId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-GrpHdr-MsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm__pattern__e2ce1f8b", "description": "GrpHdr-CreDtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "GrpHdr-CreDtTm muss dem <PERSON> ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm"}, {"id": "generated__Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm__required", "description": "GrpHdr-CreDtTm is required.", "descriptionTranslationProposal": "GrpHdr-CreDtTm ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId__required", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId is required.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId__maxLength", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId__pattern__86207ffe", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId__required", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId is required.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId__maxLength", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId__pattern__86207ffe", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm__pattern__e2ce1f8b", "description": "TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm muss dem <PERSON> ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId__maxLength", "description": "TxInfAndSts-OrgnlInstrId must be at most 16 characters long.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlInstrId darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId__pattern__86207ffe", "description": "TxInfAndSts-OrgnlInstrId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlInstrId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId__required", "description": "TxInfAndSts-OrgnlEndToEndId is required.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlEndToEndId ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId__maxLength", "description": "TxInfAndSts-OrgnlEndToEndId must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlEndToEndId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId__pattern__86207ffe", "description": "TxInfAndSts-OrgnlEndToEndId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlEndToEndId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId__maxLength", "description": "TxInfAndSts-OrgnlTxId must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlTxId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId__pattern__86207ffe", "description": "TxInfAndSts-OrgnlTxId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlTxId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR__pattern__2652818e", "description": "TxInfAndSts-OrgnlUETR must match the pattern ^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlUETR muss dem <PERSON> ^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$ entsprechen.", "type": "pattern", "value": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR__required", "description": "TxInfAndSts-OrgnlUETR is required.", "descriptionTranslationProposal": "TxInfAndSts-OrgnlUETR ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts__required", "description": "TxInfAndSts-TxSts is required.", "descriptionTranslationProposal": "TxInfAndSts-TxSts ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts__maxLength", "description": "TxInfAndSts-TxSts must be at most 4 characters long.", "descriptionTranslationProposal": "TxInfAndSts-TxSts darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry__pattern__a4aca9c5", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine__maxItems", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC__pattern__20cc4331", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC muss dem <PERSON>er ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI__pattern__dc4f5bc9", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI muss dem <PERSON>er ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr__maxItems", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr must have at most 2 items.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr darf höchstens 2 Elemente haben.", "type": "maxItems", "value": 2, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-<PERSON><PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id__conditional-required__d3935eb4", "description": "If any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-(SchmeNm or Issr) is present, then TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id__conditional-required__d3935eb4+Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id__required-conditional", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id is required if any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__pattern__8854bc02", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__pattern__ffd2e254", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__pattern__a4aca9c5", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__aab453e6", "description": "If any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth or CityOfBirth or CtryOfBirth) is present, then TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist, dann ist TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt erforderlich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__conditional-required__aab453e6+Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt__required-conditional", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt is required if any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth or CityOfBirth or CtryOfBirth) is present.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt ist erford<PERSON>lich, wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(PrvcOfBirth oder CityOfBirth oder CtryOfBirth) vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__a53fbb18", "description": "If any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt or PrvcOfBirth or CtryOfBirth) is present, then TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist, dann ist TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth erforderlich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__conditional-required__a53fbb18+Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth__required-conditional", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth is required if any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt or PrvcOfBirth or CtryOfBirth) is present.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth ist erforderlich, wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CtryOfBirth) vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__9ebea383", "description": "If any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt or PrvcOfBirth or CityOfBirth) is present, then TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist, dann ist TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth erforderlich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__conditional-required__9ebea383+Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth__required-conditional", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth is required if any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt or PrvcOfBirth or CityOfBirth) is present.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth ist erforderlich, wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-(BirthDt oder PrvcOfBirth oder CityOfBirth) vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr__maxItems", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr must have at most 2 items.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-O<PERSON>r darf hö<PERSON> 2 Elemente haben.", "type": "maxItems", "value": 2, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr__maxLength", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id__conditional-required__dcbe36ce", "description": "If any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-(SchmeNm or Issr) is present, then TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id__conditional-required__dcbe36ce+Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id__required-conditional", "description": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id is required if any of the fields TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes__pattern__a4aca9c5", "description": "TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd__maxLength", "description": "TxInfAndSts-StsRsnInf-Rsn-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Rsn-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry__maxLength", "description": "TxInfAndSts-StsRsnInf-Rsn-<PERSON><PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Rsn-<PERSON><PERSON><PERSON> höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-Rsn-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-Rsn-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf__maxItems", "description": "TxInfAndSts-StsRsnInf-AddtlInf must have at most 2 items.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-AddtlInf darf höchstens 2 Elemente haben.", "type": "maxItems", "value": 2, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf__maxLength", "description": "TxInfAndSts-StsRsnInf-AddtlInf must be at most 105 characters long.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-AddtlInf darf höchstens 105 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 105, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf__pattern__86207ffe", "description": "TxInfAndSts-StsRsnInf-AddtlInf must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-StsRsnInf-AddtlInf muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt__pattern__8854bc02", "description": "TxInfAndSts-FctvIntrBkSttlmDt-Dt must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "TxInfAndSts-FctvIntrBkSttlmDt-Dt muss dem <PERSON>er ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm__pattern__e2ce1f8b", "description": "TxInfAndSts-FctvIntrBkSttlmDt-DtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "TxInfAndSts-FctvIntrBkSttlmDt-DtTm muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef__maxLength", "description": "TxInfAndSts-ClrSysRef must be at most 35 characters long.", "descriptionTranslationProposal": "TxInfAndSts-ClrSysRef darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef__pattern__86207ffe", "description": "TxInfAndSts-ClrSysRef must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-ClrSysRef muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "TxInfAndSts-InstgAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI__required", "description": "TxInfAndSts-InstgAgt-FinInstnId-BICFI is required.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-BICFI ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__596594db", "description": "If TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is present, then TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__596594db+Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__c2c23477", "description": "If TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__c2c23477+Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is required if TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "TxInfAndSts-InstgAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-InstgAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "TxInfAndSts-InstdAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI__required", "description": "TxInfAndSts-InstdAgt-FinInstnId-BICFI is required.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-BICFI ist erforderlich.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b4812ed8", "description": "If TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is present, then TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b4812ed8+Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__949589f4", "description": "If TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__949589f4+Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is required if TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "TxInfAndSts-InstdAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "TxInfAndSts-InstdAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI"}]