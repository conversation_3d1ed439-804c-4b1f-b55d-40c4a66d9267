{"essentials": {"document": {"basic": {"Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm": "CreationDateTime", "Document-FIToFIPmtStsRpt-GrpHdr-MsgId": "MessageIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef": "ClearingSystemReference", "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt": "Date", "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm": "DateTime", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI": "LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI": "LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId": "OriginalEndToEndIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm": "OriginalCreationDateTime", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId": "OriginalMessageIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId": "OriginalMessageNameIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId": "OriginalInstructionIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId": "OriginalTransactionIdentification", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR": "OriginalUETR", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf": "AdditionalInformation", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes": "CountryOfResidence", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI": "LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm": "Name", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept": "Department", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm": "StreetName", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr": "Floor", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx": "PostBox", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room": "Room", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd": "PostCode", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm": "TownName", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry": "Country", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd": "Code", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry": "Proprietary", "Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts": "TransactionStatus"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "SERVER": {"document": {"basic": {}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {"AppHdr-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-BizSvc": "BusinessService", "AppHdr-CharSet": "CharacterSet", "AppHdr-CpyDplct": "CopyDuplicate", "AppHdr-CreDt": "CreationDate", "AppHdr-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-MktPrctc-Id": "Identification", "AppHdr-MktPrctc-Regy": "Registry", "AppHdr-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Prty": "Priority", "AppHdr-PssblDplct": "PossibleDuplicate", "AppHdr-Rltd-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-Rltd-BizSvc": "BusinessService", "AppHdr-Rltd-CharSet": "CharacterSet", "AppHdr-Rltd-CpyDplct": "CopyDuplicate", "AppHdr-Rltd-CreDt": "CreationDate", "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-Rltd-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Rltd-Prty": "Priority", "AppHdr-Rltd-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-To-FIId-FinInstnId-LEI": "LEI", "AppHdr-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-To-FIId-FinInstnId-LEI": "LEI"}, "advanced": {}, "advancedScopes": []}}}