[{"id": "generated__AppHdr-Fr-FIId-FinInstnId-BICFI__pattern", "description": "Fr-FIId-FinInstnId-BICFI must match the pattern [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}.", "descriptionTranslationProposal": "Fr-FIId-FinInstnId-BICFI muss dem <PERSON>er [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}", "target": "AppHdr-Fr-FIId-FinInstnId-BICFI"}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__pattern", "description": "Fr-FIId-FinInstnId-ClrSysMmbId-MmbId must match the pattern [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+.", "descriptionTranslationProposal": "Fr-FIId-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON> [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+ entsprechen.", "type": "pattern", "value": "[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+", "target": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "Fr-FIId-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "Fr-FIId-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__9cc5afb5", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__9cc5afb5+AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__9cc5afb5+AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}]}, {"id": "generated__AppHdr-Fr-FIId-FinInstnId-LEI__pattern", "description": "Fr-FIId-FinInstnId-LEI must match the pattern [A-Z0-9]{18,18}[0-9]{2,2}.", "descriptionTranslationProposal": "Fr-FIId-FinInstnId-LEI muss dem <PERSON> [A-Z0-9]{18,18}[0-9]{2,2} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{18,18}[0-9]{2,2}", "target": "AppHdr-Fr-FIId-FinInstnId-LEI"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-BICFI__pattern", "description": "To-FIId-FinInstnId-BICFI must match the pattern [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}.", "descriptionTranslationProposal": "To-FIId-FinInstnId-BICFI muss dem <PERSON>er [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}", "target": "AppHdr-To-FIId-FinInstnId-BICFI"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId__pattern", "description": "To-FIId-FinInstnId-ClrSysMmbId-MmbId must match the pattern [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+.", "descriptionTranslationProposal": "To-FIId-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON> [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+ entsprechen.", "type": "pattern", "value": "[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+", "target": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "To-FIId-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "To-FIId-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__bd910375", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__bd910375+AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__bd910375+AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}]}, {"id": "generated__AppHdr-To-FIId-FinInstnId-LEI__pattern", "description": "To-FIId-FinInstnId-LEI must match the pattern [A-Z0-9]{18,18}[0-9]{2,2}.", "descriptionTranslationProposal": "To-FIId-FinInstnId-LEI muss dem <PERSON> [A-Z0-9]{18,18}[0-9]{2,2} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{18,18}[0-9]{2,2}", "target": "AppHdr-To-FIId-FinInstnId-LEI"}, {"id": "generated__AppHdr-BizMsgIdr__required", "description": "BizMsgIdr is required.", "descriptionTranslationProposal": "BizMsgIdr ist erforderlich.", "type": "required", "target": "AppHdr-BizMsgIdr"}, {"id": "generated__AppHdr-BizMsgIdr__maxLength", "description": "BizMsgIdr must be at most 35 characters long.", "descriptionTranslationProposal": "BizMsgIdr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-BizMsgIdr"}, {"id": "generated__AppHdr-MsgDefIdr__required", "description": "MsgDefIdr is required.", "descriptionTranslationProposal": "MsgDefIdr ist erforderlich.", "type": "required", "target": "AppHdr-MsgDefIdr"}, {"id": "generated__AppHdr-MsgDefIdr__maxLength", "description": "MsgDefIdr must be at most 35 characters long.", "descriptionTranslationProposal": "MsgDefIdr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-MsgDefIdr"}, {"id": "generated__AppHdr-BizSvc__pattern", "description": "BizSvc must match the pattern [a-z0-9]{1,10}\\.([a-z0-9]{1,10}\\.)+\\d\\d.", "descriptionTranslationProposal": "BizSvc muss dem <PERSON>er [a-z0-9]{1,10}\\.([a-z0-9]{1,10}\\.)+\\d\\d entsprechen.", "type": "pattern", "value": "[a-z0-9]{1,10}\\.([a-z0-9]{1,10}\\.)+\\d\\d", "target": "AppHdr-BizSvc"}, {"id": "generated__AppHdr-BizSvc__required", "description": "BizSvc is required.", "descriptionTranslationProposal": "BizSvc ist erforderlich.", "type": "required", "target": "AppHdr-BizSvc"}, {"id": "generated__AppHdr-BizSvc__maxLength", "description": "BizSvc must be at most 35 characters long.", "descriptionTranslationProposal": "BizSvc darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-BizSvc"}, {"id": "generated__AppHdr-MktPrctc-<PERSON>y__maxLength", "description": "MktPrctc-Regy must be at most 350 characters long.", "descriptionTranslationProposal": "MktPrctc-<PERSON><PERSON> darf höchstens 350 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 350, "target": "AppHdr-MktPrctc-Regy"}, {"id": "generated__AppHdr-MktPrctc-Id__maxLength", "description": "MktPrctc-Id must be at most 2048 characters long.", "descriptionTranslationProposal": "MktPrctc-Id darf höchstens 2048 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 2048, "target": "AppHdr-MktPrctc-Id"}, {"id": "generated__AppHdr-MktPrctc__conditional-required__784ad81d", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-MktPrctc-Regy", "type": "present", "value": true}, {"field": "AppHdr-MktPrctc-Id", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-MktPrctc__conditional-required__784ad81d+AppHdr-MktPrctc-Regy__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-MktPrctc-Regy", "type": "required"}, {"id": "generated__AppHdr-MktPrctc__conditional-required__784ad81d+AppHdr-MktPrctc-Id__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-MktPrctc-Id", "type": "required"}]}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI__pattern", "description": "Rltd-Fr-FIId-FinInstnId-BICFI must match the pattern [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}.", "descriptionTranslationProposal": "Rltd-Fr-FIId-FinInstnId-BICFI muss dem <PERSON>er [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__pattern", "description": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId must match the pattern [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+.", "descriptionTranslationProposal": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+ entsprechen.", "type": "pattern", "value": "[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__da979695", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__da979695+AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId__conditional-required__da979695+AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}]}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId-LEI__pattern", "description": "Rltd-Fr-FIId-FinInstnId-LEI must match the pattern [A-Z0-9]{18,18}[0-9]{2,2}.", "descriptionTranslationProposal": "Rltd-Fr-FIId-FinInstnId-LEI muss dem <PERSON> [A-Z0-9]{18,18}[0-9]{2,2} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{18,18}[0-9]{2,2}", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI"}, {"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId__conditional-required__29d4ecb2", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Rltd-Fr-FIId-FinInstnId__conditional-required__29d4ecb2+AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI", "type": "required"}]}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-BICFI__pattern", "description": "Rltd-To-FIId-FinInstnId-BICFI must match the pattern [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}.", "descriptionTranslationProposal": "Rltd-To-FIId-FinInstnId-BICFI muss dem <PERSON>er [A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}", "target": "AppHdr-Rltd-To-FIId-FinInstnId-BICFI"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId__pattern", "description": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId must match the pattern [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+.", "descriptionTranslationProposal": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er [0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+ entsprechen.", "type": "pattern", "value": "[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+", "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__eee276f5", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__eee276f5+AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId__conditional-required__eee276f5+AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}]}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId-LEI__pattern", "description": "Rltd-To-FIId-FinInstnId-LEI must match the pattern [A-Z0-9]{18,18}[0-9]{2,2}.", "descriptionTranslationProposal": "Rltd-To-FIId-FinInstnId-LEI muss dem <PERSON> [A-Z0-9]{18,18}[0-9]{2,2} entsprechen.", "type": "pattern", "value": "[A-Z0-9]{18,18}[0-9]{2,2}", "target": "AppHdr-Rltd-To-FIId-FinInstnId-LEI"}, {"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId__conditional-required__a8e485b2", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Rltd-To-FIId-FinInstnId-BICFI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-LEI", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Rltd-To-FIId-FinInstnId__conditional-required__a8e485b2+AppHdr-Rltd-To-FIId-FinInstnId-BICFI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-BICFI", "type": "required"}]}, {"id": "generated__AppHdr-Rltd-BizMsgIdr__maxLength", "description": "Rltd-BizMsgIdr must be at most 35 characters long.", "descriptionTranslationProposal": "Rltd-BizMsgIdr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-Rltd-BizMsgIdr"}, {"id": "generated__AppHdr-Rltd-MsgDefIdr__maxLength", "description": "Rltd-MsgDefIdr must be at most 35 characters long.", "descriptionTranslationProposal": "Rltd-MsgDefIdr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-Rltd-MsgDefIdr"}, {"id": "generated__AppHdr-Rltd-BizSvc__maxLength", "description": "Rltd-BizSvc must be at most 35 characters long.", "descriptionTranslationProposal": "Rltd-BizSvc darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "AppHdr-Rltd-BizSvc"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481", "description": "", "descriptionTranslationProposal": "", "type": "condition", "conditionsConnector": "or", "rulesConnector": "and", "conditions": [{"field": "AppHdr-Rltd-CharSet", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-BICFI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}, {"field": "AppHdr-Rltd-To-FIId-FinInstnId-LEI", "type": "present", "value": true}, {"field": "AppHdr-Rltd-BizMsgIdr", "type": "present", "value": true}, {"field": "AppHdr-Rltd-MsgDefIdr", "type": "present", "value": true}, {"field": "AppHdr-Rltd-BizSvc", "type": "present", "value": true}, {"field": "AppHdr-Rltd-CreDt", "type": "present", "value": true}, {"field": "AppHdr-Rltd-CpyDplct", "type": "present", "value": true}, {"field": "AppHdr-Rltd-Prty", "type": "present", "value": true}], "rules": [{"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-Fr-FIId-FinInstnId-LEI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-To-FIId-FinInstnId-BICFI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-BICFI", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-To-FIId-FinInstnId-LEI__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-To-FIId-FinInstnId-LEI", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-BizMsgIdr__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-BizMsgIdr", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-MsgDefIdr__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-MsgDefIdr", "type": "required"}, {"id": "generated__AppHdr-Rltd__conditional-required__78311481+AppHdr-Rltd-CreDt__required-conditional", "description": "", "descriptionTranslationProposal": "", "target": "AppHdr-Rltd-CreDt", "type": "required"}]}]