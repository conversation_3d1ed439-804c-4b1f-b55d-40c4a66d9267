{"essentials": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt": "InterbankSettlementDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd": "Unstructured"}, "advanced": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id": "Identification"}, "advancedScopes": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry"]}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "paymentBasics": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId": "TransactionIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty": "InstructionPriority", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl": "ClearingChannel", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry": "Proprietary"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "amountCurrency": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate": "ExchangeRate"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "settlementInformation": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty": "SettlementPriority", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm": "DebitDateTime", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm": "CreditDateTime", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm": "FromTime", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm": "TillTime", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm": "CLSTime", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm": "RejectTime", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id": "Identification"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "debtorInformation": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr": "Issuer"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "creditorInformation": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr": "Issuer"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "financialInstitutionInformation": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf": "InstructionInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf": "InstructionInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id": "Identification"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "remittanceInformation": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb": "Number", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt": "RelatedDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb": "Number", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt": "RelatedDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc": "Description", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn": "Reason", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn": "Reason", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref": "Reference", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId": "TaxIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId": "RegistrationIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp": "TaxType", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId": "TaxIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId": "RegistrationIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp": "TaxType", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl": "Title", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId": "TaxIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId": "RegistrationIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp": "TaxType", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl": "Title", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone": "AdministrationZone", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb": "ReferenceNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd": "Method", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt": "Date", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb": "SequenceNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp": "Type", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy": "Category", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls": "CategoryDetails", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts": "DebtorS<PERSON>us", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId": "CertificateIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd": "FormsCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr": "Year", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp": "Type", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt": "FromDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt": "ToDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate": "Rate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr": "Year", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp": "Type", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt": "FromDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt": "ToDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf": "AdditionalInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes": "CountryOfResidence", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC": "AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb": "ReferenceNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt": "Date", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd": "FamilyMedicalInsuranceIndicator", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd": "EmployeeTerminationIndicator", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf": "AdditionalRemittanceInformation", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId": "RemittanceIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd": "Method", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr": "ElectronicAddress", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine": "AddressLine"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "regulatoryReporting": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd": "DebitCreditReportingIndicator", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp": "Type", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt": "Date", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf": "Information"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "SERVER": {"document": {"basic": {"Document-FIToFICstmrCdtTrf-GrpHdr-CreDtTm": "CreationDateTime", "Document-FIToFICstmrCdtTrf-GrpHdr-MsgId": "MessageIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId": "InstructionIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId": "EndToEndIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR": "UETR", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd": "SettlementMethod", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef": "ClearingSystemReference", "Document-FIToFICstmrCdtTrf-GrpHdr-NbOfTxs": "NumberOfTransactions", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN": "IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id": "Identification", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr": "Issuer", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm": "Name", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd": "Code", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id": "Identification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry": "Proprietary", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount": "amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm": "Name", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI": "LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI": "BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI": "LEI"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {"AppHdr-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-BizSvc": "BusinessService", "AppHdr-CharSet": "CharacterSet", "AppHdr-CpyDplct": "CopyDuplicate", "AppHdr-CreDt": "CreationDate", "AppHdr-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-MktPrctc-Id": "Identification", "AppHdr-MktPrctc-Regy": "Registry", "AppHdr-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Prty": "Priority", "AppHdr-PssblDplct": "PossibleDuplicate", "AppHdr-Rltd-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-Rltd-BizSvc": "BusinessService", "AppHdr-Rltd-CharSet": "CharacterSet", "AppHdr-Rltd-CpyDplct": "CopyDuplicate", "AppHdr-Rltd-CreDt": "CreationDate", "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-Rltd-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Rltd-Prty": "Priority", "AppHdr-Rltd-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-To-FIId-FinInstnId-LEI": "LEI", "AppHdr-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-To-FIId-FinInstnId-LEI": "LEI"}, "advanced": {}, "advancedScopes": []}}}