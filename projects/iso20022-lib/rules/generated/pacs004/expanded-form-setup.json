{"essentials": {"document": {"basic": {"Document-PmtRtr-GrpHdr-CreDtTm": "CreationDateTime", "Document-PmtRtr-GrpHdr-MsgId": "MessageIdentification", "Document-PmtRtr-GrpHdr-NbOfTxs": "NumberOfTransactions", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN": "IBAN", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Nm": "Name", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id": "Identification", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd": "Code", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmMtd": "SettlementMethod", "Document-PmtRtr-TxInf-ChrgBr": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-ChrgsInf-Amt-amount": "amount", "Document-PmtRtr-TxInf-ChrgsInf-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-ClrSysRef": "ClearingSystemReference", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-IntrBkSttlmDt": "InterbankSettlementDate", "Document-PmtRtr-TxInf-OrgnlClrSysRef": "OriginalClearingSystemReference", "Document-PmtRtr-TxInf-OrgnlEndToEndId": "OriginalEndToEndIdentification", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlCreDtTm": "OriginalCreationDateTime", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgId": "OriginalMessageIdentification", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgNmId": "OriginalMessageNameIdentification", "Document-PmtRtr-TxInf-OrgnlInstrId": "OriginalInstructionIdentification", "Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlIntrBkSttlmDt": "OriginalInterbankSettlementDate", "Document-PmtRtr-TxInf-OrgnlTxId": "OriginalTransactionIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-CcyOfTrf": "CurrencyOfTransfer", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-BrnchId-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmDt": "InterbankSettlementDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInd": "AmendmentIndicator", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFnlColltnDt": "OriginalFinalCollectionDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-CntPerPrd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-PtInTm": "PointInTime", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlMndtId": "OriginalMandateIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlTrckgDays": "OriginalTrackingDays", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-DtOfSgntr": "DateOfSignature", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-ElctrncSgntr": "ElectronicSignature", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FnlColltnDt": "FinalCollectionDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-CntPerPrd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-PtInTm": "PointInTime", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FrstColltnDt": "FirstCollectionDate", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-MndtId": "MandateIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-TrckgDays": "TrackingDays", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtMtd": "PaymentMethod", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-ClrChanl": "ClearingChannel", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-InstrPrty": "InstructionPriority", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SeqTp": "SequenceType", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdColltnDt": "RequestedCollectionDate", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-Dt": "Date", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-DtTm": "DateTime", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-AddtlRmtInf": "AdditionalRemittanceInformation", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Ref": "Reference", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Dt": "Date", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd": "FamilyMedicalInsuranceIndicator", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd": "EmployeeTerminationIndicator", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RefNb": "ReferenceNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn": "Reason", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn": "Reason", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Desc": "Description", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb": "Number", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt": "RelatedDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Nb": "Number", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-RltdDt": "RelatedDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-AdmstnZone": "AdministrationZone", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-RegnId": "RegistrationIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxId": "TaxIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxTp": "TaxType", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl": "Title", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-RegnId": "RegistrationIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxId": "TaxIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxTp": "TaxType", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dt": "Date", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Mtd": "Method", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf": "AdditionalInformation", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CertId": "CertificateIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Ctgy": "Category", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls": "CategoryDetails", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts": "DebtorS<PERSON>us", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd": "FormsCode", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt": "FromDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt": "ToDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr": "Year", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt": "FromDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt": "ToDate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr": "Year", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate": "Rate", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Tp": "Type", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-RefNb": "ReferenceNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-SeqNb": "SequenceNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount": "amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl": "Title", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId": "RegistrationIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId": "TaxIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp": "TaxType", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Ustrd": "Unstructured", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmMtd": "SettlementMethod", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-OrgnlUETR": "OriginalUETR", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-BrnchId-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-BICFI": "BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Nm": "Name", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrdInstdAmt-amount": "amount", "Document-PmtRtr-TxInf-RtrdInstdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt-amount": "amount", "Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-PmtRtr-TxInf-RtrId": "ReturnIdentification", "Document-PmtRtr-TxInf-RtrRsnInf-AddtlInf": "AdditionalInformation", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-CtryOfRes": "CountryOfResidence", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-AnyBIC": "AnyBIC", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-LEI": "LEI", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Id": "Identification", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Issr": "Issuer", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Nm": "Name", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-AdrLine": "AddressLine", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNb": "BuildingNumber", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNm": "BuildingName", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Ctry": "Country", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Dept": "Department", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-DstrctNm": "DistrictName", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Flr": "Floor", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstBx": "PostBox", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstCd": "PostCode", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Room": "Room", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-StrtNm": "StreetName", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-SubDept": "SubDepartment", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnLctnNm": "TownLocationName", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnNm": "TownName", "Document-PmtRtr-TxInf-RtrRsnInf-Rsn-Cd": "Code", "Document-PmtRtr-TxInf-SttlmPrty": "SettlementPriority", "Document-PmtRtr-TxInf-SttlmTmIndctn-CdtDtTm": "CreditDateTime", "Document-PmtRtr-TxInf-SttlmTmIndctn-DbtDtTm": "DebitDateTime", "Document-PmtRtr-TxInf-XchgRate": "ExchangeRate"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "SERVER": {"document": {"basic": {}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {"AppHdr-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-BizSvc": "BusinessService", "AppHdr-CharSet": "CharacterSet", "AppHdr-CpyDplct": "CopyDuplicate", "AppHdr-CreDt": "CreationDate", "AppHdr-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-MktPrctc-Id": "Identification", "AppHdr-MktPrctc-Regy": "Registry", "AppHdr-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Prty": "Priority", "AppHdr-PssblDplct": "PossibleDuplicate", "AppHdr-Rltd-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-Rltd-BizSvc": "BusinessService", "AppHdr-Rltd-CharSet": "CharacterSet", "AppHdr-Rltd-CpyDplct": "CopyDuplicate", "AppHdr-Rltd-CreDt": "CreationDate", "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-Rltd-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Rltd-Prty": "Priority", "AppHdr-Rltd-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-To-FIId-FinInstnId-LEI": "LEI", "AppHdr-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-To-FIId-FinInstnId-LEI": "LEI"}, "advanced": {}, "advancedScopes": []}}}