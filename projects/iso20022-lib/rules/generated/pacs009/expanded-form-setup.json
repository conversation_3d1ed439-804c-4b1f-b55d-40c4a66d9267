{"essentials": {"document": {"basic": {"Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf": "InstructionInformation", "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf": "InstructionInformation", "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount": "amount", "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt": "InterbankSettlementDate", "Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef": "ClearingSystemReference", "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId": "EndToEndIdentification", "Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId": "InstructionIdentification", "Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId": "TransactionIdentification", "Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR": "UETR", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl": "ClearingChannel", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty": "InstructionPriority", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-Purp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd": "Unstructured", "Document-FICdtTrf-CdtTrfTxInf-SttlmPrty": "SettlementPriority", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm": "CreditDateTime", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm": "DebitDateTime", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm": "CLSTime", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm": "FromTime", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm": "RejectTime", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm": "TillTime", "Document-FICdtTrf-GrpHdr-CreDtTm": "CreationDateTime", "Document-FICdtTrf-GrpHdr-MsgId": "MessageIdentification", "Document-FICdtTrf-GrpHdr-NbOfTxs": "NumberOfTransactions", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN": "IBAN", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id": "Identification", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd": "Code", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm": "Name", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id": "Identification", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd": "SettlementMethod"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "IntermediaryAgents": {"document": {"basic": {"Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI": "BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI": "LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept": "Department", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr": "Floor", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room": "Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry": "Country", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN": "IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id": "Identification", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr": "Issuer", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm": "Name", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd": "Code", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id": "Identification"}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {}, "advanced": {}, "advancedScopes": []}}, "SERVER": {"document": {"basic": {}, "advanced": {}, "advancedScopes": []}, "bah": {"basic": {"AppHdr-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-BizSvc": "BusinessService", "AppHdr-CharSet": "CharacterSet", "AppHdr-CpyDplct": "CopyDuplicate", "AppHdr-CreDt": "CreationDate", "AppHdr-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-MktPrctc-Id": "Identification", "AppHdr-MktPrctc-Regy": "Registry", "AppHdr-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Prty": "Priority", "AppHdr-PssblDplct": "PossibleDuplicate", "AppHdr-Rltd-BizMsgIdr": "BusinessMessageIdentifier", "AppHdr-Rltd-BizSvc": "BusinessService", "AppHdr-Rltd-CharSet": "CharacterSet", "AppHdr-Rltd-CpyDplct": "CopyDuplicate", "AppHdr-Rltd-CreDt": "CreationDate", "AppHdr-Rltd-Fr-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-Fr-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-Fr-FIId-FinInstnId-LEI": "LEI", "AppHdr-Rltd-MsgDefIdr": "MessageDefinitionIdentifier", "AppHdr-Rltd-Prty": "Priority", "AppHdr-Rltd-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-Rltd-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-Rltd-To-FIId-FinInstnId-LEI": "LEI", "AppHdr-To-FIId-FinInstnId-BICFI": "BICFI", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "AppHdr-To-FIId-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "AppHdr-To-FIId-FinInstnId-LEI": "LEI"}, "advanced": {}, "advancedScopes": []}}}