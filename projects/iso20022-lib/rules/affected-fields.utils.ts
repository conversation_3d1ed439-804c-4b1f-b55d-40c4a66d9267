import { isBasicCondition } from './types';
import type { BasicCondition, Rule, XsdElement } from './types';
import { getProgressiveSubstrings } from '@helaba/iso20022-lib/util';

export function computeAffectedFieldsForFieldValueChange(
  rules: Rule<string, string | undefined>[],
  nameMappings: XsdElement
): Map<string, Set<string>> {
  const affectedFieldsForChange: Map<string, Set<string>> = new Map();

  for (const rule of rules) {
    if (rule.type === 'condition') {
      // For conditional rules, if the changed field is part of a condition, all targets of subrules might be affected
      const conditionFields: string[] = rule.conditions.flatMap((condition) =>
        isBasicCondition(condition)
          ? getFieldsFromBasicCondition(condition)
          : condition.conditions
              .map((c) => getFieldsFromBasicCondition(c))
              .flat()
      );
      const subRules = rule.rules;
      const subRuleTargets: string[] = subRules.map(
        (subRule) => subRule.target
      );

      for (const field of conditionFields) {
        for (const subRuleTarget of subRuleTargets) {
          addValueToMapEntry(affectedFieldsForChange, field, subRuleTarget);
        }
      }

      for (const subRule of subRules) {
        addValueToMapEntry(
          affectedFieldsForChange,
          subRule.target,
          subRule.target
        );
        // For conditional 'required' rules that pertain to a field nested in a form array, also add the parent form array field as affected field. This allows us to show an error at the form array level when no item has been added yet.
        if (subRule.type === 'required') {
          const parentFormArrayFields: string[] = getParentFormArrayFields(
            subRule.target,
            nameMappings
          );
          // Also retrieve the fields nested in subRule.target and add the parent form array fields as affected fields for those as well (this way, changing the nested fields will trigger a reevaluation of the parent form array field).
          const nestedSubRuleTargetFields: string[] = getNestedFields(
            subRule.target,
            nameMappings
          );
          for (const nestedSubRuleTargetField of nestedSubRuleTargetFields) {
            addValueToMapEntry(
              affectedFieldsForChange,
              nestedSubRuleTargetField,
              subRule.target
            );
          }
          for (const field of conditionFields) {
            for (const parentFormArrayField of parentFormArrayFields) {
              addValueToMapEntry(
                affectedFieldsForChange,
                field,
                parentFormArrayField
              );
              for (const nestedSubRuleTargetField of nestedSubRuleTargetFields) {
                addValueToMapEntry(
                  affectedFieldsForChange,
                  nestedSubRuleTargetField,
                  parentFormArrayField
                );
              }
            }
          }
        }
      }

      // Also, if the 'rulesConnector' is 'or', that means that changing a subrule target's field might affect all the other subrule target fields.
      if (rule.rulesConnector === 'or') {
        for (const subRuleTarget of subRuleTargets) {
          for (const otherSubRuleTarget of subRuleTargets.filter(
            (t) => t !== subRuleTarget
          )) {
            addValueToMapEntry(
              affectedFieldsForChange,
              subRuleTarget,
              otherSubRuleTarget
            );
          }
        }
      }
    } else {
      addValueToMapEntry(affectedFieldsForChange, rule.target, rule.target);
      // If the rule is a 'required' rule and the target field is nested in a form array, also add the parent form array field as affected field. This way, entering a value into the nested field can clear the errors on the parent field.
      if (rule.type === 'required') {
        const parentFormArrayFields: string[] = getParentFormArrayFields(
          rule.target,
          nameMappings
        );
        // Also retrieve the fields nested in target and add the parent form array fields as affected fields for those as well (this way, changing the nested fields will trigger a reevaluation of the parent form array field).
        const nestedSubRuleTargetFields: string[] = getNestedFields(
          rule.target,
          nameMappings
        );
        for (const nestedSubRuleTargetField of nestedSubRuleTargetFields) {
          addValueToMapEntry(
            affectedFieldsForChange,
            nestedSubRuleTargetField,
            rule.target
          );
        }
        for (const parentFormArrayField of parentFormArrayFields) {
          addValueToMapEntry(
            affectedFieldsForChange,
            rule.target,
            parentFormArrayField
          );
          for (const nestedSubRuleTargetField of nestedSubRuleTargetFields) {
            addValueToMapEntry(
              affectedFieldsForChange,
              nestedSubRuleTargetField,
              parentFormArrayField
            );
          }
        }
      }
    }

    if (rule.type === 'contains') {
      // For 'contains' rules, the target of the rule is also affected by changes to the 'otherField's of the rule.
      const otherFields = rule.value;
      for (const otherField of otherFields) {
        addValueToMapEntry(affectedFieldsForChange, otherField, rule.target);
      }
    }
  }

  return affectedFieldsForChange;
}

function getFieldsFromBasicCondition(condition: BasicCondition): string[] {
  const fields = [condition.field];

  if (condition.type === 'equal' || condition.type === 'greaterThan') {
    fields.push(condition.otherField);
  }

  return fields;
}

function addValueToMapEntry(
  map: Map<string, Set<string>>,
  key: string,
  value: string
) {
  if (map.has(key)) {
    map.get(key)?.add(value);
  } else {
    map.set(key, new Set([value]));
  }
}

function getParentFormArrayFields(
  fieldName: string,
  nameMappings: XsdElement
): string[] {
  let progressiveSubstrings = getProgressiveSubstrings(fieldName);
  progressiveSubstrings.pop(); // Remove the full field name itself
  return progressiveSubstrings.filter((substring) =>
    isFormArrayParent(substring, nameMappings)
  );
}

function getNestedFields(
  fieldName: string,
  nameMappings: XsdElement
): string[] {
  const nameMappingsEntry = findByNestedAbbrName(fieldName, nameMappings);
  if (!nameMappingsEntry) {
    throw new Error(
      `Could not find name mapping entry for field name "${fieldName}".`
    );
  }
  return collectNestedFields(nameMappingsEntry);
}

function collectNestedFields(nameMappingsEntry: XsdElement): string[] {
  let fields: string[] = [];
  for (const child of nameMappingsEntry.children) {
    if (child.children.length == 0 || child.isArray) {
      fields.push(child.nestedAbbrName);
    }
    fields = fields.concat(collectNestedFields(child));
  }
  return fields;
}

function isFormArrayParent(
  substring: string,
  nameMappings: XsdElement
): boolean {
  const nameMappingsEntry = findByNestedAbbrName(substring, nameMappings);
  return nameMappingsEntry?.isArray === true;
}

function findByNestedAbbrName(
  nestedAbbrName: string,
  nameMappings: XsdElement
): XsdElement | undefined {
  if (nameMappings.nestedAbbrName === nestedAbbrName) {
    return nameMappings;
  }
  for (const child of nameMappings.children) {
    const result = findByNestedAbbrName(nestedAbbrName, child);
    if (result) {
      return result;
    }
  }

  return undefined;
}

export function mapToObject(
  map: Map<string, Set<string>>
): Record<string, string[]> {
  const obj: Record<string, string[]> = {};
  for (const [key, valueSet] of map.entries()) {
    obj[key] = Array.from(valueSet);
  }

  return obj;
}
