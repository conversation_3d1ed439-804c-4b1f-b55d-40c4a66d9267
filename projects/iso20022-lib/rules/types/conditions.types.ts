const basicConditionTypes = [
  'value',
  'present',
  'equal',
  'greaterThan',
] as const;
export type BasicConditionType = (typeof basicConditionTypes)[number];

type ConditionType = BasicConditionType | 'group';

type BaseCondition = {
  type: ConditionType;
};

type BaseBasicCondition = BaseCondition & {
  field: string;
  requireEveryArrayItem?: boolean; // If the field is part of an array, this tells the rule engine that the condition is only fulfilled if it applies to every item in the array. Required for 'CBPR_CRED_FormalRule'.
};

export type BasicCondition = BaseBasicCondition &
  (
    | {
        type: 'value';
        value: string;
      }
    | {
        type: 'present';
        value: boolean;
      }
    | {
        type: 'equal';
        value: boolean;
        otherField: string;
      }
    | {
        type: 'greaterThan';
        otherField: string;
      }
  );

export function isBasicCondition(
  condition: Condition
): condition is BasicCondition {
  return condition.type !== 'group';
}

export type ConditionsConnector = 'and' | 'or';

export type NestedCondition = BaseCondition & {
  type: 'group';
  conditions: BasicCondition[]; // Only nested once.
  conditionsConnector?: ConditionsConnector;
};

export function isNestedCondition(
  condition: Condition
): condition is NestedCondition {
  return condition.type === 'group';
}

export type Condition = BasicCondition | NestedCondition;
