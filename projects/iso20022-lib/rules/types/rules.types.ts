import { Condition, ConditionsConnector } from './conditions.types';

const basicRuleTypes = [
  'required',
  'prohibited',
  'maxLength',
  'pattern',
  'value',
  'maxItems',
  'contains',
] as const;
export type BasicRuleType = (typeof basicRuleTypes)[number];

export type RuleType = BasicRuleType | 'condition';

type BaseRule<I = string, D = string> = {
  id: I;
  description: D;
  descriptionTranslationProposal: D;
  type: RuleType;
};

type BaseBasicRule<I = string, D = string> = BaseRule<I, D> & {
  target: string;
};

export type RequiredRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'required';
};

export type ProhibitedRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'prohibited';
};

type MaxLengthRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'maxLength';
  value: number;
};

export type PatternRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'pattern';
  value: string;
};

export type ValueRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'value';
  value: string;
  isEqual: boolean;
};

type MaxItemsRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'maxItems';
  value: number;
};

// E.g. "Data present in structured elements within the Postal Address must not be repeated in AddressLine" -> target: AdrLine, value: ['Dept', 'SubDept', 'StrtNm', ...], contains: false
type ContainsRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'contains';
  value: string[];
  contains: boolean;
};

export type BasicRule<I = string, D = string> =
  | RequiredRule<I, D>
  | ProhibitedRule<I, D>
  | MaxLengthRule<I, D>
  | PatternRule<I, D>
  | ValueRule<I, D>
  | MaxItemsRule<I, D>
  | ContainsRule<I, D>;

// The targets of conditional rules are in the rules array
export type ConditionalRule<I = string, D = string> = BaseRule<I, D> & {
  type: 'condition';
  conditions: Condition[];
  conditionsConnector?: ConditionsConnector;
  rules: BasicRule<I, D>[];
  rulesConnector?: 'and' | 'or';
};

// Use D to communicate whether the description is required or not. It is not required for custom user defined rules, where we generate a description if none is provided. D is 'string' by default meaning the description is required.
export type Rule<I = string, D = string> =
  | BasicRule<I, D>
  | ConditionalRule<I, D>;
