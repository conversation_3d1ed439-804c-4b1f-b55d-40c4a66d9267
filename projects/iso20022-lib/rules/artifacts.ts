import type { Rule, XsdElement } from './types';
import { ExpandedFormSetup } from './types/expanded-form-setup.types';

// pacs008
import pacs008_nameMappings from './generated/pacs008/name-mappings.json';
import pacs008_rules from './generated/pacs008/combined-rules.json';
import pacs008_affectedFieldsMap from './generated/pacs008/affected-fields-for-field-value-change.json';
import pacs008_expandedFormSetup from './generated/pacs008/expanded-form-setup.json';

export const simplifiedSchema: XsdElement = pacs008_nameMappings;
export const combinedRules: Rule[] = pacs008_rules as Rule[];
export const pacs008AffectedFields: Record<string, string[]> =
  pacs008_affectedFieldsMap;
export const formSetup: ExpandedFormSetup = pacs008_expandedFormSetup;

export const pacs_008_001_08: {
  simplifiedSchema: XsdElement;
  combinedRules: Rule[];
  affectedFields: Record<string, string[]>;
  formSetup: ExpandedFormSetup;
} = {
  simplifiedSchema: pacs008_nameMappings,
  combinedRules: pacs008_rules as Rule[],
  affectedFields: pacs008_affectedFieldsMap,
  formSetup: pacs008_expandedFormSetup,
};

// pacs002
import pacs002_nameMappings from './generated/pacs002/name-mappings.json';
import pacs002_rules from './generated/pacs002/combined-rules.json';
import pacs002_affectedFieldsMap from './generated/pacs002/affected-fields-for-field-value-change.json';
import pacs002_expandedFormSetup from './generated/pacs002/expanded-form-setup.json';

export const pacs_002_001_10: {
  simplifiedSchema: XsdElement;
  combinedRules: Rule[];
  affectedFields: Record<string, string[]>;
  formSetup: ExpandedFormSetup;
} = {
  simplifiedSchema: pacs002_nameMappings,
  combinedRules: pacs002_rules as Rule[],
  affectedFields: pacs002_affectedFieldsMap,
  formSetup: pacs002_expandedFormSetup,
};

// pacs004
import pacs004_nameMappings from './generated/pacs004/name-mappings.json';
import pacs004_rules from './generated/pacs004/combined-rules.json';
import pacs004_affectedFieldsMap from './generated/pacs004/affected-fields-for-field-value-change.json';
import pacs004_expandedFormSetup from './generated/pacs004/expanded-form-setup.json';

export const pacs_004_001_09: {
  simplifiedSchema: XsdElement;
  combinedRules: Rule[];
  affectedFields: Record<string, string[]>;
  formSetup: ExpandedFormSetup;
} = {
  simplifiedSchema: pacs004_nameMappings,
  combinedRules: pacs004_rules as Rule[],
  affectedFields: pacs004_affectedFieldsMap,
  formSetup: pacs004_expandedFormSetup,
};

// pacs009
import pacs009_nameMappings from './generated/pacs009/name-mappings.json';
import pacs009_rules from './generated/pacs009/combined-rules.json';
import pacs009_affectedFieldsMap from './generated/pacs009/affected-fields-for-field-value-change.json';
import pacs009_expandedFormSetup from './generated/pacs009/expanded-form-setup.json';

export const pacs_009_001_08: {
  simplifiedSchema: XsdElement;
  combinedRules: Rule[];
  affectedFields: Record<string, string[]>;
  formSetup: ExpandedFormSetup;
} = {
  simplifiedSchema: pacs009_nameMappings,
  combinedRules: pacs009_rules as Rule[],
  affectedFields: pacs009_affectedFieldsMap,
  formSetup: pacs009_expandedFormSetup,
};
