import { isObject, isStringArray } from './type.utils';

/**
 * Compare previous and current form group values to find changed fields.
 * @param prev the previous form group value.
 * @param curr the current form group value.
 * @param path the path to the current field in the form group.
 * @returns An array of field IDs that have changed.
 */
export function getChangedFieldIds(
  prev: unknown,
  curr: unknown,
  path: string[] = []
): string[] {
  const changes: string[] = [];

  if (prev === curr) {
    return changes; // No changes
  }

  if (Array.isArray(prev) || Array.isArray(curr)) {
    if (
      !(
        (Array.isArray(prev) && Array.isArray(curr)) ||
        typeof prev === 'undefined' ||
        typeof curr === 'undefined'
      )
    ) {
      throw new Error(
        `Cannot compare array with non-array: ${JSON.stringify(
          prev
        )} vs ${JSON.stringify(curr)}`
      );
    }

    if (typeof prev === 'undefined' || typeof curr === 'undefined') {
      const definedArray = Array.isArray(prev) ? prev : curr;
      changes.push(...getFieldIds(definedArray, path));
    } else if (Array.isArray(prev) && Array.isArray(curr)) {
      if (isStringArray(prev) && isStringArray(curr)) {
        if (
          prev.length !== curr.length ||
          !prev.every((val, i) => val === curr[i])
        ) {
          changes.push(path.join('.'));
        }
      } else if (prev.every(isObject) && curr.every(isObject)) {
        if (prev.length !== curr.length) {
          changes.push(path.join('.'));
        }
        const maxLength = Math.max(prev.length, curr.length);
        for (let i = 0; i < maxLength; i++) {
          changes.push(
            ...getChangedFieldIds(prev[i], curr[i], [...path, `${i}`])
          );
        }
      } else {
        throw new Error(
          `Cannot compare arrays with mixed types: ${JSON.stringify(
            prev
          )} vs ${JSON.stringify(curr)}`
        );
      }
    }
  } else if (isObject(prev) || isObject(curr)) {
    if (
      !(
        (isObject(prev) && isObject(curr)) ||
        typeof prev === 'undefined' ||
        typeof curr === 'undefined'
      )
    ) {
      throw new Error(
        `Cannot compare object with non-object: ${JSON.stringify(
          prev
        )} vs ${JSON.stringify(curr)}`
      );
    }

    if (typeof prev === 'undefined' || typeof curr === 'undefined') {
      const definedObject = isObject(prev) ? prev : curr;
      changes.push(...getFieldIds(definedObject, path));
    } else if (isObject(prev) && isObject(curr)) {
      const keys = new Set([...Object.keys(prev), ...Object.keys(curr)]);
      for (const key of keys) {
        const subPath = [...path, key];
        if (!(key in prev) || !(key in curr)) {
          changes.push(subPath.join('.'));
        } else {
          changes.push(...getChangedFieldIds(prev[key], curr[key], subPath));
        }
      }
    }
  } else if (prev !== curr) {
    changes.push(path.join('.'));
  }

  return changes;
}

function getFieldIds(val: unknown, path: string[] = []): string[] {
  const id = path.join('.');
  const results: string[] = [];

  if (Array.isArray(val)) {
    // Include the array itself
    if (id) results.push(id);

    for (const [index, item] of val.entries()) {
      results.push(...getFieldIds(item, [...path, index.toString()]));
    }
  } else if (isObject(val)) {
    // Do NOT include plain objects
    for (const [key, value] of Object.entries(val)) {
      results.push(...getFieldIds(value, [...path, key]));
    }
  } else {
    if (id) results.push(id);
  }

  return results;
}
