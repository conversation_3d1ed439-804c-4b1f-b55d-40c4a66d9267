export function skipCircularStringify(
  obj: unknown,
  excludeKeys: string[] = []
): string {
  const seen = new WeakSet();

  return JSON.stringify(
    obj,
    function (key, value) {
      const path =
        this && this.__currentPath ? `${this.__currentPath}.${key}` : key;

      // Skip excluded keys
      if (excludeKeys.includes(path)) {
        return undefined;
      }

      // Handle Map
      if (value instanceof Map) {
        return {
          __type: 'Map',
          entries: Array.from(value.entries()),
        };
      }

      // Handle Set
      if (value instanceof Set) {
        return {
          __type: 'Set',
          values: Array.from(value.values()),
        };
      }

      // Handle circular references
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return undefined;
        }
        seen.add(value);

        // Attach current path to child objects so we can track nested paths
        Object.defineProperty(value, '__currentPath', {
          value: path,
          enumerable: false, // prevent it from appearing in JSON
          configurable: true,
        });
      }

      return value;
    },
    2
  );
}
