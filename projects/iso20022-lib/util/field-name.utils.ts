import {
  isNumberString,
  isObject,
  isPresent,
  isStringArray,
} from './type.utils';

export function getFieldId(fieldPrefix: string, fieldName: string): string {
  return `${fieldPrefix}${fieldPrefix ? '.' : ''}${fieldName}`;
}

/**
 * Flattens an array field name by removing indices and joining the parts with a hyphen.
 * @param fieldId the field id to flatten, e.g. "field1.0.nestedField1".
 * @returns the flattened field name, e.g. "field1-nestedField1".
 */
export function flattenFieldId(fieldId: string): string {
  if (!fieldId.includes('.')) {
    return fieldId;
  }

  // Remove array index if present
  const parts = fieldId.split('.').filter((part) => !isNumberString(part));

  return parts.join('-');
}

/**
 * Splits a PascalCase string into an array of words.
 * @param str the string to split.
 * @returns the individual parts.
 * @example
 * splitPascalCase("GroupHeader") // ["Group", "Header"]
 * splitPascalCase("FIToFICustomerCreditTransferV08") // ["FI", "To", "FI", "Customer", "Credit", "Transfer", "V08"]
 * splitPascalCase("CreditTransferTransactionInformation") // ["Credit", "Transfer", "Transaction", "Information"]
 * splitPascalCase("PreviousInstructingAgent1Account") // ["Previous", "Instructing", "Agent1", "Account"]
 * splitPascalCase("IBAN") // ["IBAN"]
 */
export function splitPascalCase(str: string): string[] {
  const result: string[] = [];
  let word = '';

  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    const prev = str[i - 1];
    const next = str[i + 1];

    const isUpper = /[A-Z]/.test(char);
    const isPrevLowerAlphaNum =
      typeof prev !== 'undefined' && /[a-z0-9]/.test(prev);
    const isNextLowerAlphaNum =
      typeof next !== 'undefined' && /[a-z0-9]/.test(next);

    if (i > 0 && isUpper && (isPrevLowerAlphaNum || isNextLowerAlphaNum)) {
      result.push(word);
      word = char;
    } else {
      word += char;
    }
  }

  if (word) result.push(word);

  return result.filter((word) => !!word);
}

/**
 * Find all scopes that are described by the given field names.
 * Scopes are any hyphen-separated prefix of fields present in allFieldNames.
 * A scope is "covered" if every allFieldName that belongs to that scope
 * (i.e. starts with that prefix) is present in `fieldNames`.
 *
 * The function returns the top-most covered scopes: if an ancestor scope is covered,
 * its descendant covered scopes are omitted.
 *
 * @param fieldNames the selected/available field names (may contain extras)
 * @param allFieldNames the universe of fields to consider (defines valid scopes)
 * @returns covered scopes in stable order of first appearance in allFieldNames
 *  * @example
 * findScopes(['field1-nestedField1', 'field1-nestedField2'], ['field1-nestedField1', 'field1-nestedField2', 'field2']) -> ['field1']
 * findScopes(['field1-nestedField1', 'field1-nestedField2', 'field2'], ['field1-nestedField1', 'field1-nestedField2', 'field2']) -> ['field1', 'field2']
 * findScopes(['field1-nestedField1'], ['field1-nestedField1', 'field1-nestedField2', 'field2']) -> []
 * findScopes(['field1-nestedField1', 'field1-nestedField2', 'field2', 'field3-sub1'], ['field1-nestedField1', 'field1-nestedField2', 'field2', 'field3-sub1', 'field3-sub2']) -> ['field1', 'field2']
 * findScopes(['field1-nestedField1-sub1', 'field1-nestedField1-sub2'], ['field1-nestedField1-sub1', 'field1-nestedField1-sub2', 'field1-nestedField2']) -> ['field1-nestedField1']
 */
export function findScopes(
  fieldNames: string[],
  allFieldNames: string[]
): string[] {
  // Map prefix(scope) -> set of all full field names that belong to that prefix
  const scopeToAllFields = new Map<string, Set<string>>();

  // Build prefix map in order of appearance (Map preserves insertion order).
  for (const full of allFieldNames) {
    // Defensive: ignore empty strings and trim whitespace
    const normalized = full.trim();
    if (!normalized) continue;

    const parts = normalized.split('-').filter(Boolean);
    // Generate prefixes of length 1..parts.length
    for (let i = 1; i <= parts.length; i++) {
      const prefix = parts.slice(0, i).join('-');
      let set = scopeToAllFields.get(prefix);
      if (!set) {
        set = new Set<string>();
        scopeToAllFields.set(prefix, set);
      }
      set.add(normalized);
    }
  }

  const given = new Set(fieldNames.map((f) => f.trim()).filter(Boolean));

  // Find all scopes for which every associated full field is present in `given`
  const covered = new Set<string>();
  for (const [scope, fields] of scopeToAllFields.entries()) {
    let allPresent = true;
    for (const f of fields) {
      if (!given.has(f)) {
        allPresent = false;
        break;
      }
    }
    if (allPresent) covered.add(scope);
  }

  // Return all covered scopes in stable order
  const result: string[] = [];
  for (const scope of scopeToAllFields.keys()) {
    if (covered.has(scope)) {
      result.push(scope);
    }
  }
  return result;
}

/**
 * Recursively collects all paths where a value is present.
 * Arrays of strings are treated as leaves.
 */
export function findPresentPaths(
  data: unknown,
  prefix: string[] = [],
  result: Set<string> = new Set()
): Set<string> {
  if (Array.isArray(data)) {
    if (isStringArray(data)) {
      // Treat this array as a leaf
      if (isPresent(data)) {
        result.add(prefix.join('.'));
      }
    } else {
      if (isPresent(data)) {
        result.add(prefix.join('.'));
      }
      // Recurse into complex arrays (arrays of objects)
      data.forEach((item, index) => {
        findPresentPaths(item, [...prefix, String(index)], result);
      });
    }
  } else if (isObject(data)) {
    for (const [key, val] of Object.entries(data)) {
      findPresentPaths(val, [...prefix, key], result);
    }
  } else {
    // Leaf node
    if (isPresent(data)) {
      result.add(prefix.join('.'));
    }
  }

  return result;
}
