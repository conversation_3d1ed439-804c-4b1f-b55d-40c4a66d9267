import { DatePattern, patternRegexMap } from '@helaba/iso20022-lib/types';

export function isPresent(value: unknown, nesting: string = ''): boolean {
  const valueToCheck =
    nesting.length === 0 ? value : getNestedValue(value, nesting);

  if (valueToCheck === null || valueToCheck === undefined) {
    return false;
  }

  if (typeof valueToCheck === 'string') {
    return isNonEmptyString(valueToCheck);
  }
  if (isObject(valueToCheck)) {
    return Object.values(valueToCheck).some((val) => isPresent(val));
  }
  if (Array.isArray(valueToCheck)) {
    return (
      valueToCheck.length > 0 && valueToCheck.some((val) => isPresent(val))
    );
  }

  throw new Error(
    `isPresent called with unsupported type: ${typeof valueToCheck}. Only strings, objects, and arrays are supported.`
  );
}

function getNestedValue(val: unknown, nesting: string): unknown {
  if (
    !nesting ||
    val === null ||
    val === undefined ||
    typeof val !== 'object'
  ) {
    return val;
  }

  const nestingOptions = getProgressiveSubstrings(nesting);

  for (const substring of nestingOptions) {
    if (
      (Array.isArray(val) && !val.some((entry) => substring in entry)) ||
      (!Array.isArray(val) && !(substring in val))
    ) {
      continue;
    }
    if (Array.isArray(val)) {
      return val.map((item) => getNestedValue(item, nesting)).flat();
    }
    const nestedVal = (val as Record<string, unknown>)[substring];
    const truncatedSubstring = nesting.slice(substring.length + 1);
    return getNestedValue(nestedVal, truncatedSubstring);
  }

  return undefined;
}

export function getProgressiveSubstrings(fieldName: string): string[] {
  const parts = fieldName.split('-');
  return parts.map((_, i) => parts.slice(0, i + 1).join('-')).filter(Boolean);
}

export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.trim() !== '';
}

export function isNumberString(value: string): boolean {
  return /^\d+$/.test(value);
}

export function isObject(obj: unknown): obj is Record<string, unknown> {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj);
}

export function isStringArray(arr: unknown): arr is string[] {
  return (
    typeof arr !== 'undefined' &&
    arr !== null &&
    Array.isArray(arr) &&
    arr.every((element) => typeof element === 'string')
  );
}

export function retrieveNumber(value: unknown): number {
  if (typeof value !== 'string') {
    throw new Error(`Expected a string but got: ${typeof value}`);
  }

  const parsedValue = Number(value);
  if (isNaN(parsedValue)) {
    throw new Error(`Cannot convert value to number: ${value}`);
  }

  return parsedValue;
}

export function isValidLeafFormValue(
  value: unknown
): value is string | string[] {
  return typeof value === 'string' || isStringArray(value);
}

export function isValidDate(value: unknown): value is Date {
  return value instanceof Date && !isNaN(value.getTime());
}

/**
 * Checks if the given value is a complete date string according to the specified pattern.
 * @param value The date string to validate.
 * @param pattern The expected date pattern.
 * @returns True if the value is a complete date string in the specified pattern, false otherwise.
 */
export function isCompleteDateString(
  value: string,
  pattern: DatePattern
): boolean {
  const regex = patternRegexMap[pattern];
  return regex.test(value);
}

// The format produced here matches the format currently expected by ISO 20022 date fields.
// NOTE: When changing the output format, also adapt the function 'parseIsoDateWithOffset' accordingly.
export function formatDateToIsoOffset(
  date: Date,
  includeTime: boolean,
  timeOnly: boolean
): string {
  if (!isValidDate(date)) {
    throw new Error('Invalid date');
  }

  const pad = (n: number, len = 2) => String(n).padStart(len, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());

  const tzOffsetMinutes = date.getTimezoneOffset();

  if (!includeTime) {
    // Only date + optional offset
    if (tzOffsetMinutes === 0) {
      return `${year}-${month}-${day}Z`;
    }
    const sign = tzOffsetMinutes > 0 ? '-' : '+';
    const offsetHours = pad(Math.floor(Math.abs(tzOffsetMinutes) / 60));
    const offsetMinutes = pad(Math.abs(tzOffsetMinutes) % 60);

    return `${year}-${month}-${day}${sign}${offsetHours}:${offsetMinutes}`;
  }

  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  if (tzOffsetMinutes === 0) {
    return timeOnly
      ? `${hours}:${minutes}:${seconds}Z`
      : `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
  }

  const sign = tzOffsetMinutes > 0 ? '-' : '+';
  const offsetHours = pad(Math.floor(Math.abs(tzOffsetMinutes) / 60));
  const offsetMinutes = pad(Math.abs(tzOffsetMinutes) % 60);

  return timeOnly
    ? `${hours}:${minutes}:${seconds}${sign}${offsetHours}:${offsetMinutes}`
    : `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${sign}${offsetHours}:${offsetMinutes}`;
}

export function parseIsoDateWithOffset(str: string): Date {
  // Regex buckets: [date, time, offset]
  const dateTimeRegex =
    /^(?:(\d{4}-\d{2}-\d{2}))?(?:T?(\d{2}:\d{2}:\d{2}))?(Z|[+-]\d{2}:\d{2})?$/;

  const match = str.match(dateTimeRegex);
  if (!match) {
    throw new Error(`Unrecognized ISO-like string: ${str}`);
  }

  let [, datePart, timePart, offsetPart] = match;

  // --- Defaults ---
  if (!datePart) {
    // time-only: default to 1970-01-01
    datePart = '1970-01-01';
  }
  if (!timePart) {
    // date-only: default to midnight
    timePart = '00:00:00';
  }
  if (!offsetPart) {
    // if no explicit zone: treat as Z (UTC)
    offsetPart = 'Z';
  }

  // Normalize to a full ISO string
  const isoString = `${datePart}T${timePart}${offsetPart}`;

  const d = new Date(isoString);
  if (isNaN(d.getTime())) {
    throw new Error(`Invalid Date from string: ${isoString}`);
  }
  return d;
}
