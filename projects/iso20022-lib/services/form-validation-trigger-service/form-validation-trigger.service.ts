import { inject, Injectable } from '@angular/core';
import { AbstractControl, FormGroup, ValidationErrors } from '@angular/forms';
import {
  getFieldIdForControl,
  getFieldIdsFromFormGroup,
  performCustomValidation,
} from '@helaba/iso20022-lib/angular-utils';
import { Rule } from '@helaba/iso20022-lib/rules';
import { FormErrorsService } from '../form-errors-service';
import { FormDataService } from '../form-data-service';

/**
 * Manually trigger validation for a specific control or for a form group.
 * This service maintains a 'shouldRunCustomValidators' flag that it sets to 'true' for manual validations and that is respected in the custom validator functions (see FormRulesDirective).
 */
@Injectable({
  providedIn: 'root',
})
export class FormValidationTriggerService {
  formErrorsService = inject(FormErrorsService);
  formDataService = inject(FormDataService);

  allFormGroups = this.formDataService.allFormGroups;

  public validateControl(control: AbstractControl) {
    if (!control) {
      throw new Error(
        'FormRulesDirective: Provided control is undefined or null. Ensure that a valid AbstractControl is passed to validateControl.'
      );
    }
    this.triggerCustomValidation(control);
  }

  public validateForm(formGroup: FormGroup) {
    const allFieldIds = getFieldIdsFromFormGroup(formGroup);
    for (const fieldId of allFieldIds) {
      const control = formGroup.get(fieldId);
      if (!control) {
        throw new Error(
          `FormRulesDirective: Control for field "${fieldId}" not found in the form group. Ensure that the field exists in the form group.`
        );
      }
      this.triggerCustomValidation(control);
    }
    // Finally, check the overall form validity to update validity state
    this.triggerCustomValidation(formGroup);
  }

  #shouldRunCustomValidators = false;

  private triggerCustomValidation(control: AbstractControl) {
    // Set flag to allow validators to run
    this.#shouldRunCustomValidators = true;

    try {
      console.debug(
        `Triggering custom validation for ${
          control.parent
            ? 'control with fieldId ' + getFieldIdForControl(control)
            : 'root control'
        }`
      );
      control.updateValueAndValidity({ onlySelf: true, emitEvent: true }); // onlySelf to avoid triggering parent updates. 'emitEvent' cannot be false as that would suppress statusChanges which we need for showing errors.
    } finally {
      // Reset flag to prevent validators from running on normal Angular validation cycles
      this.#shouldRunCustomValidators = false;
    }
  }

  public performCustomValidationIfAllowed(
    fieldId: string,
    control: AbstractControl,
    rules: Rule<string, undefined>[],
    pageKey: string
  ): ValidationErrors | null {
    if (!this.#shouldRunCustomValidators) {
      // Return the latest value stored in the FormErrorsService. If we would just return null here, automatic updates would clear existing errors.
      const pageFieldErrors = this.formErrorsService.fieldErrors()[pageKey];
      if (!pageFieldErrors) {
        return null;
      }
      if (pageFieldErrors.document.has(fieldId)) {
        return pageFieldErrors.document.get(fieldId) ?? null;
      } else if (pageFieldErrors.bah.has(fieldId)) {
        return pageFieldErrors.bah.get(fieldId) ?? null;
      } else {
        return null;
      }
    }
    return performCustomValidation(
      fieldId,
      control,
      rules,
      this.allFormGroups()
    );
  }
}
