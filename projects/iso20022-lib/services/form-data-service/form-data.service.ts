import { Injectable, signal, untracked } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MultiPageFormGroups } from '@helaba/iso20022-lib/types';
import { FormContextService } from '../form-context-service';
import { MultiPageFormContexts } from './form-data.types';

/**
 * Store multiple form groups, e.g. for each page of a multi-step form.
 */
@Injectable({
  providedIn: 'root',
})
export class FormDataService {
  #allFormGroups = signal<MultiPageFormGroups>({});
  #allFormContexts = signal<MultiPageFormContexts>({});

  allFormGroups = this.#allFormGroups.asReadonly();
  allFormContexts = this.#allFormContexts.asReadonly();

  /**
   * Merge all page data into a single object
   */
  setPageData(
    pageKey: string,
    formData: {
      documentFormGroup: FormGroup;
      documentContext: FormContextService;
      bahFormGroup: FormGroup;
      bahContext: FormContextService;
    }
  ): void {
    // This means that you cannot use allFormGroups() or allFormContexts() in effects. Otherwise leads to an infinite loop: effect depends on #allFormGroups() -> calls set() -> updates #allFormGroups() -> effect triggered again -> ...
    this.#allFormGroups.set({
      ...this.#allFormGroups(),
      [pageKey]: {
        documentFormGroup: formData.documentFormGroup,
        bahFormGroup: formData.bahFormGroup,
      },
    });
    this.#allFormContexts.set({
      ...this.#allFormContexts(),
      [pageKey]: {
        documentContext: formData.documentContext,
        bahContext: formData.bahContext,
      },
    });
  }
}
