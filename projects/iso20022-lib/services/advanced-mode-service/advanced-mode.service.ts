import { Injectable, signal } from '@angular/core';

/**
 * Store multiple form groups, e.g. for each page of a multi-step form.
 */
@Injectable({
  providedIn: 'root',
})
export class AdvancedModeService {
  #isAdvancedMode = signal<boolean>(false);
  #advancedModeFields = signal<Set<string>>(new Set());
  #advancedModeScopes = signal<Set<string>>(new Set());

  isAdvancedMode = this.#isAdvancedMode.asReadonly();
  advancedModeFields = this.#advancedModeFields.asReadonly();
  advancedModeScopes = this.#advancedModeScopes.asReadonly();

  setIsAdvancedMode(isAdvancedMode: boolean): void {
    this.#isAdvancedMode.set(isAdvancedMode);
  }
  setAdvancedModeFields(fieldIds: string[]): void {
    this.#advancedModeFields.set(new Set(fieldIds));
  }
  setAdvancedModeScopes(scopes: string[]): void {
    this.#advancedModeScopes.set(new Set(scopes));
  }
}
