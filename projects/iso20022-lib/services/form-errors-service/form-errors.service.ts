import { computed, Injectable, signal } from '@angular/core';
import { ValidationErrors } from '@angular/forms';
import { skipCircularStringify } from '@helaba/iso20022-lib/util';
import { MultiPageFormErrors } from '@helaba/iso20022-lib/types';

/**
 * Allows storing errors for multiple FormGroups and making them available outside of the form context.
 */
@Injectable({
  providedIn: 'root',
})
export class FormErrorsService {
  #fieldErrors = signal<MultiPageFormErrors>({});
  #errorMessages = signal<Map<string, string>>(new Map());
  #erroneousPages = signal<Set<string>>(new Set());

  fieldErrors = this.#fieldErrors.asReadonly();
  errorMessages = this.#errorMessages.asReadonly();
  erroneousPages = this.#erroneousPages.asReadonly();

  erroneousFieldIds = computed<Set<string>>(() => {
    const fieldErrors = this.#fieldErrors();
    const mergedFieldErrors = new Map<string, ValidationErrors | null>();
    for (const fieldErrorsForPage of Object.values(fieldErrors)) {
      const documentFormGroupErrors = fieldErrorsForPage.document;
      const bahFormGroupErrors = fieldErrorsForPage.bah;
      for (const [fieldId, errors] of documentFormGroupErrors.entries()) {
        mergedFieldErrors.set(fieldId, errors);
      }
      for (const [fieldId, errors] of bahFormGroupErrors.entries()) {
        mergedFieldErrors.set(fieldId, errors);
      }
    }
    return new Set(
      Array.from(mergedFieldErrors.entries())
        .filter(
          ([_, errors]) => errors !== null && Object.keys(errors).length > 0
        )
        .map(([fieldId]) => fieldId)
    );
  });
  erroneousScopes = computed<Set<string>>(() => {
    const erroneousFieldIds = this.erroneousFieldIds();

    const scopes = new Set<string>();
    for (const fieldId of erroneousFieldIds) {
      const fieldIdParts = fieldId.split('.');
      let prefix = '';

      for (const fieldIdPart of fieldIdParts) {
        for (const part of fieldIdPart.split('-')) {
          prefix = prefix.endsWith('.')
            ? `${prefix}${part}`
            : prefix
            ? `${prefix}-${part}`
            : part;
          scopes.add(prefix);
        }
        prefix += '.';
      }
    }

    return scopes;
  });

  updateFormGroupErrors(
    pageKey: string,
    formGroupType: 'document' | 'bah',
    formGroupErrors: Map<string, ValidationErrors | null>
  ) {
    console.debug(
      'Setting fieldErrors for',
      pageKey,
      `(${formGroupType})`,
      'to',
      skipCircularStringify(formGroupErrors)
    );
    const errorsToRetain =
      formGroupType === 'document'
        ? this.#fieldErrors()[pageKey]?.bah
        : this.#fieldErrors()[pageKey]?.document;
    const errorsToRetainHasErrors = errorsToRetain
      ? Array.from(errorsToRetain.values()).some(
          (errors) => errors && Object.keys(errors).length > 0
        )
      : false;
    const newErrorsHasErrors = Array.from(formGroupErrors.values()).some(
      (errors) => errors && Object.keys(errors).length > 0
    );
    this.#fieldErrors.set({
      ...this.#fieldErrors(),
      [pageKey]: {
        document:
          formGroupType === 'document'
            ? formGroupErrors
            : errorsToRetain ?? new Map(),
        bah:
          formGroupType === 'bah'
            ? formGroupErrors
            : errorsToRetain ?? new Map(),
      },
    });
    const currentErroneousPages = new Set(this.#erroneousPages());
    let addedOrRemovedPage = false;
    if (errorsToRetainHasErrors || newErrorsHasErrors) {
      if (!currentErroneousPages.has(pageKey)) {
        console.debug('Adding', pageKey, 'to erroneousPages');
        currentErroneousPages.add(pageKey);
        addedOrRemovedPage = true;
      }
    } else {
      if (currentErroneousPages.has(pageKey)) {
        console.debug('Removing', pageKey, 'from erroneousPages');
        currentErroneousPages.delete(pageKey);
        addedOrRemovedPage = true;
      }
    }
    if (addedOrRemovedPage) {
      console.debug(
        'Setting erroneousPages for',
        pageKey,
        'to',
        skipCircularStringify(currentErroneousPages)
      );
      this.#erroneousPages.set(currentErroneousPages);
    }
  }

  setErrorMessages(errorMessages: Map<string, string>) {
    this.#errorMessages.set(errorMessages);
  }
}
