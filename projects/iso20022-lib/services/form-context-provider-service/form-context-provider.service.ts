import { Injectable, signal } from '@angular/core';
import { FormContextService } from '../form-context-service';

/**
 * We cannot just inject the FormContextService directly, because it is instantiated in 'initializeMultiPageForm()' in the FormSetupService for each form group and there is no way to inject that instantiated instance without loosing signal reactivity.
 * Changes to allFormData() (e.g. because the mode is switched) would not cause the injected FormContextService instance to be updated.
 * So we use this provider service to store the current FormContextService instance in a signal, which can then be injected where needed without baking (just use 'providers: [FormContextProviderService]' in the parent component and keep the formContextService updated in there).
 */
@Injectable()
export class FormContextProviderService {
  #currentFormContext = signal<FormContextService | null>(null);

  currentFormContext = this.#currentFormContext.asReadonly();

  setCurrentFormContext(newFormContext: FormContextService): void {
    this.#currentFormContext.set(newFormContext);
  }
}
