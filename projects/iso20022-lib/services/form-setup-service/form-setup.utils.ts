import { AbstractControl, FormGroup, ValidationErrors } from '@angular/forms';
import { getControlsAndFieldIdsFromFormGroup } from '@helaba/iso20022-lib/angular-utils';
import { MultiPageFormGroups } from '@helaba/iso20022-lib/types';

export function getFormGroupErrors(
  formGroup: FormGroup
): Map<string, ValidationErrors | null> {
  const controlsAndFieldIds = getControlsAndFieldIdsFromFormGroup(formGroup);
  const formGroupErrors: Map<string, ValidationErrors | null> = new Map();
  for (const { control, fieldId } of controlsAndFieldIds) {
    const errors = control.errors;
    formGroupErrors.set(fieldId, errors);
  }

  return formGroupErrors;
}

export function getAllControlsFieldIds(allFormGroups: MultiPageFormGroups): {
  fieldId: string;
  control: AbstractControl;
  formGroup: FormGroup;
}[] {
  const flattenedFormGroups = Object.values(allFormGroups)
    .map((page) => [page.documentFormGroup, page.bahFormGroup])
    .flat();

  const allControlsAndFieldIds: {
    fieldId: string;
    control: AbstractControl;
    formGroup: FormGroup;
  }[] = [];

  for (const formGroup of flattenedFormGroups) {
    allControlsAndFieldIds.push(
      ...getControlsAndFieldIdsFromFormGroup(formGroup)
    );
  }

  return allControlsAndFieldIds;
}
