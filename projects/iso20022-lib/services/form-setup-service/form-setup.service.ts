import { Subscription } from 'rxjs';
import { inject, Injectable } from '@angular/core';
import { Rule } from '@helaba/iso20022-lib/rules';
import { findRelevantRules } from '@helaba/iso20022-lib/angular-utils';
import {
  AbstractControl,
  FormGroup,
  NonNullableFormBuilder,
  ValidationErrors,
} from '@angular/forms';
import {
  CompletionPageKey,
  FormStep,
  MultiPageFormGroups,
} from '@helaba/iso20022-lib/types';
import {
  flattenFieldId,
  getChangedFieldIds,
  isObject,
  skipCircularStringify,
} from '@helaba/iso20022-lib/util';
import { FormValidationTriggerService } from '../form-validation-trigger-service';
import { FormDataService } from '../form-data-service';
import { FormErrorsService } from '../form-errors-service';
import { getAllControlsFieldIds, getFormGroupErrors } from './form-setup.utils';
import { FormContextService } from '../form-context-service';

/**
 * Contains functionality to set up a multi-page form, initializing form groups, applying custom validator functions to the form controls, and setting up subscriptions to form value changes.
 */
@Injectable({
  providedIn: 'root',
})
export class FormSetupService {
  formValidationTriggerService = inject(FormValidationTriggerService);
  formDataService = inject(FormDataService);
  formErrorsService = inject(FormErrorsService);
  fb = inject(NonNullableFormBuilder);

  validationRules: Rule<string, undefined>[] = [];

  #activeSubscriptions: Subscription[] = [];
  #previousFormValues: Record<string, { document: any; bah: any }> = {}; // Store previous form values for comparison
  #previousFormErrors: Record<string, { document: string; bah: string }> = {}; // Store previous form errors for comparison

  initializeMultiPageForm(
    validationRules: Rule<string, undefined>[],
    formSteps: FormStep<any, any>[],
    affectedFields: Record<string, string[]>,
    existingFormGroups?: MultiPageFormGroups
  ) {
    this.validationRules = validationRules;

    for (const step of formSteps) {
      const pageKey = step.key;
      if (pageKey === CompletionPageKey.COMPLETION) {
        continue;
      }

      const documentFormGroupInitializer = step.documentFormGroupInitializer;
      if (!documentFormGroupInitializer) {
        throw new Error(
          `Form group initializer is missing for step with key: ${pageKey}`
        );
      }
      const documentFormGroup: FormGroup =
        existingFormGroups && existingFormGroups[pageKey]?.documentFormGroup
          ? existingFormGroups[pageKey].documentFormGroup
          : documentFormGroupInitializer(this.fb);
      // Apply custom ValidatorFn to all form controls
      for (const [fieldName, control] of Object.entries(
        documentFormGroup.controls
      )) {
        this.setCustomValidator(fieldName, control, pageKey);
      }
      const documentContext = new FormContextService();
      documentContext.setFormGroup(documentFormGroup);
      documentContext.setFormValue(documentFormGroup.value);

      const bahFormGroupInitializer = step.bahFormGroupInitializer;
      if (!bahFormGroupInitializer) {
        throw new Error(
          `BAH form group initializer is missing for step with key: ${pageKey}`
        );
      }
      const bahFormGroup: FormGroup =
        existingFormGroups && existingFormGroups[pageKey]?.bahFormGroup
          ? existingFormGroups[pageKey].bahFormGroup
          : bahFormGroupInitializer(this.fb);
      // Apply custom ValidatorFn to all form controls
      for (const [fieldName, control] of Object.entries(
        bahFormGroup.controls
      )) {
        this.setCustomValidator(fieldName, control, pageKey);
      }
      const bahContext = new FormContextService();
      bahContext.setFormGroup(bahFormGroup);
      bahContext.setFormValue(bahFormGroup.value);

      this.formDataService.setPageData(pageKey, {
        documentFormGroup,
        documentContext,
        bahFormGroup,
        bahContext,
      });

      // Initialize the 'previousFormValues'
      this.#previousFormValues[pageKey] = {
        document: documentFormGroup.value,
        bah: bahFormGroup.value,
      };
      // Initialize the 'previousFormErrors'
      const initialDocumentFormGroupErrors: Map<
        string,
        ValidationErrors | null
      > = getFormGroupErrors(documentFormGroup);
      const initialBahFormGroupErrors: Map<string, ValidationErrors | null> =
        getFormGroupErrors(bahFormGroup);
      this.#previousFormErrors[pageKey] = {
        document: skipCircularStringify(initialDocumentFormGroupErrors),
        bah: skipCircularStringify(initialBahFormGroupErrors),
      };

      this.#activeSubscriptions.push(
        documentFormGroup.valueChanges.subscribe((currentValues) => {
          this.handleFormGroupValueChanges(
            currentValues,
            pageKey,
            affectedFields,
            'document',
            documentContext
          );
        })
      );

      this.#activeSubscriptions.push(
        documentFormGroup.statusChanges.subscribe(() => {
          this.handleFormGroupStatusChanges(
            documentFormGroup,
            pageKey,
            'document'
          );
        })
      );

      this.#activeSubscriptions.push(
        bahFormGroup.valueChanges.subscribe((currentValues) => {
          this.handleFormGroupValueChanges(
            currentValues,
            pageKey,
            affectedFields,
            'bah',
            bahContext
          );
        })
      );

      this.#activeSubscriptions.push(
        bahFormGroup.statusChanges.subscribe(() => {
          this.handleFormGroupStatusChanges(bahFormGroup, pageKey, 'bah');
        })
      );
    }
  }

  private handleFormGroupValueChanges(
    currentValues: unknown,
    pageKey: string,
    affectedFields: Record<string, string[]>,
    formGroupType: 'document' | 'bah',
    formContext: FormContextService
  ) {
    if (!isObject(currentValues)) {
      throw new Error('Expected form values to be an object');
    }

    const changedFieldIds: string[] = getChangedFieldIds(
      this.#previousFormValues[pageKey][formGroupType],
      currentValues
    );
    this.#previousFormValues[pageKey][formGroupType] = currentValues; // Update previous values for next comparison

    if (changedFieldIds.length === 0) {
      // No actual value changes, so we skip processing
      return;
    }

    formContext.setFormValue(currentValues);

    console.debug('CHANGED FIELD IDS', changedFieldIds);

    // Figure out which other fields are connected to the changed fields via conditional rules.
    const affectedFlattenedFieldIdsForValueChanges: Set<string> = new Set(
      changedFieldIds.flatMap(
        (changedFieldId) => affectedFields[flattenFieldId(changedFieldId)] || []
      )
    );

    console.debug('AFFECTED FLATTENED FIELD IDS', [
      ...affectedFlattenedFieldIdsForValueChanges,
    ]);

    // We need to recompute this each time to get the latest setup
    const allControlsAndFieldIds = getAllControlsFieldIds(
      this.formDataService.allFormGroups()
    );

    for (const { fieldId, control, formGroup } of allControlsAndFieldIds) {
      if (
        affectedFlattenedFieldIdsForValueChanges.has(flattenFieldId(fieldId))
      ) {
        this.formValidationTriggerService.validateControl(control);
        // Validate the respective form group (might not be the parent of the control whose value changed which is why we need a manual validation here).
        this.formValidationTriggerService.validateControl(formGroup);
      }
    }
  }

  private handleFormGroupStatusChanges(
    formGroup: FormGroup,
    pageKey: string,
    formGroupType: 'document' | 'bah'
  ) {
    const formGroupErrors: Map<string, ValidationErrors | null> =
      getFormGroupErrors(formGroup);
    if (
      this.#previousFormErrors[pageKey][formGroupType] ===
      skipCircularStringify(formGroupErrors)
    ) {
      // No actual error changes, so we skip processing
      return;
    }
    this.#previousFormErrors[pageKey][formGroupType] =
      skipCircularStringify(formGroupErrors); // Update previous errors for next comparison

    // Update the errors in the central FormErrorsService.
    this.formErrorsService.updateFormGroupErrors(
      pageKey,
      formGroupType,
      formGroupErrors
    );
  }

  cleanupFormSetup() {
    this.#activeSubscriptions.forEach((sub) => sub.unsubscribe());
    this.#activeSubscriptions = [];
    this.#previousFormValues = {};
  }

  setCustomValidator(
    fieldId: string,
    control: AbstractControl,
    pageKey: string
  ) {
    const rulesForField = findRelevantRules(
      fieldId,
      control,
      this.validationRules
    );

    if (rulesForField.length > 0) {
      control.setValidators(
        this.getCustomValidatorFn(fieldId, rulesForField, pageKey)
      );
    }
  }

  private getCustomValidatorFn(
    fieldId: string,
    rules: Rule<string, undefined>[],
    pageKey: string
  ) {
    // This custom validator function is called often by Angular. We only want to perform validation if it was triggered manually via the FormValidationTriggerService ('triggerCustomValidation').
    return (control: AbstractControl): ValidationErrors | null => {
      return this.formValidationTriggerService.performCustomValidationIfAllowed(
        fieldId,
        control,
        rules,
        pageKey
      );
    };
  }
}
