import { Injectable, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { findPresentPaths, flattenFieldId } from '@helaba/iso20022-lib/util';
import { getFieldIdsFromFormGroup } from '@helaba/iso20022-lib/angular-utils';

/**
 * Store the current form group's context:
 * - 'formValue' updated for each value change.
 * Allows nested child components to read their form group's context.
 */
@Injectable()
export class FormContextService {
  #formGroup = signal<FormGroup | null>(null);
  #formValue = signal<Record<string, unknown>>({});
  #presentFieldIds = signal<Set<string>>(new Set());

  formGroup = this.#formGroup.asReadonly();
  formValue = this.#formValue.asReadonly();
  presentFieldIds = this.#presentFieldIds.asReadonly();

  setFormGroup(newFormGroup: FormGroup) {
    this.#formGroup.set(newFormGroup);
  }

  setFormValue(newFormValue: Record<string, unknown>) {
    this.#formValue.set(newFormValue);
    this.#presentFieldIds.set(findPresentPaths(newFormValue));
  }

  getFieldIdsToShow(
    isReadOnly: boolean,
    isAdvancedMode: boolean,
    advancedModeFields: Set<string>
  ): Set<string> {
    const formGroup = this.#formGroup();
    if (!formGroup) {
      return new Set<string>();
    }

    const presentFieldIds = this.#presentFieldIds();

    const allFieldIds = getFieldIdsFromFormGroup(formGroup);

    if (isAdvancedMode && !isReadOnly) {
      // editable advanced mode → show all fields
      return new Set(allFieldIds);
    }

    if (isAdvancedMode && isReadOnly) {
      // read-only advanced mode → show present fields only
      return presentFieldIds;
    }

    const basicFieldIds = new Set(
      allFieldIds.filter(
        (fieldId) => !advancedModeFields.has(flattenFieldId(fieldId))
      )
    );

    if (!isAdvancedMode && !isReadOnly) {
      // editable basic mode → show basic fields
      return basicFieldIds;
    }

    // read-only basic mode → validate and show present fields
    for (const presentFieldId of presentFieldIds) {
      if (!basicFieldIds.has(presentFieldId)) {
        throw new Error(
          `Unexpected mismatch: We are in basic mode and there is a present field ID "${presentFieldId}" that is not part of the basic fields.`
        );
      }
    }
    return presentFieldIds;
  }
}
