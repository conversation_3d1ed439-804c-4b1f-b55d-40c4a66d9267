import { EnvironmentProviders, Provider, signal } from '@angular/core';
import {
  ISO_LIB_DEFAULT_LABELS,
  ISO_LIB_LABELS,
  ISO_LIB_CLIENT_LABELS,
  Labels,
} from './tokens';

export function provideIsoLibLabels(
  labels?: Partial<Labels>
): Array<Provider | EnvironmentProviders> {
  const clientLabels = { ...(labels ?? {}) };

  const defaults: Labels = {};

  // Create a reactive signal that computes the merged labels
  const labelsSig = signal<Labels>(defaults);

  // Apply shallow merge (keys in clientLabels override defaults)
  labelsSig.set({ ...defaults, ...(clientLabels as Labels) });

  return [
    { provide: ISO_LIB_DEFAULT_LABELS, useValue: defaults },
    { provide: ISO_LIB_CLIENT_LABELS, useValue: clientLabels },
    { provide: ISO_LIB_LABELS, useValue: labelsSig },
  ];
}
