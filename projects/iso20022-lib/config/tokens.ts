import { InjectionToken, Signal } from '@angular/core';

export type Labels = Record<string, string>;

export const ISO_LIB_DEFAULT_LABELS = new InjectionToken<Labels>(
  'ISO_LIB_DEFAULT_LABELS'
);
export const ISO_LIB_CLIENT_LABELS = new InjectionToken<Partial<Labels>>(
  'ISO_LIB_USER_LABELS'
);

// Effective labels signal (merged).
export const ISO_LIB_LABELS = new InjectionToken<Signal<Labels>>(
  'ISO_LIB_LABELS'
);
