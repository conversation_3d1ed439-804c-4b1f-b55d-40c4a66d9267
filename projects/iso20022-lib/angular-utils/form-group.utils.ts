import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
} from '@angular/forms';
import { isFormArray, isFormGroup, isLeafControl } from './form-type.utils';

/**
 * Gets the full path of a form control within a form structure
 */
export function getFieldIdForControl(control: AbstractControl): string | null {
  if (!control.parent) {
    return null; // This is the root control
  }

  const path: string[] = [];
  let currentControl = control;

  // Traverse up the parent chain
  while (currentControl.parent) {
    const parent = currentControl.parent;

    if (isFormGroup(parent)) {
      // Find the key in FormGroup
      const key = Object.keys(parent.controls).find(
        (k) => parent.controls[k] === currentControl
      );
      if (key !== undefined) {
        path.unshift(key);
      }
    } else if (isFormArray(parent)) {
      // Find the index in FormArray
      const index = parent.controls.findIndex((c) => c === currentControl);
      if (index !== -1) {
        path.unshift(index.toString());
      }
    }

    currentControl = parent;
  }

  return path.length > 0 ? path.join('.') : null;
}

/**
 *
 * @param flattenedFieldId the flattened field ID to find, e.g. "field1-nestedField1".
 * @param formGroup the FormGroup to search in.
 * @returns the AbstractControls matching the flattened field ID.
 */
export function findControlsByFlattenedFieldId(
  flattenedFieldId: string,
  formGroup: FormGroup
): (
  | FormControl<string | null | undefined>
  | FormArray<FormControl<string | null | undefined> | FormGroup>
)[] {
  const rootLevelFieldNames = Object.keys(formGroup.controls);

  if (rootLevelFieldNames.includes(flattenedFieldId)) {
    const control = formGroup.controls[flattenedFieldId];
    if (!control) {
      throw new Error(
        `Cannot find control for field name "${flattenedFieldId}" in form group.`
      );
    }
    if (!isLeafControl(control) && !isFormArray(control)) {
      throw new Error(
        `Expected control for field name "${flattenedFieldId}" to be a FormControl or FormArray, but it is not.`
      );
    }
    return [control];
  }

  const parts = flattenedFieldId.split('-');
  if (parts.length === 0) {
    throw new Error(`'flattenedFieldName' is empty: "${flattenedFieldId}".`);
  }
  const rootLevelFieldName = parts.reduce<string>((acc, part) => {
    if (rootLevelFieldNames.includes(acc)) {
      return acc;
    }
    return acc ? `${acc}-${part}` : part;
  }, '');

  const control = formGroup.get(rootLevelFieldName);
  if (!control) {
    return [];
  }

  // We go one level deeper and only need the remaining field names to find the control.
  const remainingFlattenedFieldId = flattenedFieldId.slice(
    rootLevelFieldName.length + 1
  ); // Remove the root level field name + 1 character for the hyphen

  if (isFormGroup(control)) {
    return findControlsByFlattenedFieldId(remainingFlattenedFieldId, control);
  }

  if (isFormArray(control)) {
    const controls = [];
    for (const [index, nestedFormGroup] of control.controls.entries()) {
      if (!isFormGroup(nestedFormGroup)) {
        throw new Error(
          `Expected control at index ${index} in form array "${rootLevelFieldName}" to be a FormGroup, but it is not.`
        );
      }
      controls.push(
        ...findControlsByFlattenedFieldId(
          remainingFlattenedFieldId,
          nestedFormGroup
        )
      );
    }
    return controls;
  }

  throw new Error(
    `Expected control for field name "${flattenedFieldId}" to be a FormControl, FormGroup, or FormArray, but it is none of those.`
  );
}

/**
 * Generic recursive traversal of a FormGroup.
 *
 * @param formGroup - The starting FormGroup
 * @param path - Current path prefix (used internally for recursion)
 * @param visitor - Callback invoked for each control
 */
function traverseFormGroup<T>(
  formGroup: FormGroup,
  path: string,
  visitor: (args: { fieldId: string; control: AbstractControl }) => T[]
): T[] {
  if (!formGroup) return [];

  const results: T[] = [];

  for (const [fieldName, control] of Object.entries(formGroup.controls)) {
    const currentPath = path ? `${path}.${fieldName}` : fieldName;

    if (isLeafControl(control)) {
      results.push(...visitor({ fieldId: currentPath, control }));
    } else if (isFormGroup(control)) {
      results.push(...traverseFormGroup(control, currentPath, visitor));
    } else if (isFormArray(control)) {
      // Include the array itself if desired
      results.push(...visitor({ fieldId: currentPath, control }));

      control.controls.forEach((item, index) => {
        const arrayPath = `${currentPath}.${index}`;
        if (isFormGroup(item)) {
          results.push(...traverseFormGroup(item, arrayPath, visitor));
        } else {
          // For arrays of simple controls
          results.push(...visitor({ fieldId: arrayPath, control: item }));
        }
      });
    }
  }

  return results;
}

export function getFieldIdsFromFormGroup(formGroup: FormGroup): string[] {
  return traverseFormGroup(formGroup, '', ({ fieldId }) => [fieldId]);
}

export function getControlsAndFieldIdsFromFormGroup(
  formGroup: FormGroup
): { fieldId: string; control: AbstractControl; formGroup: FormGroup }[] {
  return traverseFormGroup(formGroup, '', ({ fieldId, control }) => [
    { fieldId, control },
  ]).map((item) => ({ ...item, formGroup })); // add the root formGroup to each item
}
