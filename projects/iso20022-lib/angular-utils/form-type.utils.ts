import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
} from '@angular/forms';

export function isFormGroup(control: AbstractControl): control is FormGroup {
  return (
    control instanceof FormGroup &&
    control.value !== null &&
    control.value !== undefined &&
    typeof control.value === 'object' &&
    !Array.isArray(control.value) &&
    'controls' in control &&
    control.controls !== null &&
    control.controls !== undefined &&
    typeof control.controls === 'object' &&
    !Array.isArray(control.controls)
  );
}

export function isFormArray(control: AbstractControl): control is FormArray {
  return (
    control instanceof FormArray &&
    control.value !== null &&
    control.value !== undefined &&
    Array.isArray(control.value) &&
    'controls' in control &&
    control.controls !== null &&
    control.controls !== undefined &&
    Array.isArray(control.controls)
  );
}

export function isLeafControl(
  control: AbstractControl
): control is FormControl<string | null | undefined> {
  return (
    control instanceof FormControl &&
    (control.value === null ||
      control.value === undefined ||
      typeof control.value === 'string') &&
    (!('controls' in control) ||
      control.controls === undefined ||
      control.controls === null)
  );
}
