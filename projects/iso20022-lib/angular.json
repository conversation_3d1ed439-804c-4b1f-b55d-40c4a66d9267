{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "", "projects": {"iso20022-lib": {"projectType": "library", "root": ".", "sourceRoot": "./src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "ng-package.json"}, "configurations": {"production": {"tsConfig": "tsconfig.lib.prod.json"}, "development": {"tsConfig": "tsconfig.lib.json"}}, "defaultConfiguration": "production"}}}}}