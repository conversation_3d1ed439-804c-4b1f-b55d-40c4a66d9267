import { Type } from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  NonNullableFormBuilder,
  ValidationErrors,
} from '@angular/forms';

export type SelectOption<T> = {
  key: string;
  label: string;
  value: T;
};

export enum DatePattern {
  YYYY_MM_DD = 'yyyy-MM-dd',
  HH_MM_SS = 'HH:mm:ss',
  YYYY_MM_DD_HH_MM_SS = 'yyyy-MM-dd HH:mm:ss',
}

export const patternRegexMap: Record<DatePattern, RegExp> = {
  [DatePattern.YYYY_MM_DD]: /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/,
  [DatePattern.HH_MM_SS]: /^([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/,
  [DatePattern.YYYY_MM_DD_HH_MM_SS]:
    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01]) ([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/,
};

export enum CompletionPageKey {
  COMPLETION = 'completion',
}

export type FormSchemaMap = Record<
  string,
  FormControl<string> | FormArray<FormControl<string>> | FormArray<FormGroup>
>;

export type FormStep<D extends FormSchemaMap, H extends FormSchemaMap> = {
  key: string;
  label: string;
  component: Type<any>;
  documentFormGroupInitializer?: (fb: NonNullableFormBuilder) => D;
  bahFormGroupInitializer?: (fb: NonNullableFormBuilder) => H;
};

export type MultiPageFormGroups = Record<
  string,
  { documentFormGroup: FormGroup; bahFormGroup: FormGroup }
>;

export type MultiPageFormErrors = Record<
  string,
  {
    document: Map<string, ValidationErrors | null>;
    bah: Map<string, ValidationErrors | null>;
  }
>;
