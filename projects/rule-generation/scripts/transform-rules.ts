import { sharedRequiredDefinitions } from '../input/pacs.008/pacs.008.001.08_cbprplus/custom_rules';
import {
  generateRuleId,
  getNestedFieldNames,
  getRef,
  getBasicRuleHash,
  getMergedFieldName,
  generateRuleDescription,
  generateConditionalExplanation,
  concatenateFieldNames,
  moveToFront,
} from './utils';
import type {
  XsdElement,
  BasicRule,
  Condition,
  Connector,
  CustomConditionalRuleWithUsageGuidelineRules,
  CustomRuleWithBaseKeysAndUsageGuidelineRules,
  CustomRuleWithUsageGuidelineRules,
  NestedCondition,
  NestedDefinition,
  ProhibitedRule,
  RequiredDefinition,
  RequiredRule,
  Rule,
  BasicCondition,
} from '@helaba/iso20022-lib/rules';
import { isBasicCondition } from '@helaba/iso20022-lib/rules';
import { isObject, isStringArray } from '@helaba/iso20022-lib/util';

function addBaseKeyToRuleAndRemoveBaseKeysAttribute<
  T extends CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
>(rule: T, baseKey: string): T {
  const transformedRule = { ...rule };

  if ('baseKeys' in transformedRule) {
    // Remove the baseKeys property as it is not needed in the transformed rule.
    delete transformedRule.baseKeys;
  }

  if ('target' in transformedRule && transformedRule.target) {
    transformedRule.target = `${baseKey}-${transformedRule.target}`;
  }
  if (
    transformedRule.type === 'contains' &&
    'value' in transformedRule &&
    isStringArray(transformedRule.value)
  ) {
    transformedRule.value = transformedRule.value.map(
      (field: string) => `${baseKey}-${field}`
    );
  }

  // Replace '[base]' in description if the user has provided a description.
  transformedRule.description = transformedRule.description?.replace(
    /\[base\]/g,
    `${baseKey}-`
  );

  return transformedRule;
}

function addBaseKeyToBasicCondition(
  condition: BasicCondition,
  baseKey: string
): BasicCondition {
  const transformed: BasicCondition = { ...condition };

  transformed.field = `${baseKey}-${transformed.field}`;

  if ('otherField' in transformed && transformed.otherField) {
    transformed.otherField = `${baseKey}-${transformed.otherField}`;
  }

  return transformed;
}

function addBaseKeyToCondition(
  condition: Condition,
  baseKey: string
): Condition {
  if (isBasicCondition(condition)) {
    return addBaseKeyToBasicCondition(condition, baseKey);
  } else {
    const transformed: NestedCondition = { ...condition };
    transformed.conditions = transformed.conditions.map(
      (cond: BasicCondition) => addBaseKeyToBasicCondition(cond, baseKey)
    );

    return transformed;
  }
}

/**
 * For each base key, create a new rule with the base key added to all 'id', 'target', 'field', 'otherField', 'otherFields', and 'errorMessage' properties.
 * @param rule the original rule with the 'baseKeys' property
 * @returns an array of new rules with the base keys applied
 */
function getRulesForBaseKeys(
  rule: CustomRuleWithBaseKeysAndUsageGuidelineRules<
    undefined,
    string | undefined
  >
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (!rule.baseKeys || rule.baseKeys.length === 0) {
    if (rule.baseKeys) {
      delete rule.baseKeys;
    }
    return [
      rule as CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
    ];
  }

  return rule.baseKeys.map((baseKey: string) => {
    const transformedRule: CustomRuleWithUsageGuidelineRules<
      undefined,
      string | undefined
    > = addBaseKeyToRuleAndRemoveBaseKeysAttribute<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
    >(rule, baseKey);

    if (
      'conditions' in transformedRule &&
      transformedRule.conditions &&
      transformedRule.conditions.length > 0
    ) {
      transformedRule.conditions = transformedRule.conditions.map(
        (condition: Condition) => addBaseKeyToCondition(condition, baseKey)
      );
    }

    if (
      'rules' in transformedRule &&
      transformedRule.rules &&
      transformedRule.rules.length > 0
    ) {
      transformedRule.rules = transformedRule.rules.map(
        (subRule: BasicRule<undefined, string | undefined>) =>
          addBaseKeyToRuleAndRemoveBaseKeysAttribute<
            BasicRule<undefined, string | undefined>
          >(subRule, baseKey)
      );
    }

    return transformedRule;
  });
}

function addAutogeneratedId(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  nameMappings: XsdElement
): CustomRuleWithUsageGuidelineRules<string, string | undefined> {
  const transformedRule: CustomRuleWithUsageGuidelineRules<
    string,
    string | undefined
  > =
    rule.type === 'condition'
      ? { ...rule, id: '', rules: rule.rules.map((r) => ({ ...r, id: '' })) }
      : { ...rule, id: '' };

  // Make sure there are no special characters except underscores in the usageGuidelineRules.
  if (
    transformedRule.usageGuidelineRules &&
    !transformedRule.usageGuidelineRules.every((prefix: string) =>
      /^[a-zA-Z0-9_]+$/.test(prefix)
    )
  ) {
    throw new Error(
      `Invalid rule identifier prefixes "${rule.usageGuidelineRules}". Only alphanumeric characters are allowed.`
    );
  }

  const ruleIdPrefix: string = `${rule.usageGuidelineRules?.join('-') ?? ''}`;

  if (transformedRule.type !== 'condition') {
    transformedRule.id = generateRuleId(
      transformedRule.type,
      ruleIdPrefix,
      transformedRule.target,
      {
        value:
          transformedRule.type === 'value' || transformedRule.type === 'pattern'
            ? transformedRule.value
            : undefined,
      }
    );
  }

  if (transformedRule.type === 'condition') {
    // Make sure all conditional rules have the same type.
    if (transformedRule.rules.length === 0) {
      throw new Error(
        `Rule with type "condition" must have at least one rule. ${transformedRule}`
      );
    }
    const conditionalRuleType = transformedRule.rules[0].type;
    if (!transformedRule.rules.every((r) => r.type === conditionalRuleType)) {
      throw new Error(
        `All rules in a conditional rule must have the same type. Found types: ${transformedRule.rules
          .map((r) => r.type)
          .join(', ')}. Multiple rule types are not supported as of now.`
      );
    }

    transformedRule.id = generateRuleId(
      'condition',
      ruleIdPrefix,
      getMergedFieldName(
        transformedRule.rules.map((r) => r.target),
        nameMappings
      ),
      {
        conditions: transformedRule.conditions,
        conditionalRuleType,
      }
    );

    const transformedConditionalRules = [];
    for (const conditionalRule of transformedRule.rules) {
      const transformedConditionalRule: BasicRule<string, string | undefined> =
        { ...conditionalRule, id: '' };
      transformedConditionalRule.id = generateRuleId(
        conditionalRule.type,
        ruleIdPrefix,
        conditionalRule.target,
        {
          isConditional: true,
          conditionalParentRuleId: transformedRule.id,
        }
      );
      transformedConditionalRules.push(transformedConditionalRule);
    }

    transformedRule.rules = transformedConditionalRules;
  }

  if (!transformedRule.id) {
    throw new Error(`Rule with type "${rule.type}" does not have an id.`);
  }

  return transformedRule;
}

function getTargetRef(
  target: string,
  definitions: Record<string, unknown>
): string | undefined {
  const targetRefs: string[] = Object.values(definitions).reduce<string[]>(
    (acc, definition) => {
      if (!isObject(definition) || !('properties' in definition)) {
        return acc;
      }
      const properties = definition['properties'];
      if (!isObject(properties)) {
        return acc;
      }

      for (const propertyKey of Object.keys(properties)) {
        const property = properties[propertyKey];

        if (!isObject(property) || !('nestedFieldNames' in property)) {
          continue;
        }

        const nestedFieldNames = property['nestedFieldNames'];

        if (
          isStringArray(nestedFieldNames) &&
          nestedFieldNames.includes(target)
        ) {
          const ref = getRef(property);
          if (ref) {
            acc.push(ref);
          }
        }
      }

      return acc;
    },
    []
  );

  if (targetRefs.length === 0) {
    return undefined;
  }
  if (targetRefs.length > 1) {
    throw new Error(
      `Target "${target}" is referenced in multiple definitions: ${targetRefs.join(
        ', '
      )}.`
    );
  }
  return targetRefs[0];
}

function getDefinition(
  ref: string,
  definitions: Record<string, unknown>
): Record<string, unknown> | undefined {
  const definition = definitions[ref];
  if (!isObject(definition) || !('type' in definition)) {
    throw new Error(`Definition for ref "${ref}" is not a valid object.`);
  }

  if (definition['type'] !== 'object') {
    return undefined;
  }

  return definition;
}

/**
 * Retrieve a map that contains for each required rule the target reference (e.g. 'PostalAddress24__1').
 */
function getRelevantDefinitionReferences(
  conditionalRequiredRules: RequiredRule<undefined, string | undefined>[],
  definitions: Record<string, unknown>
): Map<string, string> {
  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalRequiredRule of conditionalRequiredRules) {
    const targetRef = getRelevantDefinitionReference(
      conditionalRequiredRule.target,
      definitions
    );

    if (!targetRef) {
      continue;
    }

    // Just to make sure there are no hash collisions
    if (relevantDefinitions.has(getBasicRuleHash(conditionalRequiredRule))) {
      throw new Error(
        `Target "${conditionalRequiredRule.target}" is already defined in relevantDefinitions. This should not happen.`
      );
    }

    relevantDefinitions.set(
      getBasicRuleHash(conditionalRequiredRule),
      targetRef
    );
  }

  return relevantDefinitions;
}

function getRelevantDefinitionReference(
  target: string,
  definitions: Record<string, unknown>
): string | undefined {
  const targetRef = getTargetRef(target, definitions);
  if (!targetRef) {
    // This means that this rule does not have to be expanded, it has no ref and thus there is no further nesting.
    return undefined;
  }

  const nestedDefinition = getDefinition(targetRef, definitions);
  if (!nestedDefinition) {
    // This means that this rule does not have to be expanded, the rule is not of type "object" and thus there is no further nesting.
    return undefined;
  }

  if (!(targetRef in sharedRequiredDefinitions)) {
    throw new Error(
      `Target "${target}" is not defined in sharedRequiredDefinitions.`
    );
  }

  return targetRef;
}

/**
 * Expands a rule with type "condition" that has a "required" target into multiple rules based on the sharedRequiredDefinitions.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandRequiredTargets(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalRequiredRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) => rule.type === 'required'
  );

  if (conditionalRequiredRules.length === 0) {
    return [rule];
  }

  const expandedRules: CustomRuleWithUsageGuidelineRules<
    undefined,
    string | undefined
  >[] = [];

  if (conditionalRequiredRules.length !== conditionalRules.length) {
    if (rule.rulesConnector === 'or') {
      throw new Error(
        `Rule ${JSON.stringify(
          rule
        )} mixes conditional rules of type 'required' with other rule types and the 'rulesConnector' is 'or'. This is not supported as of now.`
      );
    }
    // Split out the non-required rules into their own conditional rule to simplify the expansion logic.
    const nonRequiredRules = conditionalRules.filter(
      (rule: BasicRule<undefined, string | undefined>) =>
        rule.type !== 'required'
    );
    expandedRules.push({
      ...rule,
      rules: nonRequiredRules,
    });
  }

  const rulesConnector = rule.rulesConnector || 'and';

  const requiredDefinition: RequiredDefinition = expandRequiredDefinition(
    conditionalRequiredRules,
    rulesConnector,
    definitions
  );
  const requiredDefinitionType: Connector =
    'and' in requiredDefinition ? 'and' : 'or';
  let requiredDefinitionEntries: NestedDefinition;
  if (requiredDefinitionType === 'and' && 'and' in requiredDefinition) {
    requiredDefinitionEntries = requiredDefinition.and;
  } else if (requiredDefinitionType === 'or' && 'or' in requiredDefinition) {
    requiredDefinitionEntries = requiredDefinition.or;
  } else {
    throw new Error(
      `This should not happen. Invalid required definition: ${requiredDefinition}`
    );
  }

  const simpleFields: string[] = [];
  const nestedGroups: string[][] = [];

  for (const entry of requiredDefinitionEntries) {
    if (typeof entry === 'string') {
      simpleFields.push(entry);
    } else if (Array.isArray(entry)) {
      // Ensure the nested group is exactly one-level deep and contains only strings
      for (const inner of entry) {
        if (Array.isArray(inner)) {
          throw new Error(
            'Nested groups deeper than one level are not supported'
          );
        }
        if (typeof inner !== 'string') {
          throw new Error('Nested group may only contain strings');
        }
      }
      nestedGroups.push(entry as string[]);
    } else {
      throw new Error('Invalid entry in RequiredDefinition');
    }
  }

  if (nestedGroups.length > 1) {
    throw new Error(
      'Multiple nested groups in a single RequiredDefinition are not supported'
    );
  }

  // No nested group -> single flat rule
  if (nestedGroups.length === 0) {
    return [
      {
        ...rule,
        rules: simpleFields.map((field) => ({
          id: undefined,
          description: undefined,
          descriptionTranslationProposal: undefined,
          type: 'required',
          target: field,
        })),
      },
    ];
  }

  // Exactly one nested group (one-level)
  const nested = nestedGroups[0];
  if (nested.length === 0) {
    throw new Error('Empty nested group is invalid');
  }

  if (requiredDefinitionType === 'and') {
    // Parent: AND(simpleFields..., OR(nested...))
    // -> Need:
    //   - rule that requires all simpleFields (AND)
    //   - rule that requires at least one of nested (OR)
    if (simpleFields.length > 0) {
      expandedRules.push({
        ...rule,
        rules: simpleFields.map((field) => ({
          id: undefined,
          description: undefined,
          descriptionTranslationProposal: undefined,
          type: 'required',
          target: field,
        })),
        rulesConnector: 'and',
      });
    }
    expandedRules.push({
      ...rule,
      rules: nested.map((field) => ({
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: field,
      })),
      rulesConnector: 'or',
    });

    return expandedRules;
  } else {
    // Parent: OR(simpleFields..., AND(nested...))
    // -> Use the pivot trick:
    //   - Rule A: baseCondition, OR, [ ...simpleFields, pivot ]
    //   - Rule B: baseCondition AND pivot present, AND, [ remaining nested elements ]
    const pivot = nested[0];
    const remaining = nested.slice(1);

    const firstOrFields = [...simpleFields, pivot];
    expandedRules.push({
      ...rule,
      rules: firstOrFields.map((field) => ({
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: field,
      })),
      rulesConnector: 'or',
    });

    if (remaining.length > 0) {
      if (rule.conditionsConnector === 'or') {
        throw new Error(
          `Cannot expand rule ${JSON.stringify(
            rule
          )} with conditionsConnector "or" when there is an AND group in the required definition. This is not supported as of now.`
        );
      }
      expandedRules.push({
        ...rule,
        conditions: [
          ...rule.conditions,
          {
            type: 'present',
            value: true,
            field: pivot,
          },
        ],
        conditionsConnector: 'and',
        rules: remaining.map((field) => ({
          id: undefined,
          description: undefined,
          descriptionTranslationProposal: undefined,
          type: 'required',
          target: field,
        })),
        rulesConnector: 'and',
      });
    }
    return expandedRules;
  }
}

/**
 * Expand base rules + shared definitions into a single RequiredDefinition.
 */
function expandRequiredDefinition(
  requiredRules: RequiredRule<undefined, string | undefined>[],
  rulesConnector: Connector,
  definitions: Record<string, unknown>
): RequiredDefinition {
  type Out = {
    items: NestedDefinition;
    flatten: boolean; // true => splice into parent
  };

  const flip = (c: Connector): Connector => (c === 'and' ? 'or' : 'and');

  // Push an Out into an accumulating NestedDefinition with correct flattening.
  function pushExpanded(acc: NestedDefinition, out: Out) {
    if (out.flatten) {
      acc.push(...out.items);
    } else {
      // Avoid pointless grouping around a single string (or empty)
      if (out.items.length === 1 && typeof out.items[0] === 'string') {
        acc.push(out.items[0]);
      } else {
        acc.push(out.items);
      }
    }
  }

  // Expand a required definition body under its own top connector.
  function expandRequiredBody(
    def: RequiredDefinition,
    prefix: string
  ): { items: NestedDefinition; top: Connector } {
    const top: Connector = 'and' in def ? 'and' : 'or';
    const body = 'and' in def ? def.and : def.or;
    const items: NestedDefinition = [];

    for (const entry of body) {
      if (typeof entry === 'string') {
        const out = expandAtom(`${prefix}-${entry}`, top); // recurse into chained refs
        pushExpanded(items, out);
      } else {
        // Nested array => explicit connector flip
        const nested = expandNestedArray(entry, flip(top), prefix);
        items.push(nested);
      }
    }
    return { items, top };
  }

  // Expand a nested array that implicitly flips connector.
  function expandNestedArray(
    group: NestedDefinition,
    context: Connector,
    prefix: string
  ): NestedDefinition {
    const acc: NestedDefinition = [];
    for (const entry of group) {
      if (typeof entry === 'string') {
        const out = expandAtom(`${prefix}-${entry}`, context);
        pushExpanded(acc, out);
      } else {
        // flip again
        acc.push(expandNestedArray(entry, flip(context), prefix));
      }
    }
    return acc;
  }

  // Expand a single target in the given parent context.
  function expandAtom(target: string, parentContext: Connector): Out {
    const ref = getRelevantDefinitionReference(target, definitions);
    if (!ref) return { items: [target], flatten: true };

    const def = sharedRequiredDefinitions[ref];
    if (!def) return { items: [target], flatten: true };

    const { items, top } = expandRequiredBody(def, target);
    return {
      items,
      flatten: top === parentContext, // same connector → splice
    };
  }

  function simplifyTopLevel(
    connector: Connector,
    acc: NestedDefinition
  ): RequiredDefinition {
    // If top-level has a single nested group, unwrap into opposite connector
    if (acc.length === 1 && Array.isArray(acc[0])) {
      const inner = acc[0];
      return connector === 'and' ? { or: inner } : { and: inner };
    }
    return connector === 'and' ? { and: acc } : { or: acc };
  }

  // Top-level assembly
  const acc: NestedDefinition = [];
  for (const target of requiredRules.map((r) => r.target)) {
    pushExpanded(acc, expandAtom(target, rulesConnector));
  }

  return simplifyTopLevel(rulesConnector, acc);
}

/**
 * Expands a rule with type "condition" that has a "prohibited" target into multiple rules.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandProhibitedTargets(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalProhibitedRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) =>
      rule.type === 'prohibited'
  );
  const conditionalNonProhibitedRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) =>
      rule.type !== 'prohibited'
  );

  // We only allow 'rulesConnector: "and"' for conditional rules with "prohibited" rules.
  if (rule.rulesConnector === 'or' && conditionalProhibitedRules.length > 0) {
    throw new Error(
      `Rule ${rule.id} has a rulesConnector of "or" and there is a prohibited rule. This is not supported as of now.`
    );
  }

  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalProhibitedRule of conditionalProhibitedRules) {
    const target = conditionalProhibitedRule.target;
    const targetRef = getTargetRef(target, definitions);
    if (!targetRef) {
      continue;
    }

    const nestedDefinition = getDefinition(targetRef, definitions);
    if (!nestedDefinition) {
      continue;
    }

    relevantDefinitions.set(target, targetRef);
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  const newConditionalProhibitedRules: ProhibitedRule<
    undefined,
    string | undefined
  >[] = [];

  for (const [target, targetRef] of relevantDefinitions.entries()) {
    const prohibitedRuleToExpand = conditionalProhibitedRules.find(
      (rule: ProhibitedRule<undefined, string | undefined>) =>
        rule.target === target
    );

    if (!prohibitedRuleToExpand) {
      throw new Error(
        `No "prohibited" rule found for target "${target}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      target
    );

    newConditionalProhibitedRules.push(
      ...allNestedFieldNames.map(
        (fieldName): ProhibitedRule<undefined, string | undefined> => ({
          ...prohibitedRuleToExpand,
          id: undefined,
          description: `${prohibitedRuleToExpand.description} This means, that ${fieldName} must not be present.`,
          target: fieldName,
        })
      )
    );
  }

  const conditionalProhibitedRulesNotToExpand =
    conditionalProhibitedRules.filter(
      (rule: ProhibitedRule<undefined, string | undefined>) =>
        !relevantDefinitions.has(rule.target)
    );

  // Spread out the "prohibited" rules into multiple rules.
  return [
    {
      ...rule,
      rules: [
        ...conditionalNonProhibitedRules,
        ...conditionalProhibitedRulesNotToExpand,
        ...newConditionalProhibitedRules,
      ],
      rulesConnector: 'and',
    },
  ];
}

/**
 * Expand conditions of type "present" to include all subfields. If there is a sibling to the condition connected with "and", extract that into its own rule.
 * @param rule
 * @param definitions
 * @returns
 */
function expandPresentConditions(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditions: Condition[] = rule.conditions;

  const relevantDefinitions: Map<string, string> = new Map();

  for (const condition of conditions) {
    if (isBasicCondition(condition)) {
      if (condition.type !== 'present' || condition.value !== true) {
        continue;
      }
      const target = condition.field;
      const targetRef = getTargetRef(target, definitions);
      if (!targetRef) {
        continue;
      }
      const nestedDefinition = getDefinition(targetRef, definitions);
      if (!nestedDefinition) {
        continue;
      }
      relevantDefinitions.set(target, targetRef);
    } else {
      // Nested conditions cannot be expanded as of now.
      for (const subCondition of condition.conditions) {
        if (subCondition.type !== 'present' || subCondition.value !== true) {
          continue;
        }
        const target = subCondition.field;
        const targetRef = getTargetRef(target, definitions);
        if (!targetRef) {
          continue;
        }
        const nestedDefinition = getDefinition(targetRef, definitions);
        if (nestedDefinition) {
          throw new Error(
            `Found a nested condition in rule ${rule.id} that requires expansion. Expansion is not supported for nested conditions as of now.`
          );
        }
      }
    }
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  if (
    (typeof rule.conditionsConnector === 'undefined' ||
      rule.conditionsConnector === 'and') &&
    relevantDefinitions.size > 1
  ) {
    throw new Error(
      `Multiple conditions connected with "and" found in rule ${rule.id} that need to be expanded. This is not supported as of now.`
    );
  }

  const expandedConditions: Condition[] = [];

  for (const [field, targetRef] of relevantDefinitions.entries()) {
    const presentConditionToExpand = conditions.find(
      (condition) => isBasicCondition(condition) && condition.field === field
    );

    if (
      !presentConditionToExpand ||
      !isBasicCondition(presentConditionToExpand)
    ) {
      throw new Error(
        `No "present" condition found for field "${field}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      field
    );

    expandedConditions.push(
      ...allNestedFieldNames.map(
        (fieldName): Condition => ({
          type: 'present',
          value: true,
          field: fieldName,
        })
      )
    );
  }

  const conditionsNotToExpand = conditions.filter(
    (condition) =>
      !isBasicCondition(condition) || !relevantDefinitions.has(condition.field)
  );

  if (rule.conditionsConnector === 'or' || conditions.length === 1) {
    // Simple case: We just spread out the "present" condition into multiple conditions.
    return [
      {
        ...rule,
        conditions: [...expandedConditions, ...conditionsNotToExpand],
        conditionsConnector: 'or',
      },
    ];
  } else {
    // There are multiple conditions connected with "and". Create a new rule containing the conditions not to expand.
    const newConditionalRule: CustomConditionalRuleWithUsageGuidelineRules<
      undefined,
      string | undefined
    > = {
      ...rule,
      conditions: conditionsNotToExpand,
      conditionsConnector: 'and',
    };

    return [
      newConditionalRule,
      {
        ...rule,
        conditions: expandedConditions,
        conditionsConnector: 'or',
      },
    ];
  }
}

function addAutogeneratedDescription(
  rule: CustomRuleWithUsageGuidelineRules<string, string | undefined>,
  nameMappings: XsdElement
): CustomRuleWithUsageGuidelineRules<string, string> {
  const transformedRule: CustomRuleWithUsageGuidelineRules<string, string> =
    rule.type === 'condition'
      ? {
          ...rule,
          description: rule.description || '',
          descriptionTranslationProposal:
            rule.descriptionTranslationProposal || '',
          rules: rule.rules.map((r) => ({
            ...r,
            description: r.description || '',
            descriptionTranslationProposal:
              r.descriptionTranslationProposal || '',
          })),
        }
      : {
          ...rule,
          description: rule.description || '',
          descriptionTranslationProposal:
            rule.descriptionTranslationProposal || '',
        };

  const ruleType = transformedRule.type;

  if (ruleType !== 'condition') {
    if (transformedRule.description) {
      console.info(
        `Rule with type "${ruleType}" already has a description. Using the existing description.`
      );
    } else {
      transformedRule.description = generateRuleDescription(
        ruleType,
        transformedRule.target,
        nameMappings,
        'en',
        {
          ruleDependentValue:
            ruleType === 'maxLength' ||
            ruleType === 'pattern' ||
            ruleType === 'maxItems' ||
            ruleType === 'contains' ||
            ruleType === 'value'
              ? transformedRule.value
              : undefined,
          secondRuleDependentValue:
            ruleType === 'value'
              ? transformedRule.isEqual
              : ruleType === 'contains'
              ? transformedRule.contains
              : undefined,
        }
      );
    }
    if (transformedRule.descriptionTranslationProposal) {
      console.info(
        `Rule with type "${ruleType}" already has a description translation proposal. Using the existing description translation proposal.`
      );
    } else {
      transformedRule.descriptionTranslationProposal = generateRuleDescription(
        ruleType,
        transformedRule.target,
        nameMappings,
        'de',
        {
          ruleDependentValue:
            ruleType === 'maxLength' ||
            ruleType === 'pattern' ||
            ruleType === 'maxItems' ||
            ruleType === 'contains' ||
            ruleType === 'value'
              ? transformedRule.value
              : undefined,
          secondRuleDependentValue:
            ruleType === 'value'
              ? transformedRule.isEqual
              : ruleType === 'contains'
              ? transformedRule.contains
              : undefined,
        }
      );
    }
  }

  if (transformedRule.type === 'condition') {
    if (
      transformedRule.description &&
      transformedRule.rules.every((r) => r.description)
    ) {
      console.info(
        `Rule with type "condition" ${transformedRule.id} already has a description including all nested rules. Using the existing description.`
      );
    } else {
      if (transformedRule.rules.length === 0) {
        throw new Error(
          `Rule with type "condition" must have at least one rule. ${transformedRule}`
        );
      }

      transformedRule.description = generateRuleDescription(
        'condition',
        concatenateFieldNames(
          transformedRule.rules.map((r) => r.target),
          transformedRule.rulesConnector ?? 'and',
          'en',
          nameMappings
        ),
        nameMappings,
        'en',
        {
          conditions: transformedRule.conditions,
          conditionsConnector: transformedRule.conditionsConnector ?? 'and',
          conditionalRules: transformedRule.rules,
          conditionalRulesConnector: transformedRule.rulesConnector ?? 'and',
        }
      );

      const transformedConditionalRules = [];
      for (const conditionalRule of transformedRule.rules) {
        const transformedConditionalRule: BasicRule<string, string> = {
          ...conditionalRule,
        };
        const conditionalRuleType = transformedConditionalRule.type;
        transformedConditionalRule.description = generateRuleDescription(
          conditionalRule.type,
          concatenateFieldNames(
            moveToFront(
              transformedRule.rules.map((r) => r.target),
              conditionalRule.target
            ),
            transformedRule.rulesConnector ?? 'and',
            'en',
            nameMappings
          ),
          nameMappings,
          'en',
          {
            ruleDependentValue:
              conditionalRuleType === 'maxLength' ||
              conditionalRuleType === 'pattern' ||
              conditionalRuleType === 'maxItems' ||
              conditionalRuleType === 'value'
                ? transformedConditionalRule.value
                : undefined,
            secondRuleDependentValue:
              conditionalRuleType === 'value'
                ? transformedConditionalRule.isEqual
                : undefined,
            conditionalExplanation: generateConditionalExplanation(
              transformedRule.conditions,
              transformedRule.conditionsConnector ?? 'and',
              'en',
              nameMappings
            ),
          }
        );
        transformedConditionalRules.push(transformedConditionalRule);
      }

      transformedRule.rules = transformedConditionalRules;
    }
    if (
      transformedRule.descriptionTranslationProposal &&
      transformedRule.rules.every((r) => r.descriptionTranslationProposal)
    ) {
      console.info(
        `Rule with type "condition" already has a description translation proposal including all nested rules. Using the existing description.`
      );
    } else {
      if (transformedRule.rules.length === 0) {
        throw new Error(
          `Rule with type "condition" must have at least one rule. ${transformedRule}`
        );
      }

      transformedRule.descriptionTranslationProposal = generateRuleDescription(
        'condition',
        concatenateFieldNames(
          transformedRule.rules.map((r) => r.target),
          transformedRule.rulesConnector ?? 'and',
          'de',
          nameMappings
        ),
        nameMappings,
        'de',
        {
          conditions: transformedRule.conditions,
          conditionsConnector: transformedRule.conditionsConnector ?? 'and',
          conditionalRules: transformedRule.rules,
          conditionalRulesConnector: transformedRule.rulesConnector ?? 'and',
        }
      );

      const transformedConditionalRules = [];
      for (const conditionalRule of transformedRule.rules) {
        const transformedConditionalRule: BasicRule<string, string> = {
          ...conditionalRule,
        };
        const conditionalRuleType = transformedConditionalRule.type;
        transformedConditionalRule.descriptionTranslationProposal =
          generateRuleDescription(
            conditionalRule.type,
            concatenateFieldNames(
              moveToFront(
                transformedRule.rules.map((r) => r.target),
                conditionalRule.target
              ),
              transformedRule.rulesConnector ?? 'and',
              'de',
              nameMappings
            ),
            nameMappings,
            'de',
            {
              ruleDependentValue:
                conditionalRuleType === 'maxLength' ||
                conditionalRuleType === 'pattern' ||
                conditionalRuleType === 'maxItems' ||
                conditionalRuleType === 'value'
                  ? transformedConditionalRule.value
                  : undefined,
              secondRuleDependentValue:
                conditionalRuleType === 'value'
                  ? transformedConditionalRule.isEqual
                  : undefined,
              conditionalExplanation: generateConditionalExplanation(
                transformedRule.conditions,
                transformedRule.conditionsConnector ?? 'and',
                'de',
                nameMappings
              ),
            }
          );
        transformedConditionalRules.push(transformedConditionalRule);
      }

      transformedRule.rules = transformedConditionalRules;
    }
  }

  if (
    !(
      transformedRule.description &&
      transformedRule.descriptionTranslationProposal
    )
  ) {
    throw new Error(
      `Rule with type "${rule.type}" does not have a description and description translation proposal.`
    );
  }

  return transformedRule;
}

function addUsageGuidelineRulesToDescriptions(
  rule: CustomRuleWithUsageGuidelineRules<string, string>
): Rule {
  const transformedRule = { ...rule };
  const descriptionSuffix = rule.usageGuidelineRules?.join(', ');
  const description = transformedRule.description;
  const descriptionTranslationProposal =
    transformedRule.descriptionTranslationProposal;

  transformedRule.description = `${description}${
    descriptionSuffix ? ` (${descriptionSuffix})` : ''
  }`;
  transformedRule.descriptionTranslationProposal = `${descriptionTranslationProposal}${
    descriptionSuffix ? ` (${descriptionSuffix})` : ''
  }`;

  if (transformedRule.type === 'condition') {
    const transformedConditionalRules: BasicRule[] = [];
    for (const conditionalRule of transformedRule.rules) {
      const transformedConditionalRule = { ...conditionalRule };
      transformedConditionalRule.description = `${conditionalRule.description}${
        descriptionSuffix ? ` (${descriptionSuffix})` : ''
      }`;
      transformedConditionalRule.descriptionTranslationProposal = `${
        conditionalRule.descriptionTranslationProposal
      }${descriptionSuffix ? ` (${descriptionSuffix})` : ''}`;
      transformedConditionalRules.push(transformedConditionalRule);
    }
    transformedRule.rules = transformedConditionalRules;
  }

  if (transformedRule.usageGuidelineRules) {
    delete transformedRule.usageGuidelineRules;
  }

  return transformedRule;
}

export function transformRules(
  rules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
    undefined,
    string | undefined
  >[],
  jsonSchema: Record<string, unknown>,
  nameMappings: XsdElement
): Rule[] {
  if (!isObject(jsonSchema) || !('definitions' in jsonSchema)) {
    throw new Error('Invalid JSON schema format.');
  }

  const { definitions } = jsonSchema;

  if (!isObject(definitions)) {
    throw new Error('Invalid definitions in JSON schema.');
  }

  // Add the base keys to the rule (creating new rules if there are multiple base keys) and remove the 'baseKeys' property.
  // Allows the switch from 'CustomRuleWithBaseKeysAndUsageGuidelineRules' to 'CustomRuleWithUsageGuidelineRules'
  const allOriginalRules = rules.reduce<
    CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
  >((acc, rule) => {
    acc.push(...getRulesForBaseKeys(rule));
    return acc;
  }, []);

  const rulesWithExpandedRequiredTargets = allOriginalRules.reduce<
    CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
  >((acc, rule) => {
    const expandedRules = expandRequiredTargets(rule, definitions);
    acc.push(...expandedRules);
    return acc;
  }, []);

  const rulesWithExpandedProhibitedTargets =
    rulesWithExpandedRequiredTargets.reduce<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
    >((acc, rule) => {
      const expandedRules = expandProhibitedTargets(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  const rulesWithExpandedPresentConditions =
    rulesWithExpandedProhibitedTargets.reduce<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
    >((acc, rule) => {
      const expandedRules = expandPresentConditions(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  // Add IDs to all rules, allowing the switch from 'CustomRuleWithUsageGuidelineRules<string | undefined, string | undefined>' to 'CustomRuleWithUsageGuidelineRules<string, string | undefined>'.
  const rulesWithIds = rulesWithExpandedPresentConditions.map(
    (
      rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
    ): CustomRuleWithUsageGuidelineRules<string, string | undefined> =>
      addAutogeneratedId(rule, nameMappings)
  );

  // Add descriptions to all rules where the user did not provide any allowing the switch from 'CustomRuleWithUsageGuidelineRules<string, string | undefined>' to 'CustomRuleWithUsageGuidelineRules<string, string>'.
  const rulesWithDescriptions: CustomRuleWithUsageGuidelineRules<
    string,
    string
  >[] = rulesWithIds.map(
    (rule: CustomRuleWithUsageGuidelineRules<string, string | undefined>) =>
      addAutogeneratedDescription(rule, nameMappings)
  );

  const rulesWithUsageGuidelineRulesAddedToDescriptions =
    rulesWithDescriptions.map((rule) =>
      addUsageGuidelineRulesToDescriptions(rule)
    );

  return rulesWithUsageGuidelineRulesAddedToDescriptions;
}
