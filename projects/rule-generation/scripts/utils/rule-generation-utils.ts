import * as fs from 'fs';
import path from 'path';
import {
  isBasicCondition,
  isNestedCondition,
} from '@helaba/iso20022-lib/rules';
import type {
  XsdElement,
  BasicRuleType,
  BasicRule,
  ConditionsConnector,
  RuleType,
  ValueRule,
  Condition,
  BasicConditionType,
  BasicCondition,
} from '@helaba/iso20022-lib/rules';
import { isStringArray } from '@helaba/iso20022-lib/util';
import {
  CONDITONAL_RULE_ID_SEPARATOR,
  RULE_ID_SEPARATOR,
  CONCATENATED_FIELDS_SUFFIX_SEPARATORS,
  MERGED_FIELDS_SUFFIX_SEPARATORS,
  MERGED_FIELDS_SUFFIX_CONNECTOR,
  FIELD_NAME_SEPARATOR,
} from './constants';

export async function writeFileSafe(data: string | object, filePath: string) {
  // Extract the directory part of the file path
  const dir = path.dirname(filePath);

  // Create directories recursively if they don't exist
  await fs.promises.mkdir(dir, { recursive: true });

  // Write the file (creates or overwrites)
  writeFile(data, filePath);
}

export function writeFile(content: string | object, filePath: string): void {
  fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
  console.info(`Wrote to ${filePath}`);
}

/**
 * Generates a rule ID based on the rule type, prefix, and configuration.
 * @param ruleType The type of the rule (e.g., 'required', 'maxLength', 'condition', etc.).
 * @param prefix The prefix to use for the rule ID, e.g. 'generated', 'R12', etc.
 * @param nestedFieldName The field name that the rule targets, e.g. 'FIToFICstmrCdtTrf-GrpHdr-MsgId'
 * @param config Configuration object
 * @param config.conditions For conditional rules, the conditions that must be met for the rule to apply. We compute a HASH from these and add it to the generated ID to ensure uniqueness.
 * @param config.conditionalRuleType For conditional rules, type of conditional rule, attached to the end of the generated ID, e.g. 'required'.
 * @param config.isConditional Signifies whether a basic rule is meant to be used as one of the nested rules in a conditional rules. In this case, a special 'conditional' suffix is added to the rule ID.
 * @param config.conditionalParentRuleId For conditional basic rules, the rule ID of the parent "condition" type rule which is put before the basic rule's ID to ensure uniqueness.
 * @param config.value For value and pattern rules, the value of the rule to allow for adding multiple value or pattern rules for the same field.
 * @returns The generated rule ID.
 */
export function generateRuleId(
  ruleType: RuleType,
  prefix: string | undefined,
  nestedFieldName: string,
  config?: {
    conditions?: Condition[];
    conditionalRuleType?: BasicRuleType;
    isConditional?: boolean;
    conditionalParentRuleId?: string;
    value?: string;
  }
): string {
  if (
    ruleType !== 'condition' &&
    (config?.conditions || config?.conditionalRuleType)
  ) {
    throw new Error(
      `Conditions and conditional rule type should only be specified for condition rules.`
    );
  }
  if (
    ruleType === 'condition' &&
    (config?.isConditional || config?.conditionalParentRuleId || config?.value)
  ) {
    throw new Error(
      `A conditional rule is always conditional, no need to specify isConditional in the config. Also, 'conditionalParentRuleId' and 'value' should not be specified for condition rules.`
    );
  }

  // If the basic rule is conditional, we add the ID of the parent conditional rule to avoid duplicates.
  let conditionalRuleId = '';
  if (config?.isConditional) {
    if (ruleType === 'condition') {
      // This is already filtered out above but we need it this explicitly here to help typescript.
      throw new Error(`A conditional rule cannot be conditional itself.`);
    }
    if (!config.conditionalParentRuleId) {
      throw new Error(
        `Conditional parent rule ID must be provided for basic rules that are conditional.`
      );
    }
    conditionalRuleId = stripConditionalParentRuleIdPrefix(
      config.conditionalParentRuleId,
      prefix
    );
  }

  const valueHash = config?.value ? getHash(config.value) : undefined;

  let ruleId = '';
  switch (ruleType) {
    case 'required':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}required`;
      break;
    case 'prohibited':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}prohibited`;
      break;
    case 'maxLength':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}maxLength`;
      break;
    case 'pattern':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}pattern${
        valueHash ? RULE_ID_SEPARATOR + valueHash : ''
      }`;
      break;
    case 'value':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}value${
        valueHash ? RULE_ID_SEPARATOR + valueHash : ''
      }`;
      break;
    case 'maxItems':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}maxItems`;
      break;
    case 'contains':
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}contains`;
      break;
    case 'condition':
      if (!(config?.conditions && config.conditionalRuleType)) {
        throw new Error(
          `Conditions and conditional rule type must be provided for condition rules.`
        );
      }
      const conditionsHash = getConditionsHash(config.conditions);
      ruleId = `${nestedFieldName}${RULE_ID_SEPARATOR}conditional${FIELD_NAME_SEPARATOR}${config.conditionalRuleType}${RULE_ID_SEPARATOR}${conditionsHash}`;
      break;
    default:
      throw new Error(
        `Unsupported rule type "${ruleType}" for rule ID generation.`
      );
  }
  return `${prefix ? prefix + RULE_ID_SEPARATOR : ''}${
    conditionalRuleId ? conditionalRuleId + CONDITONAL_RULE_ID_SEPARATOR : ''
  }${ruleId}${
    config?.isConditional ? `${FIELD_NAME_SEPARATOR}conditional` : ''
  }`;
}

function getConditionsHash(conditions: Condition[]): string {
  if (conditions.length === 0) {
    throw new Error(`Conditions must be provided to generate a hash.`);
  }

  const fields = conditions
    .reduce<string[]>((acc, cond) => {
      if (isBasicCondition(cond)) {
        acc.push(cond.field);
      } else {
        for (const nestedCond of cond.conditions) {
          acc.push(nestedCond.field);
        }
      }
      return acc;
    }, [])
    .sort();

  const input = fields.join('|'); // Join into a single string

  return getHash(input);
}

function stripConditionalParentRuleIdPrefix(
  ruleId: string,
  prefix: string | undefined
): string {
  // Remove prefix
  const parts = ruleId.split(RULE_ID_SEPARATOR);
  if (parts.length < 4) {
    throw new Error(
      `Invalid conditional parent rule ID "${ruleId}". It should have at least four parts separated by "${RULE_ID_SEPARATOR}".`
    );
  }
  if (parts[0] !== prefix) {
    throw new Error(
      `Invalid conditional parent rule ID "${ruleId}". It should start with "${prefix}".`
    );
  }

  const strippedRuleId = parts.slice(1).join(RULE_ID_SEPARATOR);
  return strippedRuleId;
}

export function generateRuleDescription(
  ruleType: RuleType,
  nestedFieldName: string,
  nameMappings: XsdElement,
  lang: 'en' | 'de',
  config?: {
    ruleDependentValue?: unknown; // Can be the regex for a pattern rule or the maximum length for a maxLength rule.
    secondRuleDependentValue?: unknown; // For some rules, there is a second value, e.g. for 'value' rules, there is the 'value' itself + a boolean 'isEqual'.
    isForConditionalRuleDescription?: boolean; // For basic rules that are embedded in a conditional rule, this allows us to skip word order in the german translation.
    conditionalExplanation?: string;
    conditions?: Condition[]; // For condition rules, the conditions that must be met for the rule to apply.
    conditionsConnector?: ConditionsConnector;
    conditionalRules?: BasicRule<string | undefined, string | undefined>[];
    conditionalRulesConnector?: 'and' | 'or';
  }
): string {
  if (
    ruleType !== 'maxLength' &&
    ruleType !== 'pattern' &&
    ruleType !== 'maxItems' &&
    ruleType !== 'value' &&
    ruleType !== 'contains' &&
    typeof config?.ruleDependentValue !== 'undefined'
  ) {
    throw new Error(
      `Rule dependent value should not be provided for ${ruleType} rules.`
    );
  }
  if (
    ruleType !== 'value' &&
    ruleType !== 'contains' &&
    typeof config?.secondRuleDependentValue !== 'undefined'
  ) {
    throw new Error(
      `Second rule dependent value should not be provided for ${ruleType} rules.`
    );
  }
  if (
    ruleType === 'condition' &&
    typeof config?.isForConditionalRuleDescription !== 'undefined'
  ) {
    throw new Error(
      `'isConditional' should only be specified for basic rules nested inside conditional rules.`
    );
  }
  if (ruleType === 'condition' && config?.conditionalExplanation) {
    throw new Error(
      `Conditional explanation is generated automatically for condition rules and should not be provided in the config.`
    );
  }
  if (
    config?.conditionalExplanation &&
    typeof config.isForConditionalRuleDescription !== 'undefined'
  ) {
    throw new Error(
      `'conditionalExplanation' and 'isForConditionalRuleDescription' cannot be used together. 'conditionalExplanation' is for defining the description of nested conditional rules (e.g. 'XYZ is required if [conditionalExplanation]). 'isForConditionalRuleDescription' is for generating the basic rule description specifically for the description of the parent conditional rule (e.g. 'If condition XY is met, then [generateRuleDescription(..., {isConditional: true})]'. As the basic rule description is now in the latter part of the phrase, it needs to be rephrased slightly in German. This is what this flag is for.)`
    );
  }
  if (
    ruleType !== 'condition' &&
    (config?.conditions ||
      config?.conditionsConnector ||
      config?.conditionalRules ||
      config?.conditionalRulesConnector)
  ) {
    throw new Error(
      `Conditional rule type, conditions, and conditions connector should only be specified for condition rules.`
    );
  }

  // Go through the nameMappings and remove prefixes that are included in all nested field names.
  const truncatedNestedFieldName = removeSharedPrefix(
    nestedFieldName,
    nameMappings
  );

  let description = '';
  switch (ruleType) {
    case 'required':
      description =
        lang === 'en'
          ? `${truncatedNestedFieldName} is required`
          : config?.isForConditionalRuleDescription
          ? `ist ${truncatedNestedFieldName} erforderlich`
          : `${truncatedNestedFieldName} ist erforderlich`;
      break;
    case 'prohibited':
      description =
        lang === 'en'
          ? `${truncatedNestedFieldName} is prohibited`
          : config?.isForConditionalRuleDescription
          ? `ist ${truncatedNestedFieldName} nicht erlaubt`
          : `${truncatedNestedFieldName} ist nicht erlaubt`;
      break;
    case 'maxLength':
      if (typeof config?.ruleDependentValue !== 'number') {
        throw new Error(
          `Invalid ruleDependentValue for maxLength rule: ${config?.ruleDependentValue}. It must be a number representing the maximum length.`
        );
      }
      description =
        lang === 'en'
          ? `${truncatedNestedFieldName} must be at most ${config.ruleDependentValue} characters long`
          : config?.isForConditionalRuleDescription
          ? `darf ${truncatedNestedFieldName} höchstens ${config.ruleDependentValue} Zeichen lang sein`
          : `${truncatedNestedFieldName} darf höchstens ${config.ruleDependentValue} Zeichen lang sein`;
      break;
    case 'pattern':
      if (
        !config?.ruleDependentValue ||
        (typeof config.ruleDependentValue !== 'string' &&
          !isStringArray(config.ruleDependentValue))
      ) {
        throw new Error(
          `Invalid ruleDependentValue for pattern rule: ${config?.ruleDependentValue}. It must be
a string representing the regex pattern or an array containing the possible enum values.`
        );
      }
      if (typeof config.ruleDependentValue === 'string') {
        description =
          lang === 'en'
            ? `${truncatedNestedFieldName} must match the pattern ${config.ruleDependentValue}`
            : config.isForConditionalRuleDescription
            ? `muss ${truncatedNestedFieldName} dem Muster ${config.ruleDependentValue} entsprechen`
            : `${truncatedNestedFieldName} muss dem Muster ${config.ruleDependentValue} entsprechen`;
      } else if (isStringArray(config.ruleDependentValue)) {
        if (config.ruleDependentValue.length === 0) {
          throw new Error(`
            Invalid ruleDependentValue for pattern rule: ${config?.ruleDependentValue}. It must be a non-empty array of strings representing the possible enum values.`);
        }
        if (config.ruleDependentValue.length === 1) {
          description =
            lang === 'en'
              ? `${truncatedNestedFieldName} must have the value ${config.ruleDependentValue[0]}`
              : config.isForConditionalRuleDescription
              ? `muss ${truncatedNestedFieldName} den Wert ${config.ruleDependentValue[0]} haben`
              : `${truncatedNestedFieldName} muss den Wert ${config.ruleDependentValue[0]} haben`;
        } else {
          description =
            lang === 'en'
              ? `${truncatedNestedFieldName} must be one of the following values: ${config.ruleDependentValue.join(
                  ', '
                )}`
              : config.isForConditionalRuleDescription
              ? `muss ${truncatedNestedFieldName} einer der folgenden Werte sein: ${config.ruleDependentValue.join(
                  ', '
                )}`
              : `${truncatedNestedFieldName} muss einer der folgenden Werte sein: ${config.ruleDependentValue.join(
                  ', '
                )}`;
        }
      } else {
        throw new Error(
          `Invalid ruleDependentValue for pattern rule: ${config?.ruleDependentValue}. It must be a string representing the regex pattern or an array containing the possible enum values.`
        );
      }
      break;
    case 'value':
      if (
        typeof config?.ruleDependentValue !== 'string' ||
        typeof config?.secondRuleDependentValue !== 'boolean'
      ) {
        throw new Error(
          `Invalid ruleDependentValues for value rule: ${config?.ruleDependentValue}, ${config?.secondRuleDependentValue}. It must be a string representing the value and a boolean indicating whether the field should be equal to the value.`
        );
      }
      description =
        lang === 'en'
          ? `${truncatedNestedFieldName} must ${
              config.secondRuleDependentValue ? 'equal' : 'not equal'
            } "${config.ruleDependentValue}"`
          : config.isForConditionalRuleDescription
          ? `muss ${truncatedNestedFieldName} ${
              config.secondRuleDependentValue ? 'gleich' : 'ungleich'
            } "${config.ruleDependentValue}" sein`
          : `${truncatedNestedFieldName} muss ${
              config.secondRuleDependentValue ? 'gleich' : 'ungleich'
            } "${config.ruleDependentValue}" sein`;
      break;
    case 'maxItems':
      if (typeof config?.ruleDependentValue !== 'number') {
        throw new Error(
          `Invalid ruleDependentValue for maxItems rule: ${config?.ruleDependentValue}. It must be a number representing the maximum number of items in the array.`
        );
      }
      description =
        lang === 'en'
          ? `${truncatedNestedFieldName} must have at most ${config.ruleDependentValue} items`
          : config.isForConditionalRuleDescription
          ? `darf ${truncatedNestedFieldName} höchstens ${config.ruleDependentValue} Elemente haben`
          : `${truncatedNestedFieldName} darf höchstens ${config.ruleDependentValue} Elemente haben`;
      break;
    case 'contains':
      if (
        !(
          config?.ruleDependentValue && isStringArray(config.ruleDependentValue)
        )
      ) {
        throw new Error(
          `Invalid ruleDependentValue for contains rule: ${config?.ruleDependentValue}. It must be an array of strings representing the other fields that should (not) be repeated in the target.`
        );
      }
      if (typeof config.secondRuleDependentValue !== 'boolean') {
        throw new Error(
          `Invalid secondRuleDependentValue for contains rule: ${config?.secondRuleDependentValue}. It must be a boolean indicating whether the field should contain the values from the other fields.`
        );
      }
      const otherFields = concatenateFieldNames(
        config.ruleDependentValue,
        'or',
        lang,
        nameMappings
      );
      if (config.secondRuleDependentValue) {
        description =
          lang === 'en'
            ? `${truncatedNestedFieldName} must contain data present in ${otherFields}`
            : config.isForConditionalRuleDescription
            ? `muss ${truncatedNestedFieldName} Daten enthalten, die in ${otherFields} vorhanden sind`
            : `${truncatedNestedFieldName} muss Daten enthalten, die in ${otherFields} vorhanden sind`;
      } else {
        description =
          lang === 'en'
            ? `${truncatedNestedFieldName} must not repeat data present in ${otherFields}`
            : config.isForConditionalRuleDescription
            ? `darf ${truncatedNestedFieldName} Daten, die in ${otherFields} vorhanden sind, nicht wiederholen`
            : `${truncatedNestedFieldName} darf Daten, die in ${otherFields} vorhanden sind, nicht wiederholen`;
      }
      break;
    case 'condition':
      return generateConditionalRuleDescription(
        nestedFieldName,
        config?.conditions,
        config?.conditionsConnector,
        config?.conditionalRules,
        config?.conditionalRulesConnector,
        lang,
        nameMappings
      );
    default:
      throw new Error(
        `Unsupported rule type "${ruleType}" for rule description generation.`
      );
  }

  return `${description}${
    config?.conditionalExplanation
      ? `${lang === 'en' ? ' if' : ', wenn'} ${config.conditionalExplanation}`
      : ''
  }.`;
}

function generateConditionalRuleDescription(
  nestedFieldName: string,
  conditions: Condition[] | undefined,
  conditionsConnector: ConditionsConnector | undefined,
  conditionalRules:
    | BasicRule<string | undefined, string | undefined>[]
    | undefined,
  conditionalRulesConnector: 'and' | 'or' | undefined,
  lang: 'en' | 'de',
  nameMappings: XsdElement
): string {
  if (!conditions) {
    throw new Error(`Conditions must be provided for condition rules.`);
  }
  if (!conditionsConnector) {
    throw new Error(
      `Conditions connector must be provided for condition rules.`
    );
  }
  if (!conditionalRules) {
    throw new Error(`Conditional rules must be provided for condition rules.`);
  }
  if (!conditionalRulesConnector) {
    throw new Error(
      `Conditional rules connector must be provided for condition rules.`
    );
  }
  const conditionalExplanation = generateConditionalExplanation(
    conditions,
    conditionsConnector,
    lang,
    nameMappings
  );
  let nestedRuleDescription = '';

  const firstConditionalRule = conditionalRules[0];
  if (
    !conditionalRules.every((rule) => rule.type === firstConditionalRule.type)
  ) {
    throw new Error(
      `All conditional rules must have the same type. Found: ${conditionalRules
        .map((rule) => rule.type)
        .join(', ')}.`
    );
  }
  if (
    'value' in firstConditionalRule &&
    !conditionalRules.every(
      (rule) => 'value' in rule && rule.value === firstConditionalRule.value
    )
  ) {
    throw new Error(`All conditional rules must have the same value.`);
  }
  if (
    'isEqual' in firstConditionalRule &&
    !conditionalRules.every(
      (rule) =>
        'isEqual' in rule && rule.isEqual === firstConditionalRule.isEqual
    )
  ) {
    throw new Error(`All conditional rules must have the same isEqual value.`);
  }
  switch (firstConditionalRule.type) {
    case 'required':
      nestedRuleDescription = generateRuleDescription(
        'required',
        nestedFieldName,
        nameMappings,
        lang,
        {
          ruleDependentValue: undefined,
          isForConditionalRuleDescription: true,
        }
      );
      break;
    case 'prohibited':
      nestedRuleDescription = generateRuleDescription(
        'prohibited',
        nestedFieldName,
        nameMappings,
        lang,
        {
          ruleDependentValue: undefined,
          isForConditionalRuleDescription: true,
        }
      );
      break;
    case 'maxLength':
      nestedRuleDescription = generateRuleDescription(
        'maxLength',
        nestedFieldName,
        nameMappings,
        lang,
        {
          ruleDependentValue: firstConditionalRule.value,
          isForConditionalRuleDescription: true,
        }
      );
      break;
    case 'value':
      const firstValueRule: ValueRule<string | undefined, string | undefined> =
        firstConditionalRule as ValueRule;
      nestedRuleDescription = generateRuleDescription(
        'value',
        nestedFieldName,
        nameMappings,
        lang,
        {
          ruleDependentValue: firstValueRule.value,
          secondRuleDependentValue: firstValueRule.isEqual,
          isForConditionalRuleDescription: true,
        }
      );
      break;
    case 'maxItems':
      nestedRuleDescription = generateRuleDescription(
        'maxItems',
        nestedFieldName,
        nameMappings,
        lang,
        {
          ruleDependentValue: firstConditionalRule.value,
          isForConditionalRuleDescription: true,
        }
      );
      break;
    default:
      throw new Error(
        `Unsupported conditional rule type "${firstConditionalRule.type}" for condition rules.`
      );
  }
  // Return immediately. There is no conditional explanation in the config to attach and the nestedRuleDescription already contains the period.
  return lang === 'en'
    ? `If ${conditionalExplanation}, then ${nestedRuleDescription}`
    : `Wenn ${conditionalExplanation}, dann ${nestedRuleDescription}`;
}

function removeSharedPrefix(
  fieldName: string,
  nameMappings: XsdElement
): string {
  const prefixesToRemove = getSharedPrefixes(nameMappings);
  return fieldName.replace(
    new RegExp(`^${prefixesToRemove}${FIELD_NAME_SEPARATOR}`),
    ''
  );
}

function getSharedPrefixes(nameMappings: XsdElement): string {
  if (nameMappings.children.length !== 1) {
    return nameMappings.nestedAbbrName;
  }
  return getSharedPrefixes(nameMappings.children[0]);
}

export function generateConditionalExplanation(
  conditions: Condition[],
  conditionsConnector: ConditionsConnector,
  lang: 'en' | 'de',
  nameMappings: XsdElement
): string {
  if (conditions.length === 0) {
    return `'no conditions'`;
  }

  if (conditions.every(isBasicCondition)) {
    const presentConditions = conditions.filter(
      (cond) => cond.type === 'present' && cond.value === true
    );
    const absentConditions = conditions.filter(
      (cond) => cond.type === 'present' && cond.value === false
    );
    const valueConditions = conditions.filter((cond) => cond.type === 'value');
    const equalConditions = conditions.filter(
      (cond) => cond.type === 'equal' && cond.value === true
    );
    const notEqualConditions = conditions.filter(
      (cond) => cond.type === 'equal' && cond.value === false
    );
    const greaterThanConditions = conditions.filter(
      (cond) => cond.type === 'greaterThan'
    );

    const explanations: string[] = [
      getConditionalExplanationForConditionType(
        presentConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'present'
      ),
      getConditionalExplanationForConditionType(
        absentConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'present'
      ),
      getConditionalExplanationForConditionType(
        valueConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'value'
      ),
      getConditionalExplanationForConditionType(
        equalConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'equal'
      ),
      getConditionalExplanationForConditionType(
        notEqualConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'equal'
      ),
      getConditionalExplanationForConditionType(
        greaterThanConditions,
        conditionsConnector,
        lang,
        nameMappings,
        'greaterThan'
      ),
    ].filter((explanation) => explanation !== undefined);

    return explanations.join(
      ` ${translateConnector(conditionsConnector, lang)} `
    );
  } else {
    // There is at least one nested condition.
    const basicConditions = conditions.filter(isBasicCondition);
    const nonNestedConditionalExplanation = generateConditionalExplanation(
      basicConditions,
      conditionsConnector,
      lang,
      nameMappings
    );
    const nestedConditions = conditions.filter(isNestedCondition);
    const nestedConditionalExplanations = nestedConditions.map((nestedCond) =>
      generateConditionalExplanation(
        nestedCond.conditions,
        nestedCond.conditionsConnector ?? 'and',
        lang,
        nameMappings
      )
    );
    return [
      nonNestedConditionalExplanation,
      ...nestedConditionalExplanations,
    ].join(` ${conditionsConnector} `);
  }
}

function getConditionalExplanationForConditionType(
  conditions: BasicCondition[],
  conditionsConnector: ConditionsConnector,
  lang: 'en' | 'de',
  nameMappings: XsdElement,
  conditionType: BasicConditionType
): string | undefined {
  if (conditions.length === 0) {
    return undefined;
  }

  const conditionFieldNames = conditions.map((cond) => cond.field);
  const concatenatedConditionFields = concatenateFieldNames(
    conditionFieldNames,
    conditionsConnector,
    lang,
    nameMappings
  );
  // Go through the nameMappings and remove prefixes that are included in all nested field names.
  const truncatedConcatenatedConditionFields = removeSharedPrefix(
    concatenatedConditionFields,
    nameMappings
  );

  const concatenatedStringHasMultipleConditionFields =
    truncatedConcatenatedConditionFields.includes(
      CONCATENATED_FIELDS_SUFFIX_SEPARATORS[0]
    );

  if (conditionType === 'present') {
    // Only for typescript
    if (!conditions.every((cond) => 'value' in cond)) {
      throw new Error(
        `All conditions must have a 'value' property for present type conditions.`
      );
    }
    const conditionValue = conditions[0].value;
    if (!conditions.every((cond) => cond.value === conditionValue)) {
      throw new Error(
        `This function expects to have all 'present' conditions to have the same value. Please split them up correctly before handing them to this function.`
      );
    }

    if (conditionValue === true) {
      if (!concatenatedStringHasMultipleConditionFields) {
        return lang === 'en'
          ? `${truncatedConcatenatedConditionFields} is present`
          : `${truncatedConcatenatedConditionFields} vorhanden ist`;
      }
      if (conditionsConnector === 'and') {
        return lang === 'en'
          ? `all of the fields ${truncatedConcatenatedConditionFields} are present`
          : `alle Felder ${truncatedConcatenatedConditionFields} vorhanden sind`;
      } else if (conditionsConnector === 'or') {
        return lang === 'en'
          ? `any of the fields ${truncatedConcatenatedConditionFields} is present`
          : `mindestens eines der Felder ${truncatedConcatenatedConditionFields} vorhanden ist`;
      }
    } else if (conditionValue === false) {
      if (!concatenatedStringHasMultipleConditionFields) {
        return lang === 'en'
          ? `${truncatedConcatenatedConditionFields} is not present`
          : `${truncatedConcatenatedConditionFields} nicht vorhanden ist`;
      }
      if (conditionsConnector === 'and') {
        return lang === 'en'
          ? `none of the fields ${truncatedConcatenatedConditionFields} is present`
          : `keines der Felder ${truncatedConcatenatedConditionFields} vorhanden ist`;
      }
    }
  } else if (conditionType === 'value') {
    // Only for typescript
    if (!conditions.every((cond) => 'value' in cond)) {
      throw new Error(
        `All conditions must have a 'value' property for value type conditions.`
      );
    }
    const firstConditionValue = conditions[0].value;
    if (conditions.every((cond) => cond.value === firstConditionValue)) {
      return lang === 'en'
        ? `${truncatedConcatenatedConditionFields} has the value "${firstConditionValue}"`
        : `${truncatedConcatenatedConditionFields} den Wert "${firstConditionValue}" hat`;
    }
    if (!concatenatedStringHasMultipleConditionFields) {
      // All fields are the same, only the value is different.
      const conditionValues = conditions
        .map((cond) => `"${cond.value}"`)
        .join(` ${translateConnector(conditionsConnector, lang)} `);
      return lang === 'en'
        ? `${truncatedConcatenatedConditionFields} has one of the values ${conditionValues}`
        : `${truncatedConcatenatedConditionFields} einen der Werte ${conditionValues} hat`;
    }
    // There are multiple condition fields with different values.
    return conditions
      .map((cond) =>
        lang === 'en'
          ? `${removeSharedPrefix(cond.field, nameMappings)} has the value "${
              cond.value
            }"`
          : `${removeSharedPrefix(cond.field, nameMappings)} den Wert "${
              cond.value
            }" hat`
      )
      .join(` ${translateConnector(conditionsConnector, lang)} `);
  } else if (conditionType === 'equal') {
    // Only for typescript
    if (!conditions.every((cond) => 'value' in cond)) {
      throw new Error(
        `All conditions must have a 'value' property for equal type conditions.`
      );
    }
    if (!conditions.every((cond) => 'otherField' in cond)) {
      throw new Error(
        `All conditions must have a 'otherField' property for equal type conditions.`
      );
    }

    const conditionValue = conditions[0].value;
    if (!conditions.every((cond) => cond.value === conditionValue)) {
      throw new Error(
        `This function expects to have all 'equal' conditions to have the same value. Please split them up correctly before handing them to this function.`
      );
    }

    const firstConditionOtherField = conditions[0].otherField;

    if (conditionValue === true) {
      if (
        conditions.every((cond) => cond.otherField === firstConditionOtherField)
      ) {
        return lang === 'en'
          ? `${truncatedConcatenatedConditionFields} has the same value as the field ${removeSharedPrefix(
              firstConditionOtherField,
              nameMappings
            )}`
          : `${truncatedConcatenatedConditionFields} den gleichen Wert hat wie das Feld ${removeSharedPrefix(
              firstConditionOtherField,
              nameMappings
            )}`;
      }

      if (!concatenatedStringHasMultipleConditionFields) {
        // All fields are the same, only the otherField is different.
        const conditionOtherFields = conditions
          .map(
            (cond) => `"${removeSharedPrefix(cond.otherField, nameMappings)}"`
          )
          .join(` ${translateConnector(conditionsConnector, lang)} `);
        return lang === 'en'
          ? `${truncatedConcatenatedConditionFields} has the same value as one of the fields ${conditionOtherFields}`
          : `${truncatedConcatenatedConditionFields} den gleichen Wert hat wie eines der Felder ${conditionOtherFields}`;
      }

      // There are multiple condition fields with different otherFields
      return conditions
        .map((cond) =>
          lang === 'en'
            ? `${removeSharedPrefix(
                cond.field,
                nameMappings
              )} has the same value as the field "${removeSharedPrefix(
                cond.otherField,
                nameMappings
              )}"`
            : `${removeSharedPrefix(
                cond.field,
                nameMappings
              )} den gleichen Wert hat wie das Feld "${removeSharedPrefix(
                cond.otherField,
                nameMappings
              )}"`
        )
        .join(` ${translateConnector(conditionsConnector, lang)} `);
    }
  } else if (conditionType === 'greaterThan') {
    // Only for typescript
    if (!conditions.every((cond) => 'otherField' in cond)) {
      throw new Error(
        `All conditions must have a 'otherField' property for equal type conditions.`
      );
    }

    const firstConditionOtherField = conditions[0].otherField;

    if (
      conditions.every((cond) => cond.otherField === firstConditionOtherField)
    ) {
      return lang === 'en'
        ? `${truncatedConcatenatedConditionFields} is greater than ${firstConditionOtherField}`
        : `${truncatedConcatenatedConditionFields} größer ist als ${firstConditionOtherField}`;
    }

    if (!concatenatedStringHasMultipleConditionFields) {
      // All fields are the same, only the otherField is different.
      const conditionOtherFields = conditions
        .map((cond) => `"${cond.otherField}"`)
        .join(` ${translateConnector(conditionsConnector, lang)} `);
      return lang === 'en'
        ? `${truncatedConcatenatedConditionFields} is greater than ${conditionOtherFields}`
        : `${truncatedConcatenatedConditionFields} größer ist als ${conditionOtherFields}`;
    }

    // There are multiple condition fields with different otherFields
    return conditions
      .map((cond) =>
        lang === 'en'
          ? `${removeSharedPrefix(cond.field, nameMappings)} is greater than "${
              cond.otherField
            }"`
          : `${removeSharedPrefix(cond.field, nameMappings)} größer ist als "${
              cond.otherField
            }"`
      )
      .join(` ${translateConnector(conditionsConnector, lang)} `);
  }

  throw new Error(
    `Unsupported conditions for conditional explanation generation: ${JSON.stringify(
      conditions
    )}, conditionsConnector: ${conditionsConnector}`
  );
}

export function getBasicRuleHash(
  basicRule: BasicRule<undefined, string | undefined>
): string {
  const target = basicRule.target;
  const type = basicRule.type;
  const value =
    type === 'value'
      ? `${basicRule.value}|${basicRule.isEqual}`
      : type === 'required' || type === 'prohibited'
      ? ''
      : `${basicRule.value}`;

  return getHash(`${target}|${type}|${value}`);
}

function getHash(input: string): string {
  // DJB2 hash
  let hash = 5381;
  for (let i = 0; i < input.length; i++) {
    hash = (hash * 33) ^ input.charCodeAt(i);
  }

  // Convert to unsigned 32-bit and return as hex string
  return (hash >>> 0).toString(16);
}

/**
 * Merge multiple field names into one, to be used in rule IDs.
 * @param fieldNames a list of field names
 * @param nameMappings the XSD element mappings to determine whether all children of a big element are included in the list
 * @return the merged field name
 * @example
 * getMergedFieldName(['FIToFICstmrCdtTrf-GrpHdr-MsgId', 'FIToFICstmrCdtTrf-GrpHdr-CreDtTm']) -> 'FIToFICstmrCdtTrf-GrpHdr-[MsgId_CreDtTm]'
 */
export function getMergedFieldName(
  fieldNames: string[],
  nameMappings: XsdElement
): string {
  if (fieldNames.length === 0) {
    throw new Error(`Cannot merge field names: no field names provided.`);
  }

  const sharedBase = longestCommonPrefix(fieldNames);
  const fieldNameSuffixes = fieldNames.map((fieldName) =>
    fieldName.slice(sharedBase.length)
  );
  const nonEmptySuffixes = fieldNameSuffixes
    .map((suffix) => suffix.replace(/^-/, ''))
    .filter((suffix) => suffix.length > 0);

  // 'nonEmptySuffixes' might be a long list if it includes all children of a big element, e.g. 'InstgRmbrsmntAgt-FinInstnId-BICFI', 'InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd', etc.
  // So we use the 'nameMappings' to figure out whether all children of, e.g. 'InstgRmbrsmntAgt' are in the list. In that case we only keep 'InstgRmbrsmntAgt'.
  const selectedFieldNameSuffixes: string[] = Array.from(
    selectFieldNameSuffixes(nonEmptySuffixes, sharedBase, nameMappings)
  );

  const joinedSelectedFieldNameSuffixes =
    selectedFieldNameSuffixes.length === 0
      ? ''
      : selectedFieldNameSuffixes.length === 1
      ? selectedFieldNameSuffixes[0]
      : `${MERGED_FIELDS_SUFFIX_SEPARATORS[0]}${selectedFieldNameSuffixes.join(
          MERGED_FIELDS_SUFFIX_CONNECTOR
        )}${MERGED_FIELDS_SUFFIX_SEPARATORS[1]}`;

  const hasNonEmptySuffixes = joinedSelectedFieldNameSuffixes.length > 0;

  return `${sharedBase.replace(/-$/, '')}${
    sharedBase && hasNonEmptySuffixes ? '-' : ''
  }${joinedSelectedFieldNameSuffixes}`;
}

/**
 * Merge multiple field names into an expression that can be used in rule descriptions.
 * @param fieldNames a list of field names
 * @param connector the connector to use between field names, either 'and' or 'or'
 * @param nameMappings the XSD element mappings to determine whether all children of a big element are included in the list
 * @return the merged field name expression
 * concatenateFieldNames(['Document-FIToFICstmrCdtTrf-GrpHdr-MsgId', 'Document-FIToFICstmrCdtTrf-GrpHdr-CreDtTm'], 'and') -> 'Document-FIToFICstmrCdtTrf-GrpHdr-MsgId or -CreDtTm'
 */
export function concatenateFieldNames(
  fieldNames: string[],
  connector: 'and' | 'or',
  lang: 'en' | 'de',
  nameMappings: XsdElement
): string {
  if (fieldNames.length === 0) {
    throw new Error(`Cannot concatenate field names: no field names provided.`);
  }

  const sharedBase = longestCommonPrefix(fieldNames);
  const fieldNameSuffixes = fieldNames.map((fieldName) =>
    fieldName.slice(sharedBase.length)
  );
  const nonEmptySuffixes = fieldNameSuffixes
    .map((suffix) => suffix.replace(/^-/, ''))
    .filter((suffix) => suffix.length > 0);

  // 'nonEmptySuffixes' might be a long list if it includes all children of a big element, e.g. 'InstgRmbrsmntAgt-FinInstnId-BICFI', 'InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd', etc.
  // So we use the 'nameMappings' to figure out whether all children of, e.g. 'InstgRmbrsmntAgt' are in the list. In that case we only keep 'InstgRmbrsmntAgt'.
  const selectedFieldNameSuffixes: string[] = Array.from(
    selectFieldNameSuffixes(nonEmptySuffixes, sharedBase, nameMappings)
  );

  const joinedSelectedFieldNameSuffixes =
    selectedFieldNameSuffixes.length === 0
      ? ''
      : selectedFieldNameSuffixes.length === 1
      ? selectedFieldNameSuffixes[0]
      : `${
          CONCATENATED_FIELDS_SUFFIX_SEPARATORS[0]
        }${selectedFieldNameSuffixes.join(
          ` ${translateConnector(connector, lang)} `
        )}${CONCATENATED_FIELDS_SUFFIX_SEPARATORS[1]}`;

  const hasNonEmptySuffixes = joinedSelectedFieldNameSuffixes.length > 0;

  return `${sharedBase.replace(/-$/, '')}${
    sharedBase && hasNonEmptySuffixes ? '-' : ''
  }${joinedSelectedFieldNameSuffixes}`;
}

function translateConnector(
  connector: 'and' | 'or',
  lang: 'en' | 'de'
): string {
  if (lang === 'en') {
    return connector;
  }
  return connector === 'and' ? 'und' : 'oder';
}

function longestCommonPrefix(strings: string[]): string {
  if (!strings.length) return '';

  // Split the first string into parts as baseline
  const baseParts = strings[0].split('-');

  let commonLength = baseParts.length;

  for (let i = 1; i < strings.length; i++) {
    const parts = strings[i].split('-');
    let j = 0;
    while (j < commonLength && j < parts.length && baseParts[j] === parts[j]) {
      j++;
    }
    // Reduce the number of common segments
    commonLength = j;
    if (commonLength === 0) return '';
  }

  return baseParts.slice(0, commonLength).join('-');
}

function selectFieldNameSuffixes(
  fieldNameSuffixes: string[],
  sharedBase: string,
  nameMappings: XsdElement
): Set<string> {
  const selectedFieldNameSuffixes = new Set<string>();
  // Check whether the shared base already covers all children.
  const allChildrenOfSharedBaseAreIncludedInFieldNameSuffixes =
    determineWhetherAllChildrenAreIncluded(
      sharedBase,
      '',
      fieldNameSuffixes,
      nameMappings
    );

  if (allChildrenOfSharedBaseAreIncludedInFieldNameSuffixes) {
    return selectedFieldNameSuffixes;
  }

  for (const suffix of fieldNameSuffixes) {
    // Create a list of possible suffixes, e.g. "test1-test2-test3" -> ["test1", "test1-test2", "test1-test-2-test3"]
    const suffixParts = suffix.split('-');
    const possibleSuffixes = suffixParts.map((_, index) =>
      suffixParts.slice(0, index + 1).join('-')
    );
    for (const [index, possibleSuffix] of possibleSuffixes.entries()) {
      // If any of the previous possible suffixes is already included, we skip this one.
      const previousSuffixes = possibleSuffixes.slice(0, index);
      if (
        previousSuffixes.some((prevSuffix) =>
          selectedFieldNameSuffixes.has(prevSuffix)
        )
      ) {
        continue;
      }

      const allChildrenOfPossibleSuffixAreIncludedInFieldNameSuffixes =
        determineWhetherAllChildrenAreIncluded(
          sharedBase,
          possibleSuffix,
          fieldNameSuffixes,
          nameMappings
        );

      if (allChildrenOfPossibleSuffixAreIncludedInFieldNameSuffixes) {
        selectedFieldNameSuffixes.add(possibleSuffix);
      }
    }
  }

  return selectedFieldNameSuffixes;
}

function determineWhetherAllChildrenAreIncluded(
  sharedBase: string,
  suffix: string,
  allSuffixes: string[],
  nameMappings: XsdElement
): boolean {
  const truncatedSharedBase = sharedBase.replace(/-$/, '');
  if (!truncatedSharedBase) {
    throw new Error(
      `There should be some shared base, at least the source element ${nameMappings.nestedAbbrName}.`
    );
  }
  const baseNameMappingElement = nameMappings
    ? findNameMappingElement(nameMappings, truncatedSharedBase)
    : undefined;
  if (!baseNameMappingElement) {
    throw new Error(
      `Base name mapping element "${truncatedSharedBase}" not found in name mappings.`
    );
  }

  const suffixNameMappingElement = findNameMappingElement(
    baseNameMappingElement,
    `${truncatedSharedBase}${suffix ? '-' : ''}${suffix}`
  );
  if (!suffixNameMappingElement) {
    throw new Error(
      `Suffix name mapping element "${truncatedSharedBase}${
        suffix ? '-' : ''
      }${suffix}" not found in name mappings.`
    );
  }

  const allChildren = findAllChildren(suffixNameMappingElement);
  const allFieldNames = allSuffixes.map(
    (suffix) => `${truncatedSharedBase}-${suffix}`
  );
  for (const child of allChildren) {
    if (!allFieldNames.includes(child)) {
      // If any child is not included in the list of field names, we return false.
      return false;
    }
  }

  return true;
}

function findNameMappingElement(
  nameMappings: XsdElement,
  nestedAbbrName: string
): XsdElement | undefined {
  if (nameMappings.nestedAbbrName === nestedAbbrName) {
    return nameMappings;
  }

  for (const child of nameMappings.children) {
    const foundElement = findNameMappingElement(child, nestedAbbrName);
    if (foundElement) {
      return foundElement;
    }
  }

  return undefined;
}

function findAllChildren(nameMappings: XsdElement): string[] {
  const children: string[] = [];
  for (const child of nameMappings.children) {
    if (child.children.length === 0) {
      children.push(child.nestedAbbrName);
    }
    children.push(...findAllChildren(child));
  }
  return children;
}

export function moveToFront(arr: string[], target: string): string[] {
  const index = arr.indexOf(target);
  if (index === -1) {
    throw new Error(`Element "${target}" not found in array.`);
  }
  return [target, ...arr.slice(0, index), ...arr.slice(index + 1)];
}
