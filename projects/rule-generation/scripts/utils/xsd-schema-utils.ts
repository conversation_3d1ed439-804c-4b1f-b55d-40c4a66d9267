import {
  BasicRule,
  Condition,
  Rule,
  XsdElement,
} from '@helaba/iso20022-lib/rules';
import { isNumberString } from '@helaba/iso20022-lib/util';
import { Document, Element } from '@xmldom/xmldom';
import {
  generateRuleDescription,
  generateRuleId,
} from './rule-generation-utils';
import { GENERATED_RULE_PREFIX } from './constants';

export function transformSingleOptionsChoiceToSequences(doc: Document) {
  const choiceElements = Array.from(doc.getElementsByTagName('xs:choice'));

  for (let i = 0; i < choiceElements.length; i++) {
    const choice = choiceElements[i];

    const elementChildren = Array.from(choice.childNodes).filter(
      (node) =>
        node.nodeType === 1 && (node as Element).tagName === 'xs:element'
    );

    if (elementChildren.length === 1) {
      const sequence = doc.createElement('xs:sequence');

      sequence.appendChild(elementChildren[0]);

      const parent = choice.parentNode as Element;
      if (parent) {
        parent.replaceChild(sequence, choice);
        console.info(
          `Replace choice with sequence for ${parent.getAttribute('name')}`
        );
      }
    }
  }
}

export function generateRules(
  xsdSchema: Document,
  nameMappings: XsdElement
): Rule[] {
  const rules: Rule[] = [];

  const schema = Array.from(xsdSchema.getElementsByTagName('xs:schema'))[0];
  if (!schema) {
    throw new Error(`No Schema found`);
  }
  const rootElements = getRootLevelElements(schema);

  if (rootElements.length > 1) {
    throw new Error(
      `xsd:schema should only have one root xs:element, found: ${rootElements.length}`
    );
  }
  const name = rootElements[0].getAttribute('name');
  const type = rootElements[0].getAttribute('type');

  if (!name || !type) {
    throw new Error(`Root element must have name and type`);
  }

  const allTypes = [
    ...Array.from(xsdSchema.getElementsByTagName('xs:complexType')),
    ...Array.from(xsdSchema.getElementsByTagName('xs:simpleType')),
  ];

  // For debugging purposes
  // console.debug(`Found ${name} with type ${type}`);

  handleTypes(type, name, allTypes, rules, nameMappings, false);

  return rules;
}

function handleTypes(
  typeName: string,
  curPath: string,
  allTypes: Array<Element>,
  rules: Array<Rule>,
  nameMappings: XsdElement,
  optionalPath: boolean = false
) {
  const type = allTypes.find(
    (typeEl) => typeEl.getAttribute('name') === typeName
  );

  if (!type) {
    throw new Error(`Cannot find type '${typeName}' in schema`);
  }

  if (type.tagName === 'xs:simpleType') {
    handleSimpleType(
      type,
      curPath,
      allTypes,
      rules,
      nameMappings,
      optionalPath
    );
  } else {
    handleComplexType(
      type,
      curPath,
      allTypes,
      rules,
      nameMappings,
      optionalPath
    );
  }
}

function handleSimpleType(
  simpleType: Element,
  curPath: string,
  allTypes: Array<Element>,
  rules: Array<Rule>,
  nameMappings: XsdElement,
  optionalPath: boolean
) {
  // For debugging purposes
  // console.debug(`Handle simpleType ${simpleType.getAttribute('name')} (${curPath})`)

  const restriction = simpleType.getElementsByTagName('xs:restriction')[0];
  if (!restriction) {
    console.warn(`No restriction defined for  ...`);
  }
  const baseType = restriction.getAttribute('base');
  if (!baseType) {
    console.warn(`No baseType defined for  ...`);
  }

  switch (baseType) {
    // TODO: add xs:data, xs:time, xs:dateTime with default pattern
    case 'xs:string':
      for (const childNode of Array.from(restriction.childNodes)) {
        if (childNode.nodeType === 1) {
          const child = childNode as Element;
          const tagName = child.tagName;
          let value = child.getAttribute('value');

          switch (tagName) {
            case 'xs:minLength':
              if (
                value &&
                isNumberString(value) &&
                parseInt(value) > 0 &&
                !optionalPath
              ) {
                rules.push({
                  id: generateRuleId(
                    'required',
                    GENERATED_RULE_PREFIX,
                    curPath
                  ),
                  description: generateRuleDescription(
                    'required',
                    curPath,
                    nameMappings,
                    'en',
                    {
                      ruleDependentValue: undefined,
                      conditionalExplanation: undefined,
                    }
                  ),
                  descriptionTranslationProposal: generateRuleDescription(
                    'required',
                    curPath,
                    nameMappings,
                    'de',
                    {
                      ruleDependentValue: undefined,
                      conditionalExplanation: undefined,
                    }
                  ),
                  type: 'required',
                  target: curPath,
                });
              }
              break;
            case 'xs:maxLength':
              if (value && isNumberString(value)) {
                rules.push({
                  id: generateRuleId(
                    'maxLength',
                    GENERATED_RULE_PREFIX,
                    curPath
                  ),
                  description: generateRuleDescription(
                    'maxLength',
                    curPath,
                    nameMappings,
                    'en',
                    {
                      ruleDependentValue: parseInt(value),
                    }
                  ),
                  descriptionTranslationProposal: generateRuleDescription(
                    'maxLength',
                    curPath,
                    nameMappings,
                    'de',
                    {
                      ruleDependentValue: parseInt(value),
                    }
                  ),
                  type: 'maxLength',
                  value: parseInt(value),
                  target: curPath,
                });
              }
              break;
            case 'xs:pattern':
              if (value) {
                rules.push({
                  id: generateRuleId('pattern', GENERATED_RULE_PREFIX, curPath),
                  description: generateRuleDescription(
                    'pattern',
                    curPath,
                    nameMappings,
                    'en',
                    {
                      ruleDependentValue: value,
                    }
                  ),
                  descriptionTranslationProposal: generateRuleDescription(
                    'pattern',
                    curPath,
                    nameMappings,
                    'de',
                    {
                      ruleDependentValue: value,
                    }
                  ),
                  type: 'pattern',
                  value: value,
                  target: curPath,
                });
              }
              break;
          }
        }
      }
      break;
    case 'xs:boolean':
      if (!optionalPath) {
        rules.push({
          id: generateRuleId('required', GENERATED_RULE_PREFIX, curPath),
          description: generateRuleDescription(
            'required',
            curPath,
            nameMappings,
            'en',
            {
              ruleDependentValue: undefined,
              conditionalExplanation: undefined,
            }
          ),
          descriptionTranslationProposal: generateRuleDescription(
            'required',
            curPath,
            nameMappings,
            'de',
            {
              ruleDependentValue: undefined,
              conditionalExplanation: undefined,
            }
          ),
          type: 'required',
          target: curPath,
        });
      }
      break;
  }
}

function handleComplexType(
  complexType: Element,
  curPath: string,
  allTypes: Array<Element>,
  rules: Array<Rule>,
  nameMappings: XsdElement,
  optionalPath: boolean
) {
  // For debugging purposes
  // console.debug(`Handle complexType ${complexType.getAttribute('name')} (${curPath})`);

  let found = false;

  for (let i = 0; i < complexType.childNodes.length; i++) {
    const node = complexType.childNodes[i];
    if (node.nodeType !== 1) continue; // Nur Element-Knoten

    const child = node as Element;
    const tag = child.tagName;

    // For debugging purposes
    // console.debug(`→ Found ${tag} in ${complexType.getAttribute('name')}`);
    switch (tag) {
      case 'xs:sequence':
        found = true;
        handleSequence(
          child,
          curPath,
          allTypes,
          rules,
          nameMappings,
          optionalPath
        );
        break;

      case 'xs:choice':
        // found = true;
        // handleChoice(child, curPath, allTypes, rules, optionalPath);
        break;

      case 'xs:simpleContent':
        // found = true;
        // TODO: simpleContent(child);
        // extension of simple type -> "-amount" +  attribute -> "->Ccy"
        break;

      case 'xs:annotation':
        //Ignore annotations
        break;

      default:
        console.warn(
          `Unexpected child <${tag}> in complexType '${complexType.getAttribute(
            'name'
          )}'`
        );
        break;
    }
  }

  if (!found) {
    throw new Error(
      `No supportred tag found in complexType '${complexType.getAttribute(
        'name'
      )}'`
    );
  }
}

function handleSequence(
  sequence: Element,
  curPath: string,
  allTypes: Array<Element>,
  rules: Array<Rule>,
  nameMappings: XsdElement,
  optionalPath: boolean
) {
  const elements = getRootLevelElements(sequence);

  const conditionalsRequired: Array<string> = [];
  const allPaths: Array<string> = [];

  for (const elem of elements) {
    if (elem.getAttribute('type')) {
      const path = curPath
        ? curPath + `-${elem.getAttribute('name')}`
        : `${elem.getAttribute('name')}`;

      const isOptional: boolean =
        elem.getAttribute('minOccurs') === '0' || optionalPath;

      const pathSegments = path.split('-');
      allPaths.push(...getSimpleTypePaths(elem, allTypes, pathSegments));

      if (
        elem.getAttribute('minOccurs') === '1' ||
        !elem.hasAttribute('minOccurs')
      ) {
        // this path is requried
        conditionalsRequired.push(
          ...getSimpleTypePaths(elem, allTypes, pathSegments)
        );
      }

      handleTypes(
        elem.getAttribute('type')!,
        path,
        allTypes,
        rules,
        nameMappings,
        isOptional
      );
    } else {
      throw new Error(`Element has no type`);
    }
  }

  // Generate conditinals
  if (optionalPath && conditionalsRequired.length > 0 && elements.length > 1) {
    const conditions = allPaths.map((path): Condition => {
      return {
        field: path,
        type: 'present',
        value: true,
      };
    });

    const conditionalParentRuleId = generateRuleId(
      'condition',
      GENERATED_RULE_PREFIX,
      curPath,
      { conditions: conditions, conditionalRuleType: 'required' }
    );

    rules.push({
      id: conditionalParentRuleId,
      description: '',
      descriptionTranslationProposal: '',
      type: 'condition',
      conditionsConnector: 'or',
      rulesConnector: 'and',
      conditions: conditions,
      rules: conditionalsRequired.map((path): BasicRule => {
        return {
          id: generateRuleId('required', GENERATED_RULE_PREFIX, path, {
            isConditional: true,
            conditionalParentRuleId: conditionalParentRuleId,
          }),
          description: '',
          descriptionTranslationProposal: '',
          target: path,
          type: 'required',
        };
      }),
    });
  }
}

function handleChoice(
  choice: Element,
  curPath: string,
  allTypes: Array<Element>,
  rules: Array<Rule>,
  nameMappings: XsdElement,
  optionalPath: boolean
) {
  const pathTmp = curPath;
  const elements = getRootLevelElements(choice);

  // For debugging purposes
  // console.debug(`Handle choice ${choice.parentElement!.getAttribute('name')} (${curPath}) -> ${elements.length}`);

  // Make for every choice the basic rules
  for (const elem of elements) {
    const typeName = elem.getAttribute('type');
    if (typeName) {
      const path = curPath
        ? curPath + `-${elem.getAttribute('name')}`
        : `${elem.getAttribute('name')}`;
      handleTypes(typeName, path, allTypes, rules, nameMappings, optionalPath);
    } else {
      throw new Error(`Element has no type`);
    }
  }

  // Create exclusive conditions
  for (const elem in elements) {
    const typeName = elements[elem].getAttribute('type');
    console.info(`Choice: ${pathTmp} (${elem} - ${typeName})`);
    for (let p of getSimpleTypePaths(elements[elem], allTypes)) {
      console.info(`\t=>${p}`);
    }
  }

  // TODO: get elem one above and create conditional rule
  // if one property of one choice is present, all proerties of the other choices are prohibited
}

function getSimpleTypePaths(
  base: Element,
  allTypes: Element[],
  currentPath: string[] = []
): string[] {
  const baseType = base.getAttribute('type');
  const found = baseType
    ? allTypes.find((typeEl) => typeEl.getAttribute('name') === baseType)
    : null;

  if (found?.tagName === 'xs:simpleType') {
    return [currentPath.join('-')];
  }

  if (found?.tagName === 'xs:complexType') {
    let results: string[] = [];

    for (let i = 0; i < found.childNodes.length; i++) {
      const node = found.childNodes[i];
      if (node.nodeType !== 1) continue;

      const child = node as Element;
      const tag = child.tagName;

      if (tag === 'xs:choice' || tag === 'xs:sequence') {
        for (let j = 0; j < child.childNodes.length; j++) {
          const node2 = child.childNodes[j];
          if (node2.nodeType !== 1) continue;

          const elem = node2 as Element;
          const name = elem.getAttribute('name') || elem.getAttribute('ref');
          if (!name) continue;

          results.push(
            ...getSimpleTypePaths(elem, allTypes, [...currentPath, name])
          );
        }
      }
    }

    return results;
  }

  // If base itself is a simpleType (not via type reference)
  if (base.tagName === 'xs:simpleType') {
    return [currentPath.join('-')];
  }

  return [];
}

function getRootLevelElements(schema: Element): Element[] {
  const rootElements: Element[] = [];

  for (let i = 0; i < schema.childNodes.length; i++) {
    const node = schema.childNodes[i];
    if (
      node.nodeType === 1 && // ELEMENT_NODE
      (node as Element).tagName === 'xs:element'
    ) {
      rootElements.push(node as Element);
    }
  }

  return rootElements;
}

export function extractElementPaths(xsdDoc: Document): Record<string, Element> {
  const elementMap: Record<string, Element> = {};

  const complexTypes = Array.from(
    xsdDoc.getElementsByTagName('xs:complexType')
  );
  const simpleTypes = Array.from(xsdDoc.getElementsByTagName('xs:simpleType'));

  const complexTypeMap = new Map<string, Element>();
  const simpleTypeMap = new Map<string, Element>();

  for (const ct of complexTypes) {
    const name = ct.getAttribute('name');
    if (name) complexTypeMap.set(name, ct);
  }

  for (const st of simpleTypes) {
    const name = st.getAttribute('name');
    if (name) simpleTypeMap.set(name, st);
  }

  const rootElement = Array.from(
    xsdDoc.getElementsByTagName('xs:element')
  ).find((el) => el.getAttribute('name') === 'Document');

  if (!rootElement) return elementMap;

  const rootType = rootElement.getAttribute('type');
  if (!rootType || !complexTypeMap.has(rootType)) return elementMap;

  traverseType(rootType, [], elementMap, complexTypeMap, simpleTypeMap);

  return elementMap;
}

function traverseType(
  typeName: string,
  path: string[],
  result: Record<string, Element>,
  complexTypeMap: Map<string, Element>,
  simpleTypeMap: Map<string, Element>
) {
  const typeNode = complexTypeMap.get(typeName);
  if (!typeNode) return;

  // Handle simpleContent with extension
  const simpleContent = typeNode.getElementsByTagName('xs:simpleContent')[0];
  if (simpleContent) {
    const extension = simpleContent.getElementsByTagName('xs:extension')[0];
    if (extension) {
      const baseType = extension.getAttribute('base');
      if (baseType && simpleTypeMap.has(baseType)) {
        const pathKey = [...path, 'amount'].join('-');
        result[pathKey] = simpleTypeMap.get(baseType)!;
      }

      const attributes = extension.getElementsByTagName('xs:attribute');
      for (const attr of attributes) {
        const attrName = attr.getAttribute('name');
        const attrType = attr.getAttribute('type');
        if (attrName && attrType && simpleTypeMap.has(attrType)) {
          const pathKey = [...path, attrName].join('-');
          result[pathKey] = simpleTypeMap.get(attrType)!;
        }
      }
    }
    return;
  }

  // Handle sequences
  const sequences = typeNode.getElementsByTagName('xs:sequence');
  for (const seq of sequences) {
    const elements = seq.getElementsByTagName('xs:element');
    for (const el of elements) {
      const name = el.getAttribute('name');
      const elType = el.getAttribute('type');
      if (!name || !elType) continue;

      const newPath = [...path, name];

      if (simpleTypeMap.has(elType)) {
        result[newPath.join('-')] = simpleTypeMap.get(elType)!;
      } else if (complexTypeMap.has(elType)) {
        traverseType(elType, newPath, result, complexTypeMap, simpleTypeMap);
      }
    }
  }
}

/**
 * This function is being used for name mappings of document XSD schemas which contain an extra 'Document' root element.
 * @param nameMappings The name mappings extracted from the XSD schema
 */
export function getRootChildElementFromNameMappings(
  nameMappings: XsdElement
): XsdElement {
  // We expect the base element of the XSD schema to be 'Document'.
  // 'Document' should have exactly one child element which corresponds to the root element of the JSON schema.
  if (nameMappings.fullName !== 'Document') {
    throw new Error(
      `Expected base element of XSD schema to be 'Document' but found '${nameMappings.fullName}'.`
    );
  }
  const xsdSchemaRootChildren = nameMappings.children;
  if (xsdSchemaRootChildren.length !== 1) {
    throw new Error(
      `Expected exactly one child element of 'Document' but found ${xsdSchemaRootChildren.length}.`
    );
  }

  return xsdSchemaRootChildren[0];
}
