import { XsdElement } from '@helaba/iso20022-lib/rules';

export function getAllFieldsFromNameMappings(
  nameMappings: XsdElement
): string[] {
  if (nameMappings.children.length === 0) {
    return [nameMappings.nestedAbbrName];
  }

  const childrenFields: string[] = [];
  for (const child of nameMappings.children) {
    childrenFields.push(...getAllFieldsFromNameMappings(child));
  }

  return childrenFields;
}
