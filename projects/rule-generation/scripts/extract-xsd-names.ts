import { Document, Element } from '@xmldom/xmldom';
import { EXTENSION_SUFFIX } from './types';
import type { XsdElement } from '@helaba/iso20022-lib/rules';

const NAMESPACE_PREFIX = 'xs';

function getChildElements(
  element: Element | Document,
  childName: string
): Element[] {
  return element.childNodes.filter(
    (node) =>
      node.nodeType === 1 &&
      node.nodeName === `${NAMESPACE_PREFIX}:${childName}`
  ) as Element[];
}

function getFullName(element: Element): string | undefined {
  // Try to find a documentation element with source="Name"
  const annotations = getChildElements(element, 'annotation');
  if (annotations.length > 1) {
    throw new Error('Expected at most one annotation.');
  }

  if (annotations.length === 1) {
    const documentations = getChildElements(
      annotations[0],
      'documentation'
    ).filter((doc) => doc.getAttribute('source') === 'Name');
    if (documentations.length > 1) {
      throw new Error("Expected at most one documentation with source='Name'.");
    }
    if (documentations.length === 1) {
      const documentation = documentations[0];
      if (documentation.textContent) {
        return documentation.textContent.trim();
      }
    }
  }

  return undefined;
}

function findComplexType(
  typeId: string,
  allComplexTypes: Element[],
  allSimpleTypes: Element[]
): Element | undefined {
  const complexTypes = allComplexTypes.filter(
    (node) => node.getAttribute('name') === typeId
  );
  const simpleTypes = allSimpleTypes.filter(
    (node) => node.getAttribute('name') === typeId
  );

  // There must be exactly one linked type, either complex or simple
  if (
    !(
      (complexTypes.length === 1 && simpleTypes.length === 0) ||
      (complexTypes.length === 0 && simpleTypes.length === 1)
    )
  ) {
    throw new Error(
      'Expected exactly one linked type (either complexType or simpleType).'
    );
  }

  if (complexTypes.length === 1) {
    return complexTypes[0];
  }

  return undefined;
}

function extractMappings(
  element: Element,
  allComplexTypes: Element[],
  allSimpleTypes: Element[],
  parentNestedAbbrName: string | null
): XsdElement {
  const abbrName = element.getAttribute('name');

  if (!abbrName) {
    throw new Error('Element does not have a name attribute.');
  }

  const typeId = element.getAttribute('type');

  if (!typeId) {
    throw new Error('Element does not have a type attribute.');
  }

  const nestedAbbrName = `${
    parentNestedAbbrName ? `${parentNestedAbbrName}-` : ''
  }${abbrName}`;

  const newMappingsEntry: XsdElement = {
    fullName: getFullName(element) ?? typeId,
    abbrName: abbrName,
    nestedAbbrName,
    isArray:
      element.hasAttribute('maxOccurs') &&
      element.getAttribute('maxOccurs') !== '1',
    children: [],
  };

  const complexType = findComplexType(typeId, allComplexTypes, allSimpleTypes);

  if (complexType) {
    // Retrieve children
    const sequences = getChildElements(complexType, 'sequence');
    const choices = getChildElements(complexType, 'choice');
    const simpleContents = getChildElements(complexType, 'simpleContent');

    // There must be exactly one sequence or choice or simpleContent
    if (
      (sequences.length === 1 &&
        (choices.length !== 0 || simpleContents.length !== 0)) ||
      (choices.length === 1 &&
        (sequences.length !== 0 || simpleContents.length !== 0)) ||
      (simpleContents.length === 1 &&
        (sequences.length !== 0 || choices.length !== 0)) ||
      (sequences.length !== 1 &&
        choices.length !== 1 &&
        simpleContents.length !== 1)
    ) {
      throw new Error(
        `Expected exactly one child element of type sequence or choice or simpleContent. Found ${sequences.length} sequences, ${choices.length} choices, and ${simpleContents.length} simpleContents for element ${newMappingsEntry.fullName}.`
      );
    }

    const isSequence = sequences.length === 1;
    const isChoice = choices.length === 1;
    const isSimpleContent = simpleContents.length === 1;

    const childElementsBlock = isSequence
      ? sequences[0]
      : isChoice
      ? choices[0]
      : getChildElements(simpleContents[0], 'extension')[0];

    if (isSimpleContent) {
      if (!childElementsBlock) {
        throw new Error(
          "Expected an 'extension' child element block for simpleContent but found none."
        );
      }
      const base = childElementsBlock.getAttribute('base');
      if (!base) {
        throw new Error(
          "Expected 'base' attribute in the 'extension' element of simpleContent."
        );
      }
      newMappingsEntry.children.push({
        fullName: EXTENSION_SUFFIX,
        abbrName: EXTENSION_SUFFIX,
        nestedAbbrName: nestedAbbrName + `-${EXTENSION_SUFFIX}`,
        isArray: false,
        children: [],
      });
    }

    const childElements = getChildElements(
      childElementsBlock,
      isSimpleContent ? 'attribute' : 'element'
    );
    for (const element of childElements) {
      newMappingsEntry.children.push(
        extractMappings(
          element,
          allComplexTypes,
          allSimpleTypes,
          nestedAbbrName
        )
      );
    }
  }

  return newMappingsEntry;
}

export function extractXsdElementMappings(xsdSchema: Document): XsdElement {
  const schemaElements = getChildElements(xsdSchema, 'schema');
  if (schemaElements.length !== 1) {
    throw new Error('Expected exactly one schema element in the XSD.');
  }
  const schema = schemaElements[0];

  const elements = getChildElements(schema, 'element');

  if (elements.length !== 1) {
    throw new Error('Expected exactly one root element in the XSD.');
  }

  const rootElement = elements[0];

  // We go one level deeper to "FIToFICstmrCdtTrf" instead of starting from "Document"
  const abbrName = rootElement.getAttribute('name');

  if (!abbrName) {
    throw new Error('Root element does not have a name attribute.');
  }

  const typeId = rootElement.getAttribute('type');

  if (!typeId) {
    throw new Error('Root element does not have a type attribute.');
  }

  const complexTypes = getChildElements(schema, 'complexType');
  const simpleTypes = getChildElements(schema, 'simpleType');

  //if (startRoot) {
  return extractMappings(rootElement, complexTypes, simpleTypes, null);
  //}

  // const complexType = findComplexType(typeId, complexTypes, simpleTypes);

  // // There must be exactly one "sequence" with one sub-element
  // if (!complexType) {
  //   throw new Error(
  //     `Expected exactly one complexType for root element ${abbrName}.`
  //   );
  // }
  // const sequences = getChildElements(complexType, 'sequence');
  // if (sequences.length !== 1) {
  //   throw new Error(
  //     `Expected exactly one sequence for root element ${abbrName}, found ${sequences.length}.`
  //   );
  // }
  // const sequenceElements = getChildElements(sequences[0], 'element');
  // if (sequenceElements.length !== 1) {
  //   throw new Error(
  //     `Expected exactly one sub-element in the sequence of root element ${abbrName}, found ${sequenceElements.length}.`
  //   );
  // }

  // return extractMappings(sequenceElements[0], complexTypes, simpleTypes, null);
}
