import { isObject, isStringArray } from '@helaba/iso20022-lib/util';
import type { XsdElement } from '@helaba/iso20022-lib/rules';
import { getRef, toSnakeCase } from './utils';
import { getRootChildElementFromNameMappings } from './utils/xsd-schema-utils';

function getMergedDefinition(
  definitions: Record<string, unknown>[]
): Record<string, unknown> {
  const { properties, oneOf, required, ...rest } = definitions[0];

  const mergedNestedFieldNames: Set<string> = new Set();
  const mergedProperties: Record<string, Record<string, unknown>> = {};
  const mergedOneOf: Record<string, unknown>[] = [];

  for (const definition of definitions) {
    const nestedFieldNames = definition['nestedFieldNames'];
    const definitionProperties = definition['properties'];
    const definitionOneOf = definition['oneOf'];
    if (isStringArray(nestedFieldNames)) {
      for (const nestedFieldName of nestedFieldNames) {
        mergedNestedFieldNames.add(nestedFieldName);
      }
    }
    if (isObject(definitionProperties)) {
      for (const [propertyKey, propertyValue] of Object.entries(
        definitionProperties
      )) {
        if (!isObject(propertyValue)) {
          throw new Error(`Invalid property format for "${propertyKey}".`);
        }

        const existingProperty = mergedProperties[propertyKey];
        if (existingProperty) {
          // Merge nestedFieldNames
          existingProperty['nestedFieldNames'] = [
            ...(isStringArray(existingProperty['nestedFieldNames'])
              ? existingProperty['nestedFieldNames']
              : []),
            ...(isStringArray(propertyValue['nestedFieldNames'])
              ? propertyValue['nestedFieldNames']
              : []),
          ];
        } else {
          mergedProperties[propertyKey] = {
            ...propertyValue,
          };
        }
      }
    }
    if (Array.isArray(definitionOneOf)) {
      for (const oneOfEntry of definitionOneOf) {
        if (!isObject(oneOfEntry) || !isObject(oneOfEntry['properties'])) {
          throw new Error(`Invalid oneOf entry: ${oneOfEntry}`);
        }
        const propertyKey = Object.keys(oneOfEntry['properties'])[0];
        if (!propertyKey) {
          throw new Error(
            `No property key found in oneOf entry: ${oneOfEntry}`
          );
        }
        if (!isObject(oneOfEntry['properties'][propertyKey])) {
          throw new Error(
            `Invalid property format for "${propertyKey}" in oneOf entry.`
          );
        }

        const existingOneOf = mergedOneOf.find(
          (entry) =>
            isObject(entry['properties']) &&
            Object.keys(entry['properties'])[0] === propertyKey
        );

        if (!existingOneOf) {
          mergedOneOf.push(oneOfEntry);
        } else {
          if (
            !isObject(existingOneOf['properties']) ||
            !isObject(existingOneOf['properties'][propertyKey])
          ) {
            throw new Error(
              `Invalid existing oneOf entry for property "${propertyKey}".`
            );
          }
          let mergedNestedFieldNames =
            existingOneOf['properties'][propertyKey]['nestedFieldNames'];
          if (!isStringArray(mergedNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in existing oneOf entry.`
            );
          }

          const newNestedFieldNames =
            oneOfEntry['properties'][propertyKey]['nestedFieldNames'];
          if (!isStringArray(newNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in new oneOf entry.`
            );
          }

          // Merge nestedFieldNames
          mergedNestedFieldNames = [
            ...mergedNestedFieldNames,
            ...newNestedFieldNames,
          ];

          existingOneOf['properties'][propertyKey]['nestedFieldNames'] =
            mergedNestedFieldNames;
        }
      }
    }
  }

  const mergedDefinition: Record<string, unknown> = {
    ...rest,
    properties:
      Object.keys(mergedProperties).length > 0 ? mergedProperties : undefined,
    oneOf: mergedOneOf.length > 0 ? mergedOneOf : undefined,
    required,
    nestedFieldNames:
      mergedNestedFieldNames.size > 0
        ? Array.from(mergedNestedFieldNames)
        : undefined,
  };

  return mergedDefinition;
}

function getTransformedProperty(
  originalPropertyKey: string,
  originalProperty: unknown,
  nameMappingChild: XsdElement | undefined,
  parentNestedAbbrName: string
): Record<string, unknown> {
  if (!isObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalPropertyKey}".`);
  }

  return {
    ...originalProperty,
    name: nameMappingChild ? nameMappingChild.fullName : originalPropertyKey,
    nestedFieldNames: [
      nameMappingChild
        ? nameMappingChild.nestedAbbrName
        : `${parentNestedAbbrName}-${originalPropertyKey}`,
    ],
  };
}

function getTransformedProperties(
  originalProperties: Record<string, unknown>,
  originalRequiredProperties: unknown,
  nameMappings: XsdElement
): {
  properties: Record<string, Record<string, unknown>>;
  requiredProperties: string[] | undefined;
} {
  if (
    !(typeof originalRequiredProperties === 'undefined') &&
    !Array.isArray(originalRequiredProperties)
  ) {
    throw new Error(
      `Invalid required properties format for "${nameMappings.fullName}". Expected undefined or an array.`
    );
  }

  const transformedProperties: Record<string, Record<string, unknown>> = {};
  const transformedRequiredProperties: string[] = [];

  /**
   * The XSD schema contains 'complexType's with a 'simpleContent' element which contains an 'attribute' that is extended by an 'extension'.
   * This is used to depict elements like '<IntrBkSttlmAmt Ccy="EUR">900.00</IntrBkSttlmAmt>' in the schema ('Ccy' is the attribute, the value is the 'extension').
   * In the JSON schema, this is represented by using a 'properties' object that includes both the attribute and the extension, e.g. "properties": { "currency": { "$ref": "..." }, "amount": { "type": "string", ... } }.
   * There is no 'name' linking the 'extension' to the 'amount' property, so we just make sure that when the 'amount' is inevitably not found in the name mappings, there is at least exactly one '[extension]' entry that we extract in the 'extract-xsd-names' script.
   */
  const noOfExtensions = nameMappings.children.filter(
    (child) => child.fullName === '[extension]'
  ).length;
  let noOfPropertiesWithoutNameMapping = 0;

  for (const [propertyKey, propertyValue] of Object.entries(
    originalProperties
  )) {
    const nameMappingChild = nameMappings.children.find(
      (child) => toSnakeCase(child.fullName) === propertyKey
    );

    if (!nameMappingChild) {
      noOfPropertiesWithoutNameMapping++;
      if (noOfPropertiesWithoutNameMapping > noOfExtensions) {
        throw new Error(
          `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}"`
        );
      }
    }

    const transformedProperty = getTransformedProperty(
      propertyKey,
      propertyValue,
      nameMappingChild,
      nameMappings.nestedAbbrName
    );

    const finalPropertyKey =
      nameMappingChild && nameMappingChild.abbrName
        ? nameMappingChild.abbrName
        : propertyKey;

    transformedProperties[finalPropertyKey] = transformedProperty;
    if (
      originalRequiredProperties &&
      originalRequiredProperties.includes(propertyKey)
    ) {
      transformedRequiredProperties.push(finalPropertyKey);
    }
  }

  return {
    properties: transformedProperties,
    requiredProperties:
      transformedRequiredProperties.length > 0
        ? transformedRequiredProperties
        : undefined,
  };
}

function getTransformedDefinition(
  originalDefinition: Record<string, unknown>,
  properties: unknown,
  oneOf: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  const transformedDefinition: Record<string, unknown> = {
    ...originalDefinition,
  };

  if (!properties && !oneOf) {
    // This means that this definition is not a complex type with properties or oneOf. We still add the 'nestedFieldNames'.
    transformedDefinition['nestedFieldNames'] = [nameMappings.nestedAbbrName];
  }

  if (isObject(properties)) {
    const requiredProperties = originalDefinition['required'];
    const transformedProperties = getTransformedProperties(
      properties,
      requiredProperties,
      nameMappings
    );

    transformedDefinition['properties'] = transformedProperties.properties;
    transformedDefinition['required'] =
      transformedProperties.requiredProperties;
  }

  if (Array.isArray(oneOf)) {
    const transformedOneOf: Record<string, unknown>[] = [];
    for (const oneOfEntry of oneOf) {
      const oneOfProperties = oneOfEntry.properties;
      if (
        !isObject(oneOfProperties) ||
        Object.keys(oneOfProperties).length !== 1
      ) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const oneOfRequiredProperties = oneOfEntry.required;

      const transformedProperties = getTransformedProperties(
        oneOfProperties,
        oneOfRequiredProperties,
        nameMappings
      );

      transformedOneOf.push({
        ...oneOfEntry,
        properties: transformedProperties.properties,
        required: transformedProperties.requiredProperties,
      });
    }
    transformedDefinition['oneOf'] = transformedOneOf;
  }

  return transformedDefinition;
}

function getNestedTransformedDefinitionsForProperty(
  originalDefinitions: Record<string, unknown>,
  propertyKey: string,
  originalProperty: unknown,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  if (!isObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalProperty}".`);
  }

  const propertyRef = getRef(originalProperty);

  if (!propertyRef) {
    return new Map();
  }

  const nameMappingChild = nameMappings.children.find(
    (child) => toSnakeCase(child.fullName) === propertyKey
  );

  if (!nameMappingChild) {
    throw new Error(
      `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}".`
    );
  }

  return getTransformedDefinitions(
    originalDefinitions,
    propertyRef,
    nameMappingChild
  );
}

function getTransformedDefinitions(
  originalDefinitions: Record<string, unknown>,
  ref: string,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  const definition = originalDefinitions[ref];
  if (!isObject(definition)) {
    throw new Error(`Invalid definition for "${ref}".`);
  }

  const properties = definition['properties'];
  const oneOf = definition['oneOf'];

  const transformedDefinitionsMap: Map<
    string,
    Array<Record<string, unknown>>
  > = new Map();

  // Transform the definition itself without going through refs
  const transformedDefinition = getTransformedDefinition(
    definition,
    properties,
    oneOf,
    nameMappings
  );

  transformedDefinitionsMap.set(ref, [transformedDefinition]);

  // Go through the refs in 'properties' or 'oneOf' and transform them recursively

  if (isObject(properties)) {
    for (const [propertyKey, propertyValue] of Object.entries(properties)) {
      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  if (Array.isArray(oneOf)) {
    for (const oneOfEntry of oneOf) {
      const properties = oneOfEntry.properties;
      if (!isObject(properties) || Object.keys(properties).length !== 1) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const [propertyKey, propertyValue] = Object.entries(properties)[0];

      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  return transformedDefinitionsMap;
}

export function transformSingleOneOfToProperties(schema: any): any {

  if (Array.isArray(schema)) {
    return schema.map(transformSingleOneOfToProperties);
  }

  if (typeof schema !== 'object' || schema === null) {
    return schema;
  }

  // Recursively simplify all nested properties
  for (const key of Object.keys(schema)) {
    schema[key] = transformSingleOneOfToProperties(schema[key]);
  }

  // If "oneOf" exists and contains only one element
  if (Array.isArray(schema.oneOf) && schema.oneOf.length === 1) {
    const single = transformSingleOneOfToProperties(schema.oneOf[0]);

    // Remove the "oneOf" key
    delete schema.oneOf;

    // Merge the contents of the single schema into the current object
    for (const [key, value] of Object.entries(single)) {
      // Only copy relevant schema keys
      if (
        key === 'properties' ||
        key === 'required' ||
        key === 'additionalProperties' ||
        key === 'type'
      ) {
        schema[key] = value;
      }
    }
  }

  return schema;
}

export function transformJsonSchema(
  jsonSchema: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  if (
    !isObject(jsonSchema) ||
    !('properties' in jsonSchema) ||
    !('definitions' in jsonSchema)
  ) {
    throw new Error('Invalid JSON schema format.');
  }

  const xsdSchemaRootChild = getRootChildElementFromNameMappings(nameMappings);
  const basePropertyKey = toSnakeCase(xsdSchemaRootChild.fullName);

  const { properties, definitions, ...rest } = jsonSchema;

  if (!isObject(definitions)) {
    throw new Error('Invalid definitions format.');
  }

  if (!isObject(properties) || !(basePropertyKey in properties)) {
    throw new Error("Invalid 'properties' format.");
  }

  const baseProperty = properties[basePropertyKey];

  const transformedBaseProperty = getTransformedProperty(
    basePropertyKey,
    baseProperty,
    xsdSchemaRootChild,
    'Document'
  );

  const transformedProperties: Record<string, unknown> = {
    ...properties,
    [xsdSchemaRootChild.abbrName]: transformedBaseProperty,
  };

  delete transformedProperties[basePropertyKey];

  const basePropertyRef = getRef(transformedBaseProperty);
  if (!basePropertyRef) {
    throw new Error(
      `Invalid base property reference for "${basePropertyKey}".`
    );
  }

  const transformedDefinitions: Map<
    string,
    Array<Record<string, unknown>>
  > = getTransformedDefinitions(
    definitions,
    basePropertyRef,
    xsdSchemaRootChild
  );

  // The resulting transformedDefinitions contains multiple entries for the same ref if a definition is used in more than one branch.
  // We need to merge them into a single definition per ref combining the different 'nestedFieldName's.
  const mergedDefinitions: Record<string, Record<string, unknown>> = {};
  for (const [ref, entries] of transformedDefinitions) {
    const mergedDefinition: Record<string, unknown> =
      getMergedDefinition(entries);
    mergedDefinitions[ref] = mergedDefinition;
  }

  const transformedSchema: Record<string, unknown> = {
    ...rest,
    properties: transformedProperties,
    required: [xsdSchemaRootChild.abbrName],
    definitions: mergedDefinitions,
  };

  return transformedSchema;
}