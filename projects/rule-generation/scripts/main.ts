#!/usr/bin/env node

import * as fs from 'fs';
import { Document, DOMParser } from '@xmldom/xmldom';
import minimist from 'minimist';
import { validateRules } from './validate-rules';
import { extractXsdElementMappings } from './extract-xsd-names';
import { writeFileSafe, writeFile } from './utils';
import { transformJsonSchema, transformSingleOneOfToProperties } from './transform-json-schema';
import { generateRules } from './generate-rules';
import { transformRules } from './transform-rules';
import { combineRules } from './combine-rules';
import {
  expandFormSetup,
  verifyContainsAllFieldsFromSchema,
} from './expand-form-setup';
import { replaceExtensionNames } from './replace-extensions';
import {
  computeAffectedFieldsForFieldValueChange,
  mapToObject,
  XsdElement,
} from '@helaba/iso20022-lib/rules';
import { getAllFieldsFromNameMappings } from './utils';

/**
 * Parse command line arguments
 */
function parseArgs(): {
  documentJsonSchemaFile: string;
  documentXsdSchemaFile: string;
  bahXsdSchemaFile: string;
  customRules: string;
  formSetupFile: string;
  outputFolder: string;
  intermediateResults: string;
} {
  const argv = minimist(process.argv.slice(2), {
    string: ['output-folder', 'base-element', 'intermediate-results'],
    default: {
      'output-folder': '',
      'intermediate-results': 'projects/rule-generation/intermediary-results/'
    },
  });

  if (argv._.length < 2) {
    console.error(
      'Usage: node transform-json-schema.js <json_schema> <xsd_schema> <bah-xsd_schema> <custom_rules> <form_setup> [--output-folder /output] [--intermediate-results /tmp] [--base-element element_name]'
    );
    process.exit(1);
  }

  return {
    documentJsonSchemaFile: argv._[0],
    documentXsdSchemaFile: argv._[1],
    bahXsdSchemaFile: argv._[2],
    customRules: argv._[3],
    formSetupFile: argv._[4],
    outputFolder: argv['output-folder'],
    intermediateResults: argv['intermediate-results'],
  };
}

async function main() {
  try {
    const args = parseArgs();

    const rulesModule = await import(args.customRules);
    const customDocumentRules = rulesModule.customDocumentRules;
    const customBAHRules = rulesModule.customBAHRules;

    const INTERMEDIARY_RESULTS_FOLDER = args.intermediateResults

    // Read input files
    const documentJsonSchemaContent = fs.readFileSync(
      args.documentJsonSchemaFile,
      'utf8'
    );
    const documentXsdSchemaContent = fs.readFileSync(
      args.documentXsdSchemaFile,
      'utf8'
    );
    const bahXsdSchemaContent = fs.readFileSync(args.bahXsdSchemaFile, 'utf8');
    const formSetupContent = fs.readFileSync(args.formSetupFile, 'utf8');

    // Parse input files
    const documentJsonSchema = JSON.parse(documentJsonSchemaContent);
    const formSetup = JSON.parse(formSetupContent);
    const parser = new DOMParser();
    const documentXsdSchema: Document = parser.parseFromString(
      documentXsdSchemaContent,
      'application/xml'
    );
    const bahXsdSchema: Document = parser.parseFromString(
      bahXsdSchemaContent,
      'application/xml'
    );

    // Document
    const documentNameMappings: XsdElement =
      extractXsdElementMappings(documentXsdSchema);
    writeFileSafe(
      documentNameMappings,
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`
    );
    // BaH
    const bahNameMappings: XsdElement = extractXsdElementMappings(bahXsdSchema);
    writeFileSafe(
      bahNameMappings,
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings-bah.json`
    );

    // Transform JSON schema
    const documentJsonSchemaNoSingleOneOf = transformSingleOneOfToProperties(
      documentJsonSchema
    );

    const transformedDocumentJsonSchema = transformJsonSchema(
      documentJsonSchemaNoSingleOneOf,
      documentNameMappings
    );
    writeFileSafe(
      transformedDocumentJsonSchema,
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-schema.json`
    );

    const updatedDocumentNameMappings: XsdElement = replaceExtensionNames(
      documentNameMappings,
      JSON.parse(JSON.stringify(transformedDocumentJsonSchema, null, 2)) // Without stringify/parse, some inner elements are missing, not sure what's going on here.
    );
    writeFileSafe(
      updatedDocumentNameMappings,
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`
    );
    writeFileSafe(
      updatedDocumentNameMappings,
      `${args.outputFolder}/name-mappings.json`
    );

    // Extract a list of all fields from the JSON schema
    const allDocumentFields = getAllFieldsFromNameMappings(
      updatedDocumentNameMappings
    );
    // Sort alphabetically
    allDocumentFields.sort((a: string, b: string) => a.localeCompare(b));

    const allBahFields = getAllFieldsFromNameMappings(bahNameMappings);
    allBahFields.sort((a: string, b: string) => a.localeCompare(b));

    writeFileSafe(
      { document: allDocumentFields, bah: allBahFields },
      `${INTERMEDIARY_RESULTS_FOLDER}/all-fields.json`
    );

    // Expand the 'form-setup' to contain all nested fields
    const expandedFormSetup = expandFormSetup(
      formSetup,
      updatedDocumentNameMappings,
      bahNameMappings,
      allDocumentFields,
      allBahFields
    );
    writeFileSafe(
      expandedFormSetup,
      `${args.outputFolder}/expanded-form-setup.json`
    );

    // Verify that the form setup contains all fields from the document JSON schema and BAH XSD schema.
    // Also verify that the form setup does not contain any fields that are not in the document JSON schema or BAH XSD schema.
    verifyContainsAllFieldsFromSchema(
      expandedFormSetup,
      allDocumentFields,
      allBahFields
    );

    // Generate rules from transformed JSON schema
    // Document
    const generatedDocumentRules = generateRules(
      transformedDocumentJsonSchema,
      updatedDocumentNameMappings
    );
    writeFileSafe(
      generatedDocumentRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/generated-rules.json`
    );
    // BaH
    const generatedBAHRules = generateRules(bahXsdSchema, bahNameMappings);
    writeFileSafe(
      generatedBAHRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/generated-rules-bah.json`
    );

    // Transform custom rules
    // Document
    const transformedCustomDocumentRules = transformRules(
      customDocumentRules,
      transformedDocumentJsonSchema,
      updatedDocumentNameMappings
    );
    writeFileSafe(
      transformedCustomDocumentRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-custom-rules.json`
    );
    // BaH
    // TODO: This will fail when adding actual custom rules for BaH as the 'transformedJsonSchema' does not match the BaH schema.
    const transformedCustomBAHRules = transformRules(
      customBAHRules,
      transformedDocumentJsonSchema,
      bahNameMappings
    );
    writeFileSafe(
      transformedCustomBAHRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-custom-rules-bah.json`
    );

    // Combine generated rules with custom rules
    // Document
    const combinedDocumentRules = combineRules(
      generatedDocumentRules,
      transformedCustomDocumentRules
    );
    validateRules(combinedDocumentRules);
    writeFileSafe(
      combinedDocumentRules,
      `${args.outputFolder}/combined-rules.json`
    );
    // BaH
    const combinedBAHRules = combineRules(
      generatedBAHRules,
      transformedCustomBAHRules
    );
    validateRules(combinedBAHRules);
    writeFileSafe(combinedBAHRules, `${args.outputFolder}/combined-rules-bah.json`);

    // Document
    const documentAffectedFieldsForFieldValueChange =
      computeAffectedFieldsForFieldValueChange(
        combinedDocumentRules,
        updatedDocumentNameMappings
      );
    const documentAffectedFields = mapToObject(
      documentAffectedFieldsForFieldValueChange
    );
    writeFileSafe(
      documentAffectedFields,
      `${args.outputFolder}/affected-fields-for-field-value-change.json`
    );
    // BaH
    const bahAffectedFieldsForFieldValueChange =
      computeAffectedFieldsForFieldValueChange(
        combinedBAHRules,
        bahNameMappings
      );
    const bahAffectedFields = mapToObject(bahAffectedFieldsForFieldValueChange);
    writeFileSafe(
      bahAffectedFields,
      `${args.outputFolder}/affected-fields-for-field-value-change-bah.json`
    );
  } catch (error: unknown) {
    console.error(error);
    process.exit(1);
  }
}

main();