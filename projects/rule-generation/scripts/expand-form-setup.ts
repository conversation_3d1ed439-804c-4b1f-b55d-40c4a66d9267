import { findScopes, isStringArray } from '@helaba/iso20022-lib/util';
import { FormSetup, formSetupSchema } from './types';
import type { XsdElement, ExpandedFormSetup } from '@helaba/iso20022-lib/rules';
import { getRootChildElementFromNameMappings } from './utils/xsd-schema-utils';

function flattenNameMappings(nameMappings: XsdElement): XsdElement[] {
  if (nameMappings.children.length === 0) {
    return [nameMappings];
  }

  const flattened: XsdElement[] = [];
  for (const child of nameMappings.children) {
    const childMappings = flattenNameMappings(child);
    flattened.push(...childMappings);
  }

  return flattened;
}

function expandField(
  field: string,
  definitions: Record<string, string[]>,
  baseName: string
): string[] {
  if (!field.endsWith(']')) {
    return [baseName + '-' + field];
  }

  const fieldParts = field.split('[');
  const fieldName = fieldParts[0];
  const definitionReference = fieldParts[1].slice(0, -1); // Remove the closing bracket

  const definition = definitions[definitionReference];
  if (!definition || !isStringArray(definition)) {
    throw new Error(
      `Invalid definition reference '${definitionReference}' for field '${field}'`
    );
  }

  if (definition.length === 0) {
    return [baseName + '-' + fieldName];
  }

  const expandedFields: string[] = [];
  for (const def of definition) {
    expandedFields.push(
      ...expandField(`${fieldName}-${def}`, definitions, baseName)
    );
  }

  return expandedFields;
}

function addNames(
  fields: string[],
  flattenedNameMappings: XsdElement[]
): Record<string, string> {
  const result: Record<string, string> = {};
  for (const field of fields) {
    const nameMapping = flattenedNameMappings.find(
      (mapping) => mapping.nestedAbbrName === field
    );
    if (!nameMapping) {
      throw new Error(`No name mapping found for field '${field}'`);
    }
    result[field] = nameMapping.fullName;
  }
  return result;
}

export function expandFormSetup(
  formSetupContent: unknown,
  documentNameMappings: XsdElement,
  bahNameMappings: XsdElement,
  allDocumentFields: string[],
  allBahFields: string[]
): ExpandedFormSetup {
  let formSetup: FormSetup;
  try {
    formSetup = formSetupSchema.parse(formSetupContent);
  } catch (e) {
    throw e;
  }

  const pages = formSetup.pages;
  const definitions = formSetup.definitions;

  const documentDefinitions = definitions.document;
  const bahDefinitions = definitions.bah;

  // The form-setup.json allows for leaving out the 'Document-FIToFICstmrCdtTrf' prefix for the document and the 'AppHdr' prefix for the business application header so we need to add that here.
  const documentRootElement =
    getRootChildElementFromNameMappings(documentNameMappings);
  const documentBaseName = documentRootElement.nestedAbbrName;
  const flattenedDocumentNameMappings: XsdElement[] =
    flattenNameMappings(documentNameMappings);

  const bahBaseName = bahNameMappings.nestedAbbrName;
  const flattenedBahNameMappings: XsdElement[] =
    flattenNameMappings(bahNameMappings);

  const result: ExpandedFormSetup = {};

  const expandFields = (
    fields: string[],
    definitions: Record<string, string[]>,
    fieldPrefix: string,
    flattenedNameMappings: XsdElement[]
  ): Record<string, string> => {
    let expandedFields: Record<string, string> = {};
    for (const field of fields) {
      const expandedField: string[] = expandField(
        field,
        definitions,
        fieldPrefix
      );
      const expandedFieldWithNames: Record<string, string> = addNames(
        expandedField,
        flattenedNameMappings
      );
      expandedFields = {
        ...expandedFields,
        ...expandedFieldWithNames,
      };
    }

    return expandedFields;
  };

  for (const [pageKey, pageContent] of Object.entries(pages)) {
    const documentPageFields = pageContent.document;
    const bahPageFields = pageContent.bah;
    const documentBasicPageFields = documentPageFields.basic;
    const documentAdvancedPageFields = documentPageFields.advanced;
    const bahBasicPageFields = bahPageFields.basic;
    const bahAdvancedPageFields = bahPageFields.advanced;

    const expandedBasicDocumentFields = expandFields(
      documentBasicPageFields,
      documentDefinitions,
      documentBaseName,
      flattenedDocumentNameMappings
    );
    const expandedAdvancedDocumentFields = expandFields(
      documentAdvancedPageFields,
      documentDefinitions,
      documentBaseName,
      flattenedDocumentNameMappings
    );
    const expandedBasicBahFields = expandFields(
      bahBasicPageFields,
      bahDefinitions,
      bahBaseName,
      flattenedBahNameMappings
    );
    const expandedAdvancedBahFields = expandFields(
      bahAdvancedPageFields,
      bahDefinitions,
      bahBaseName,
      flattenedBahNameMappings
    );

    result[pageKey] = {
      document: {
        basic: expandedBasicDocumentFields,
        advanced: expandedAdvancedDocumentFields,
        advancedScopes: findScopes(
          Object.keys(expandedAdvancedDocumentFields),
          allDocumentFields
        ),
      },
      bah: {
        basic: expandedBasicBahFields,
        advanced: expandedAdvancedBahFields,
        advancedScopes: findScopes(
          Object.keys(expandedAdvancedBahFields),
          allBahFields
        ),
      },
    };
  }

  // Make sure there are no duplicate fields in the result
  verifiyNoDuplicateFields(result);

  return result;
}

function verifiyNoDuplicateFields(fields: ExpandedFormSetup): void {
  const allFields: Set<string> = new Set();

  for (const pageKey in fields) {
    const page = fields[pageKey];
    const allPageDocumentBasicFields = Object.keys(page.document.basic);
    const allPageDocumentAdvancedFields = Object.keys(page.document.advanced);
    const allPageBahBasicFields = Object.keys(page.bah.basic);
    const allPageBahAdvancedFields = Object.keys(page.bah.advanced);

    for (const field of allPageDocumentBasicFields) {
      if (allFields.has(field)) {
        throw new Error(
          `Duplicate field found: '${field}' in page '${pageKey}.document.basic'`
        );
      }
      allFields.add(field);
    }
    for (const field of allPageDocumentAdvancedFields) {
      if (allFields.has(field)) {
        throw new Error(
          `Duplicate field found: '${field}' in page '${pageKey}.document.advanced'`
        );
      }
      allFields.add(field);
    }

    for (const field of allPageBahBasicFields) {
      if (allFields.has(field)) {
        throw new Error(
          `Duplicate field found: '${field}' in page '${pageKey}.bah.basic'`
        );
      }
      allFields.add(field);
    }
    for (const field of allPageBahAdvancedFields) {
      if (allFields.has(field)) {
        throw new Error(
          `Duplicate field found: '${field}' in page '${pageKey}.bah.advanced'`
        );
      }
      allFields.add(field);
    }
  }
}

export function verifyContainsAllFieldsFromSchema(
  formSetupFields: ExpandedFormSetup,
  allDocumentFields: string[],
  allBahFields: string[]
): void {
  const allFormSetupDocumentFields = Object.values(formSetupFields)
    .map((page) => ({
      ...page.document.basic,
      ...page.document.advanced,
    }))
    .flatMap((page) => Object.keys(page));

  for (const field of allDocumentFields) {
    if (!allFormSetupDocumentFields.includes(field)) {
      throw new Error(
        `Field '${field}' from document schema is not present in the expanded document form setup`
      );
    }
  }

  console.info(
    'All fields from the document schema are present in the expanded document form setup'
  );

  for (const field of allFormSetupDocumentFields) {
    if (!allDocumentFields.includes(field)) {
      throw new Error(
        `Field '${field}' from document form setup is not present in the document schema fields`
      );
    }
  }

  console.info(
    'All document fields from the expanded form setup are present in the document schema fields'
  );

  const allFormSetupBahFields = Object.values(formSetupFields)
    .map((page) => ({
      ...page.bah.basic,
      ...page.bah.advanced,
    }))
    .flatMap((page) => Object.keys(page));

  for (const field of allBahFields) {
    if (!allFormSetupBahFields.includes(field)) {
      throw new Error(
        `Field '${field}' from BAH schema is not present in the expanded BAH form setup`
      );
    }
  }

  console.info(
    'All fields from the BAH schema are present in the expanded BAH form setup'
  );

  for (const field of allFormSetupBahFields) {
    if (!allBahFields.includes(field)) {
      throw new Error(
        `Field '${field}' from BAH form setup is not present in the BAH schema fields`
      );
    }
  }

  console.info(
    'All BAH fields from the expanded form setup are present in the BAH schema fields'
  );
}
