import type { Rule } from '@helaba/iso20022-lib/rules';

/**
 * - Make sure all rules have unique ids.
 * - Make sure there are no colons in the rule ids as these are used separating keys in Angular i18n.
 * - Make sure there are no conditional rules with empty 'condition' or 'rules' arrays.
 * @param rules
 */
export function validateRules(rules: Rule[]): void {
  const ids = new Set<string>();
  for (const rule of rules) {
    if (ids.has(rule.id)) {
      throw new Error(`Duplicate rule id found: ${rule.id}`);
    }
    ids.add(rule.id);

    if (rule.type === 'condition') {
      if (rule.conditions.length === 0) {
        throw new Error(
          `Conditional rule with id "${rule.id}" has no conditions defined.`
        );
      }
      if (rule.rules.length === 0) {
        throw new Error(
          `Conditional rule with id "${rule.id}" has no rules defined.`
        );
      }

      for (const conditionalRule of rule.rules) {
        if (ids.has(conditionalRule.id)) {
          throw new Error(
            `Duplicate rule id found in conditional rules: ${conditionalRule.id}`
          );
        }
        ids.add(conditionalRule.id);

        if (conditionalRule.id.includes(':')) {
          throw new Error(
            `Conditional rule id contains a colon which is not allowed: ${conditionalRule.id}`
          );
        }
      }
    }

    if (rule.id.includes(':')) {
      throw new Error(
        `Rule id contains a colon which is not allowed: ${rule.id}`
      );
    }
  }
}
