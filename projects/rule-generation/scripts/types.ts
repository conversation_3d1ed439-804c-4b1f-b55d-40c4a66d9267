import { z } from 'zod';

export const EXTENSION_SUFFIX = '[extension]';

export const formSetupSchema = z.object({
  description: z.string(),
  pages: z.record(
    z.string(),
    z.object({
      document: z.object({
        basic: z.array(z.string()),
        advanced: z.array(z.string()),
      }),
      bah: z.object({
        basic: z.array(z.string()),
        advanced: z.array(z.string()),
      }),
    })
  ),
  definitions: z.object({
    document: z.record(z.string(), z.array(z.string())),
    bah: z.record(z.string(), z.array(z.string())),
  }),
});

export type FormSetup = z.infer<typeof formSetupSchema>;
