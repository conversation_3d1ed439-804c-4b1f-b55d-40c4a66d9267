<?xml version="1.0" encoding="UTF-8"?>
<!--- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Legal Notices

SWIFT SCRL@2016. All rights reserved.

This schema is a component of MyStandards, the SWIFT collaborative Web application used to manage
standards definitions and industry usage.

This is a licensed product, which may only be used and distributed in accordance with MyStandards License
Terms as specified in MyStandards Service Description and the related Terms of Use.

Unless otherwise agreed in writing with SWIFT SCRL, the user has no right to:
 - authorise external end users to use this component for other purposes than their internal use.
 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.
 - re-sell or authorise another party e.g. software and service providers, to re-sell this component.

This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties
with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual 
property rights of whatever nature in this component will remain the exclusive property of SWIFT or its 
licensors.

Trademarks
SWIFT is the trade name of S.W.I.F.T. SCRL.
The following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.
Other product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

Group: Cross Border Payments and Reporting Plus (CBPR+)
Collection: CBPRPlus SR2025 (Combined)
Usage Guideline: CBPRPlus-pacs.004.001.09_PaymentReturn
Base Message: pacs.004.001.09
Date of publication: 13 December 2024
URL: https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0y-cJpiEe6MIJTGjiktfA
Description: Principles:

1A. AGENTS IDENTIFICATION - Textual Rules:

-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.

-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.
Exception: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.

Note: "Instructing/ Instructed Agents" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.

1B. DEBTOR/CREDITOR - PARTY IDENTIFICATION - Textual Rules:

-> If AnyBIC is present, then (Name and Postal Address) is NOT allowed (other elements remain optional) - However, in case of conflicting information, AnyBIC will always take precedence.

-> If Name is present, it is recommended to use Postal Address.


2. The pacs.004 is also used for a return of pacs.009 COV following the serial flow. Single transactions only are allowed.


3. Character Set:

All proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.

All Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:

  !#$&%*=^_’{|}~";<>@[\]

< is replaced with &lt;
> is replaced with &gt;


4. CBPR_Agent_PointToPointOnSWIFT:

If the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI

Generated by the MyStandards web platform [https://www.swift.com/mystandards] on 2025-02-28T20:13:29Z
-->
<!---->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.02" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:head.001.001.02">
    <xs:element name="AppHdr" type="BusinessApplicationHeaderV02"/>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BICFIDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchAndFinancialInstitutionIdentification6__2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution or a branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BusinessApplicationHeader5__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BusinessApplicationHeader5__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the Business Application Header of the Business Message.&#13;
Can be used when replying to a query; can also be used when canceling or amending.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="CharSet" type="UnicodeChartsCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CharacterSet</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Contains the character set of the text-based elements used in the Business Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Fr" type="Party44Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">From</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">The sending MessagingEndpoint that has created this Business Message for the receiving MessagingEndpoint that will process this Business Message.&#13;
&#13;
Note	the sending MessagingEndpoint might be different from the sending address potentially contained in the transport header (as defined in the transport layer).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="To" type="Party44Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">To</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">The MessagingEndpoint designated by the sending MessagingEndpoint to be the recipient who will ultimately process this Business Message.&#13;
&#13;
Note the receiving MessagingEndpoint might be different from the receiving address potentially contained in the transport header (as defined in the transport layer).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="BizMsgIdr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BusinessMessageIdentifier</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguously identifies the Business Message to the MessagingEndpoint that has created the Business Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MsgDefIdr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageDefinitionIdentifier</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Contains the MessageIdentifier that defines the BusinessMessage.&#13;
It must contain a MessageIdentifier published on the ISO 20022 website.&#13;
&#13;
example	camt.001.001.03.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BizSvc" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BusinessService</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the business service agreed between the two MessagingEndpoints under which rules this Business Message is exchanged.&#13;
 To be used when there is a choice of processing services or processing service levels.&#13;
Example: E&amp;I.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CreDt" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time when this Business Message (header) was created.&#13;
Note Times must be normalized, using the "Z" annotation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CpyDplct" type="CopyDuplicate1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CopyDuplicate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicates whether the message is a Copy, a Duplicate or a copy of a duplicate of a previously sent ISO 20022 Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Prty" type="BusinessMessagePriorityCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Priority</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Relative indication of the processing precedence of the message over a (set of) Business Messages with assigned priorities.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BusinessApplicationHeaderV02">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BusinessApplicationHeaderV02</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">The Business Layer deals with Business Messages. The behaviour of the Business Messages is fully described by the Business Transaction and the structure of the Business Messages is fully described by the Message Definitions and related Message Rules, Rules and Market Practices. All of which are registered in the ISO 20022 Repository.&#13;
A single new Business Message (with its accompagnying business application header) is created - by the sending MessagingEndpoint - for each business event; that is each interaction in a Business Transaction. A Business Message adheres to the following principles:&#13;
" A Business Message (and its business application header) must not contain information about the Message Transport System or the mechanics or mechanism of message sending, transportation, or receipt. &#13;
" A Business Message must be comprehensible outside of the context of the Transport Message. That is the Business Message must not require knowledge of the Transport Message to be understood.&#13;
" A Business Message may contain headers, footers, and envelopes that are meaningful for the business. When present, they are treated as any other message content, which means that they are considered part of the Message Definition of the Business Message and as such will be part of the ISO 20022 Repository.&#13;
" A Business Message refers to Business Actors by their Name. Each instance of a Business Actor has one Name. The Business Actor must not be referred to in the Transport Layer.&#13;
Specific usage of this BusinessMessageHeader may be defined by the relevant SEG.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="CharSet" type="UnicodeChartsCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CharacterSet</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Contains the character set of the text-based elements used in the Business Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Fr" type="Party44Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">From</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">The sending MessagingEndpoint that has created this Business Message for the receiving MessagingEndpoint that will process this Business Message.&#13;
&#13;
Note	the sending MessagingEndpoint might be different from the sending address potentially contained in the transport header (as defined in the transport layer).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="To" type="Party44Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">To</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">The MessagingEndpoint designated by the sending MessagingEndpoint to be the recipient who will ultimately process this Business Message.&#13;
&#13;
Note the receiving MessagingEndpoint might be different from the receiving address potentially contained in the transport header (as defined in the transport layer).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="BizMsgIdr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BusinessMessageIdentifier</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguously identifies the Business Message to the MessagingEndpoint that has created the Business Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MsgDefIdr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageDefinitionIdentifier</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Contains the MessageIdentifier that defines the BusinessMessage.&#13;
It must contain a MessageIdentifier published on the ISO 20022 website.&#13;
&#13;
example	camt.001.001.03.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="BizSvc" type="UsageIdentifierPatternText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BusinessService</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the business service agreed between the two MessagingEndpoints under which rules this Business Message is exchanged.&#13;
 To be used when there is a choice of processing services or processing service levels.&#13;
Example: E&amp;I.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MktPrctc" type="ImplementationSpecification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MarketPractice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the market practice to which the message conforms. The market practices are a set of rules agreed between parties that restricts the usage of the messages in order to achieve better STP (Straight Through Processing) rates.&#13;
A market practice specification may also extend the underlying message specification by using extensions or supplementary data of this underlying message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CreDt" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time when this Business Message (header) was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CpyDplct" type="CopyDuplicate1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CopyDuplicate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicates whether the message is a Copy, a Duplicate or a copy of a duplicate of a previously sent ISO 20022 Message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PssblDplct" type="YesNoIndicator">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PossibleDuplicate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Flag indicating if the Business Message exchanged between the MessagingEndpoints is possibly a duplicate. &#13;
If the receiving MessagingEndpoint did not receive the original, then this Business Message should be processed as if it were the original. &#13;
&#13;
If the receiving MessagingEndpoint did receive the original, then it should perform necessary actions to avoid processing this Business Message again.&#13;
&#13;
This will guarantee business idempotent behaviour.&#13;
&#13;
NOTE: this is named "PossResend" in FIX - this is an application level resend not a network level retransmission.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Prty" type="Priority2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Priority</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Relative indication of the processing precedence of the message over a (set of) Business Messages with assigned priorities.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rltd" type="BusinessApplicationHeader5__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Related</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the Business Application Header(s) of the Business Message(s) to which this Business Message relates.&#13;
Can be used when replying to a query; can also be used when canceling or amending.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="BusinessMessagePriorityCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BusinessMessagePriorityCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the priority levels for the BusinessMessage.&#13;
The different priorities are typically user / service / implementation dependent. The semantics of the different values for a Mesage (Set) need to be defined by the relevant user community (SEG.).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="CBPR_DateTime">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_DateTime</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). &#13;
&#13;
This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.&#13;
Note on the time format:&#13;
1) beginning / end of calendar day&#13;
00:00:00 = the beginning of a calendar day&#13;
24:00:00 = the end of a calendar day&#13;
2) fractions of second in time format&#13;
Decimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.&#13;
&#13;
Example: 2020-07-16T19:20:30.45+01:00</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:dateTime">
            <xs:pattern value=".*(\+|-)((0[0-9])|(1[0-4])):[0-5][0-9]"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax28Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax28Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="28"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of a clearing system identifier.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a clearing system, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ClrSysId" type="ClearingSystemIdentification2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MmbId" type="CBPR_RestrictedFINXMax28Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a member of a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CopyDuplicate1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CopyDuplicate1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies if this document is a copy, a duplicate, or a duplicate of a copy.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CODU">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CopyDuplicate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Message is being sent as a copy to a party other than the account owner, for information purposes and the message is a duplicate of a message previously sent.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="COPY">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Copy</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Message is being sent as a copy to a party other than the account owner, for information purposes.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DUPL">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Duplicate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Message is for information/confirmation purposes. It is a duplicate of a message previously sent.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalClearingSystemIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing system identification code, as published in an external clearing system identification code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FinancialInstitutionIdentification18__2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification18__2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details to identify a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="BICFI" type="BICFIDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BICFI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information used to identify a member within a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identifier of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ImplementationSpecification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ImplementationSpecification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Identifies the implementation specification to which the ISO 20022 message conforms.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Regy" type="Max350Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Registry</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the implementation specification registry in which the implementation specification of the ISO 20022 message is maintained.&#13;
For example, "MyStandards".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Id" type="Max2048Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifier which unambiguously identifies, within the implementation specification registry, the implementation specification to which the ISO 20022 message is compliant. This can be done via a URN. It can also contain a version number or date.&#13;
For instance, "2018-01-01 – Version 2" or "urn:uuid:6e8bc430-9c3a-11d9-9669-0800200c9a66".</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="LEIIdentifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LEIIdentifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Legal Entity Identifier is a code allocated to a party as described in ISO 17442 "Financial Services - Legal Entity Identifier (LEI)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max2048Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 2048 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max350Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max350Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 350 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="350"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max35Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 35 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Party44Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Party44Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Identification of a person, an organisation or a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="FIId" type="BranchAndFinancialInstitutionIdentification6__2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="Priority2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Priority2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the priority level of an event.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGH">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">High</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is high.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NORM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Normal</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is normal.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="UnicodeChartsCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">UnicodeChartsCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">codelist containing the names of the UNICODE code block name as specified on http://unicode.org/Public/UNIDATA/Blocks.txt.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="UsageIdentifierPatternText">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">UsageIdentifierPatternText</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">35 Character Text restricted to the Usage Identifier Pattern. That is, it must be a lower case string of maximum 35 characters containing the following elements separated by period&#13;
&#13;
1. Short issuer organisation (Mandatory)&#13;
2. Business context (Mandatory and Repetitive)&#13;
3. Version (Mandatory)&#13;
&#13;
Each element must be an alphanumeric string of maximum 10 characters except the Version that must be exactly 2 digits.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-z0-9]{1,10}\.([a-z0-9]{1,10}\.)+\d\d"/>
            <xs:minLength value="6"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoIndicator">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">YesNoIndicator</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Indicates a "Yes" or "No" type of answer for an element.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
</xs:schema>