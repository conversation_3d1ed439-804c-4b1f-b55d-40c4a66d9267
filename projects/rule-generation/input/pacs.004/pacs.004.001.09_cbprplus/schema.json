{"$comment": {"legalNotices": "SWIFT SCRL@2023. All rights reserved.\n\nThis schema is a component of MyStandards, the SWIFT collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with SWIFT SCRL, the user has no right to:\n - authorise external end users to use this component for other purposes than their internal use.\n - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n - re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual \nproperty rights of whatever nature in this component will remain the exclusive property of SWIFT or its \nlicensors.\n\nTrademarks\nSWIFT is the trade name of S.W.I.F.T. SCRL.\nThe following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.004.001.09_PaymentReturn", "baseMessage": "pacs.004.001.09", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0y-cJpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1A. AGENTS IDENTIFICATION - Textual Rules:\r\n\r\n-> If BIC<PERSON> is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n1B. DEBTOR/CREDITOR - PARTY IDENTIFICATION - Textual Rules:\r\n\r\n-> If AnyBIC is present, then (Name and Postal Address) is NOT allowed (other elements remain optional) - However, in case of conflicting information, AnyBIC will always take precedence.\r\n\r\n-> If Name is present, it is recommended to use Postal Address.\r\n\r\n\r\n2. The pacs.004 is also used for a return of pacs.009 COV following the serial flow. Single transactions only are allowed.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema PaymentReturnV09 (pacs.004.001.09) Generated by SWIFT MyStandards 2025-04-23 08:11:38", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.004.001.09"}, "payment_return_v09": {"$ref": "#/definitions/PaymentReturnV09"}}, "definitions": {"AccountIdentification4Choice": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"iban": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier"}}, "required": ["iban"]}, {"type": "object", "additionalProperties": false, "properties": {"other": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1"}}, "required": ["other"]}]}, "AccountIdentification4Choice__1": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"iban": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier"}}, "required": ["iban"]}, {"type": "object", "additionalProperties": false, "properties": {"other": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1__1"}}, "required": ["other"]}]}, "AccountSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "AccountSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ActiveCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\"."}, "ActiveOrHistoricCurrencyAndAmount": {"type": "object", "description": "A number of monetary units specified in an active or a historic currency where the unit of currency is explicit and compliant with ISO 4217.", "additionalProperties": false, "properties": {"currency": {"$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "amount": {"type": "string", "maxLength": 19, "pattern": "^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$"}}, "required": ["currency", "amount"]}, "ActiveOrHistoricCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\"."}, "AmendmentInformationDetails13__1": {"type": "object", "description": "Provides further details on the list of direct debit mandate elements that have been modified when the amendment indicator has been set.", "additionalProperties": false, "properties": {"original_mandate_identification": {"description": "Unique identification, as assigned by the creditor, to unambiguously identify the original mandate.", "$ref": "#/definitions/Max35Text"}, "original_creditor_scheme_identification": {"description": "Original creditor scheme identification that has been modified.", "$ref": "#/definitions/PartyIdentification135__4"}, "original_creditor_agent": {"description": "Original creditor agent that has been modified.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__4"}, "original_creditor_agent_account": {"description": "Original creditor agent account that has been modified.", "$ref": "#/definitions/CashAccount38"}, "original_debtor": {"description": "Original debtor that has been modified.", "$ref": "#/definitions/PartyIdentification135__4"}, "original_debtor_account": {"description": "Original debtor account that has been modified.", "$ref": "#/definitions/CashAccount38"}, "original_debtor_agent": {"description": "Original debtor agent that has been modified.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__5"}, "original_debtor_agent_account": {"description": "Original debtor agent account that has been modified.", "$ref": "#/definitions/CashAccount38"}, "original_final_collection_date": {"description": "Original final collection date that has been modified.", "$ref": "#/definitions/ISODate"}, "original_frequency": {"description": "Original frequency that has been modified.", "$ref": "#/definitions/Frequency36Choice"}, "original_reason": {"description": "Original reason for the mandate to allow the user to distinguish between different mandates for the same creditor.", "$ref": "#/definitions/MandateSetupReason1Choice"}, "original_tracking_days": {"description": "Original number of tracking days that has been modified.", "$ref": "#/definitions/Exact2NumericText"}}}, "AmountType4Choice__1": {"type": "object", "description": "Specifies the amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"instructed_amount": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.  Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1"}}, "required": ["instructed_amount"]}, {"type": "object", "additionalProperties": false, "properties": {"equivalent_amount": {"description": "Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.", "$ref": "#/definitions/EquivalentAmount2__1"}}, "required": ["equivalent_amount"]}]}, "AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BaseOneRate": {"type": "string", "description": "Rate expressed as a decimal, for example, 0.7 is 7/10 and 70%.", "maxLength": 12}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__2": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__2"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__3": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1"}, "branch_identification": {"description": "Identifies a specific branch of a financial institution.  Usage: This component should be used in case the identification information in the financial institution component does not provide identification up to branch level.", "$ref": "#/definitions/BranchData3__1"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__4": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__3"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__5": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__4"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__6": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__5"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__7": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__6"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__8": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__7"}}, "required": ["financial_institution_identification"]}, "BranchData3__1": {"type": "object", "description": "Information that locates and identifies a specific branch of a financial institution.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a branch of a financial institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}}, "CBPR_Amount__1": {"type": "object", "additionalProperties": false, "properties": {"currency": {"$ref": "#/definitions/ActiveCurrencyCode"}, "amount": {"type": "string", "maxLength": 15, "pattern": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$"}}, "required": ["currency", "amount"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "CBPR_RestrictedFINXMax105Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 105 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 105, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax320Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]\r\n", "minLength": 1, "maxLength": 320, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax34Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash \"/\" at the beginning and end of line and double slash \"//\" within the line.", "minLength": 1, "maxLength": 34, "pattern": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$"}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax4Text_Extended": {"type": "string", "description": "Specifies a character string witha minimum length of 1, and a maximum length of 4 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 4, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax70Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CashAccount38": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice"}, "type": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice"}, "currency": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "name": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/Max70Text"}, "proxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1"}}, "required": ["identification"]}, "CashAccount38__1": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1"}, "type": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1"}, "currency": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "name": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text"}, "proxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1"}}, "required": ["identification"]}, "CashAccount38__2": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1"}, "type": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1"}, "currency": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "name": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "proxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1"}}, "required": ["identification"]}, "CashAccountType2Choice": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "CashAccountType2Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "CategoryPurpose1Choice__1": {"type": "object", "description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories.\nUsage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Category purpose, as published in an external category purpose code list.", "$ref": "#/definitions/ExternalCategoryPurpose1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Category purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ChargeBearerType1Code__1": {"type": "string", "description": "Specifies which party(ies) will pay charges due for processing of the instruction.\n*`CRED`-All transaction charges are to be borne by the creditor.\n*`SHAR`-In a credit transfer context, means that transaction charges on the sender side are to be borne by the debtor, transaction charges on the receiver side are to be borne by the creditor. In a direct debit context, means that transaction charges on the sender side are to be borne by the creditor, transaction charges on the receiver side are to be borne by the debtor.", "enum": ["CRED", "SHAR"]}, "Charges7__1": {"type": "object", "description": "Provides information on the charges related to the payment transaction.", "additionalProperties": false, "properties": {"amount": {"description": "Transaction charges to be paid by the charge bearer.", "$ref": "#/definitions/CBPR_Amount__1"}, "agent": {"description": "Agent that takes the transaction charges or to which the transaction charges are due.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}}, "required": ["amount", "agent"]}, "ClearingChannel2Code": {"type": "string", "description": "Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.\n*`RTGS`-Clearing channel is a real-time gross settlement system.\n*`RTNS`-Clearing channel is a real-time net settlement system.\n*`MPNS`-Clearing channel is a mass payment net settlement system.\n*`BOOK`-Payment through internal book transfer.", "enum": ["RTGS", "RTNS", "MPNS", "BOOK"]}, "ClearingSystemIdentification2Choice": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Identification code for a clearing system, that has not yet been identified in the list of clearing systems.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code"}}, "required": ["code"]}]}, "ClearingSystemIdentification2Choice__2": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Identification code for a clearing system, that has not yet been identified in the list of clearing systems.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ClearingSystemMemberIdentification2": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"clearing_system_identification": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice"}, "member_identification": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/Max35Text"}}, "required": ["member_identification"]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"clearing_system_identification": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1"}, "member_identification": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text"}}, "required": ["clearing_system_identification", "member_identification"]}, "ClearingSystemMemberIdentification2__2": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"clearing_system_identification": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__2"}, "member_identification": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text"}}, "required": ["clearing_system_identification", "member_identification"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code)."}, "CreditDebitCode": {"type": "string", "description": "Specifies if an operation is an increase or a decrease.\n*`CRDT`-Operation is an increase.\n*`DBIT`-Operation is a decrease.", "enum": ["CRDT", "DBIT"]}, "CreditorReferenceInformation2__1": {"type": "object", "description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of creditor reference.", "$ref": "#/definitions/CreditorReferenceType2__1"}, "reference": {"description": "Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.  Usage: If available, the initiating party should provide this reference in the structured remittance information, to enable reconciliation by the creditor upon receipt of the amount of money.  If the business context requires the use of a creditor reference or a payment remit identification, and only one identifier can be passed through the end-to-end chain, the creditor's reference or payment remittance identification should be quoted in the end-to-end transaction identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}}, "CreditorReferenceType1Choice__1": {"type": "object", "description": "Specifies the type of document referred by the creditor.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Type of creditor reference, in a coded form.", "$ref": "#/definitions/DocumentType3Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Creditor reference type, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "CreditorReferenceType2__1": {"type": "object", "description": "Specifies the type of creditor reference.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Coded or proprietary format creditor reference type.", "$ref": "#/definitions/CreditorReferenceType1Choice__1"}, "issuer": {"description": "Entity that assigns the credit reference type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "DateAndDateTime2Choice__1": {"type": "object", "description": "Choice between a date or a date and time format.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"date": {"description": "Specified date.", "$ref": "#/definitions/ISODate"}}, "required": ["date"]}, {"type": "object", "additionalProperties": false, "properties": {"date_time": {"description": "Specified date and time.", "$ref": "#/definitions/CBPR_DateTime"}}, "required": ["date_time"]}]}, "DateAndPlaceOfBirth1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"birth_date": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate"}, "province_of_birth": {"description": "Province where a person was born.", "$ref": "#/definitions/Max35Text"}, "city_of_birth": {"description": "City where a person was born.", "$ref": "#/definitions/Max35Text"}, "country_of_birth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode"}}, "required": ["birth_date", "city_of_birth", "country_of_birth"]}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"birth_date": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate"}, "province_of_birth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "city_of_birth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_of_birth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode"}}, "required": ["birth_date", "city_of_birth", "country_of_birth"]}, "DatePeriod2": {"type": "object", "description": "Range of time defined by a start date and an end date.", "additionalProperties": false, "properties": {"from_date": {"description": "Start date of the range.", "$ref": "#/definitions/ISODate"}, "to_date": {"description": "End date of the range.", "$ref": "#/definitions/ISODate"}}, "required": ["from_date", "to_date"]}, "DecimalNumber": {"type": "string", "description": "Number of objects represented as a decimal number, for example 0.75 or 45.6.", "maxLength": 19}, "DiscountAmountAndType1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "DiscountAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice__1"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "DiscountAmountType1Choice": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "DiscountAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "DocumentAdjustment1__1": {"type": "object", "description": "Set of elements used to provide information on the amount and reason of the document adjustment.", "additionalProperties": false, "properties": {"amount": {"description": "Amount of money of the document adjustment.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "credit_debit_indicator": {"description": "Specifies whether the adjustment must be subtracted or added to the total amount.", "$ref": "#/definitions/CreditDebitCode"}, "reason": {"description": "Specifies the reason for the adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax4Text_Extended"}, "additional_information": {"description": "Provides further details on the document adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}, "required": ["amount"]}, "DocumentLineIdentification1__1": {"type": "object", "description": "Identifies the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of referred document line identification.", "$ref": "#/definitions/DocumentLineType1__1"}, "number": {"description": "Identification of the type specified for the referred document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "related_date": {"description": "Date associated with the referred document line.", "$ref": "#/definitions/ISODate"}}}, "DocumentLineInformation1__1": {"type": "object", "description": "Provides document line information.\r\n", "additionalProperties": false, "properties": {"identification": {"type": "array", "description": "Provides identification of the document line.", "items": {"$ref": "#/definitions/DocumentLineIdentification1__1"}}, "description": {"description": "Description associated with the document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "amount": {"description": "Provides details on the amounts of the document line.", "$ref": "#/definitions/RemittanceAmount3__1"}}, "required": ["identification"]}, "DocumentLineType1Choice": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Line identification type in a coded form.", "$ref": "#/definitions/ExternalDocumentLineType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "DocumentLineType1__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the referred document line identification.", "$ref": "#/definitions/DocumentLineType1Choice"}, "issuer": {"description": "Identification of the issuer of the reference document line identificationtype.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "DocumentType3Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`RADM`-Document is a remittance advice sent separately from the current transaction.\n*`RPIN`-Document is a linked payment instruction to which the current payment instruction is related, for example, in a cover scenario.\n*`FXDR`-Document is a pre-agreed or pre-arranged foreign exchange transaction to which the payment transaction refers.\n*`DISP`-Document is a dispatch advice.\n*`PUOR`-Document is a purchase order.\n*`SCOR`-Document is a structured communication reference provided by the creditor to identify the referred transaction.", "enum": ["RADM", "RPIN", "FXDR", "DISP", "PUOR", "SCOR"]}, "DocumentType6Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`MSIN`-Document is an invoice claiming payment for the supply of metered services, for example gas or electricity supplied to a fixed meter.\n*`CNFA`-Document is a credit note for the final amount settled for a commercial transaction.\n*`DNFA`-Document is a debit note for the final amount settled for a commercial transaction.\n*`CINV`-Document is an invoice.\n*`CREN`-Document is a credit note.\n*`DEBN`-Document is a debit note.\n*`HIRI`-Document is an invoice for the hiring of human resources or renting goods or equipment.\n*`SBIN`-Document is an invoice issued by the debtor.\n*`CMCN`-Document is an agreement between the parties, stipulating the terms and conditions of the delivery of goods or services.\n*`SOAC`-Document is a statement of the transactions posted to the debtor's account at the supplier.\n*`DISP`-Document is a dispatch advice.\n*`BOLD`-Document is a shipping notice.\n*`VCHR`-Document is an electronic payment document.\n*`AROI`-Document is a payment that applies to a specific source document.\n*`TSUT`-Document is a transaction identifier as assigned by the Trade Services Utility.\n*`PUOR`-Document is a purchase order.", "enum": ["MSIN", "CNFA", "DNFA", "CINV", "CREN", "DEBN", "HIRI", "SBIN", "CMCN", "SOAC", "DISP", "BOLD", "VCHR", "AROI", "TSUT", "PUOR"]}, "EquivalentAmount2__1": {"type": "object", "description": "Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.", "additionalProperties": false, "properties": {"amount": {"description": "Amount of money to be moved between debtor and creditor, before deduction of charges, expressed in the currency of the debtor's account, and to be moved in a different currency. Usage: The first agent will convert the equivalent amount into the amount to be moved.", "$ref": "#/definitions/CBPR_Amount__1"}, "currency_of_transfer": {"description": "Specifies the currency of the to be transferred amount, which is different from the currency of the debtor's account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}}, "required": ["amount", "currency_of_transfer"]}, "Exact2NumericText": {"type": "string", "description": "Specifies a numeric string with an exact length of 2 digits.", "pattern": "^[0-9]{2}$"}, "ExternalAccountIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalCashAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalCategoryPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the category purpose, as published in an external category purpose code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalDiscountAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalDocumentLineType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the document line type as published in an external document type code list."}, "ExternalFinancialInstitutionIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external financial institution identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalGarnishmentType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the garnishment type as published in an external document type code list."}, "ExternalLocalInstrument1Code": {"type": "string", "minLength": 1, "maxLength": 35, "description": "Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalMandateSetupReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external mandate setup reason code in the format of character string with a maximum length of 4 characters.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalProxyAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external proxy account type code, as published in the proxy account type external code set.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external purpose code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalReturnReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the return reason, as published in an external return reason code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalServiceLevel1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external service level code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalTaxAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "FinancialIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalFinancialInstitutionIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}}}, "FinancialInstitutionIdentification18__2": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}}, "required": ["bicfi"]}, "FinancialInstitutionIdentification18__3": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/Max140Text"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3"}}}, "FinancialInstitutionIdentification18__4": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/Max140Text"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3"}}}, "FinancialInstitutionIdentification18__5": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__2"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "other": {"description": "Unique identification of an agent, as assigned by an institution, using an identification scheme.", "$ref": "#/definitions/GenericFinancialIdentification1__1"}}}, "FinancialInstitutionIdentification18__6": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__2"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}}}, "FinancialInstitutionIdentification18__7": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "other": {"description": "Unique identification of an agent, as assigned by an institution, using an identification scheme.", "$ref": "#/definitions/GenericFinancialIdentification1__1"}}}, "Frequency36Choice": {"type": "object", "description": "Choice of format for a frequency, for example, the frequency of payment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"type": {"description": "Specifies a frequency in terms of a specified period type.", "$ref": "#/definitions/Frequency6Code"}}, "required": ["type"]}, {"type": "object", "additionalProperties": false, "properties": {"period": {"description": "Specifies a frequency in terms of a count per period within a specified period type.", "$ref": "#/definitions/FrequencyPeriod1"}}, "required": ["period"]}, {"type": "object", "additionalProperties": false, "properties": {"point_in_time": {"description": "Specifies a frequency in terms of an exact point in time or moment within a specified period type.", "$ref": "#/definitions/FrequencyAndMoment1"}}, "required": ["point_in_time"]}]}, "Frequency6Code": {"type": "string", "description": "Specifies the regularity of an event.\n*`YEAR`-Event takes place every year or once a year.\n*`MNTH`-Event takes place every month or once a month.\n*`QURT`-Event takes place every three months or four times a year.\n*`MIAN`-Event takes place every six months or two times a year.\n*`WEEK`-Event takes place once a week.\n*`DAIL`-Event takes place every day.\n*`ADHO`-Event takes place on request or as necessary.\n*`INDA`-Event takes place several times a day.\n*`FRTN`-Event takes place every two weeks.", "enum": ["YEAR", "MNTH", "QURT", "MIAN", "WEEK", "DAIL", "ADHO", "INDA", "FRTN"]}, "FrequencyAndMoment1": {"type": "object", "description": "Defines a frequency in terms a specific moment within a specified period type.", "additionalProperties": false, "properties": {"type": {"description": "Period for which the number of instructions are to be created and processed.", "$ref": "#/definitions/Frequency6Code"}, "point_in_time": {"description": "Further information on the exact point in time the event should take place.", "$ref": "#/definitions/Exact2NumericText"}}, "required": ["type", "point_in_time"]}, "FrequencyPeriod1": {"type": "object", "description": "Defines a frequency in terms on counts per period for a specific period type.", "additionalProperties": false, "properties": {"type": {"description": "Period for which the number of instructions are to be created and processed.", "$ref": "#/definitions/Frequency6Code"}, "count_per_period": {"description": "Number of instructions to be created and processed during the specified period.", "$ref": "#/definitions/DecimalNumber"}}, "required": ["type", "count_per_period"]}, "Garnishment3__1": {"type": "object", "description": "Provides remittance information about a payment for garnishment-related purposes.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of garnishment.", "$ref": "#/definitions/GarnishmentType1__1"}, "garnishee": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the garnisher.", "$ref": "#/definitions/PartyIdentification135__5"}, "garnishment_administrator": {"description": "Party on the credit side of the transaction who administers the garnishment on behalf of the ultimate beneficiary.", "$ref": "#/definitions/PartyIdentification135__5"}, "reference_number": {"description": "Reference information that is specific to the agency receiving the garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "date": {"description": "Date of payment which garnishment was taken from.", "$ref": "#/definitions/ISODate"}, "remitted_amount": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "family_medical_insurance_indicator": {"description": "Indicates if the person to whom the garnishment applies (that is, the ultimate debtor) has family medical insurance coverage available.", "$ref": "#/definitions/TrueFalseIndicator"}, "employee_termination_indicator": {"description": "Indicates if the employment of the person to whom the garnishment applies (that is, the ultimate debtor) has been terminated.", "$ref": "#/definitions/TrueFalseIndicator"}}, "required": ["type"]}, "GarnishmentType1Choice__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Garnishment type in a coded form. Would suggest this to be an External Code List to contain: GNCS    Garnishment from a third party payer for Child Support GNDP    Garnishment from a Direct Payer for Child Support GTPP     Garnishment from a third party payer to taxing agency.", "$ref": "#/definitions/ExternalGarnishmentType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "GarnishmentType1__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the garnishment.", "$ref": "#/definitions/GarnishmentType1Choice__1"}, "issuer": {"description": "Identification of the issuer of the garnishment type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "GenericAccountIdentification1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/Max34Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text"}}, "required": ["identification"]}, "GenericAccountIdentification1__1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax34Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericFinancialIdentification1__1": {"type": "object", "description": "Information related to an identification of a financial institution.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/FinancialIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericOrganisationIdentification1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/Max35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text"}}, "required": ["identification"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericOrganisationIdentification1__2": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__2"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification", "scheme_name"]}, "GenericOrganisationIdentification1__3": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__3"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["identification"]}, "GenericPersonIdentification1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/Max35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text"}}, "required": ["identification"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericPersonIdentification1__2": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__2"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification", "scheme_name"]}, "GenericPersonIdentification1__3": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__3"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["identification"]}, "GroupHeader90__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"message_identification": {"description": "Point to point reference, as assigned by the instructing party and sent to the next party in the chain, to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "creation_date_time": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime"}, "number_of_transactions": {"description": "Number of individual transactions contained in the message.", "$ref": "#/definitions/Max15NumericText_fixed"}, "settlement_information": {"description": "Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.", "$ref": "#/definitions/SettlementInstruction7__1"}}, "required": ["message_identification", "creation_date_time", "number_of_transactions", "settlement_information"]}, "IBAN2007Identifier": {"type": "string", "description": "The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard \"Banking and related financial services - International Bank Account Number (IBAN)\" and replaced by the more recent edition of the standard.", "pattern": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$"}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$"}, "LocalInstrument2Choice__1": {"type": "object", "description": "Set of elements that further identifies the type of local instruments being requested by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the local instrument, as published in an external local instrument code list.", "$ref": "#/definitions/ExternalLocalInstrument1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the local instrument, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "MandateRelatedInformation14__1": {"type": "object", "description": "Provides further details related to a direct debit mandate signed between the creditor and the debtor.", "additionalProperties": false, "properties": {"mandate_identification": {"description": "Unique identification, as assigned by the creditor, to unambiguously identify the mandate.", "$ref": "#/definitions/Max35Text"}, "date_of_signature": {"description": "Date on which the direct debit mandate has been signed by the debtor.", "$ref": "#/definitions/ISODate"}, "amendment_indicator": {"description": "Indicator notifying whether the underlying mandate is amended or not.", "$ref": "#/definitions/TrueFalseIndicator"}, "amendment_information_details": {"description": "List of mandate elements that have been modified.", "$ref": "#/definitions/AmendmentInformationDetails13__1"}, "electronic_signature": {"description": "Additional security provisions, such as a digital signature, as provided by the debtor.", "$ref": "#/definitions/Max1025Text"}, "first_collection_date": {"description": "Date of the first collection of a direct debit as per the mandate.", "$ref": "#/definitions/ISODate"}, "final_collection_date": {"description": "Date of the final collection of a direct debit as per the mandate.", "$ref": "#/definitions/ISODate"}, "frequency": {"description": "Regularity with which direct debit instructions are to be created and processed.", "$ref": "#/definitions/Frequency36Choice"}, "reason": {"description": "Reason for the direct debit mandate to allow the user to distinguish between different mandates for the same creditor.", "$ref": "#/definitions/MandateSetupReason1Choice"}, "tracking_days": {"description": "Specifies the number of days the direct debit instruction must be tracked.", "$ref": "#/definitions/Exact2NumericText"}}}, "MandateSetupReason1Choice": {"type": "object", "description": "Specifies the reason for the setup of the mandate.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Reason for the mandate setup, as published in an external reason code list.", "$ref": "#/definitions/ExternalMandateSetupReason1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Reason for the mandate setup, in a proprietary form.", "$ref": "#/definitions/Max70Text"}}, "required": ["proprietary"]}]}, "Max1025Text": {"type": "string", "description": "Specifies a character string with a maximum length of 1025 characters.", "minLength": 1, "maxLength": 1025}, "Max140Text": {"type": "string", "description": "Specifies a character string with a maximum length of 140 characters.", "minLength": 1, "maxLength": 140}, "Max15NumericText_fixed": {"type": "string", "description": "\n*`1`-null", "enum": ["1"]}, "Max16Text": {"type": "string", "description": "Specifies a character string with a maximum length of 16 characters.", "minLength": 1, "maxLength": 16}, "Max2048Text": {"type": "string", "description": "Specifies a character string with a maximum length of 2048 characters.", "minLength": 1, "maxLength": 2048}, "Max34Text": {"type": "string", "description": "Specifies a character string with a maximum length of 34 characters.", "minLength": 1, "maxLength": 34}, "Max35Text": {"type": "string", "description": "Specifies a character string with a maximum length of 35 characters.", "minLength": 1, "maxLength": 35}, "Max70Text": {"type": "string", "description": "Specifies a character string with a maximum length of 70characters.", "minLength": 1, "maxLength": 70}, "Number": {"type": "string", "description": "Number of objects represented as an integer.", "maxLength": 19}, "OrganisationIdentification29": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1"}}}}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}}}}, "OrganisationIdentification29__2": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__2"}}}}, "OrganisationIdentification29__3": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__3"}}}}, "OrganisationIdentificationSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "OrganisationIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}]}, "OrganisationIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "OriginalGroupInformation29__1": {"type": "object", "description": "Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.", "additionalProperties": false, "properties": {"original_message_identification": {"description": "Point to point reference assigned by the original instructing party to unambiguously identify the original message.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_message_name_identification": {"description": "Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_creation_date_time": {"description": "Original date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime"}}, "required": ["original_message_identification", "original_message_name_identification"]}, "OriginalTransactionReference28__1": {"type": "object", "description": "Key elements used to refer the original transaction.", "additionalProperties": false, "properties": {"interbank_settlement_amount": {"description": "Amount of money moved between the instructing agent and the instructed agent.", "$ref": "#/definitions/CBPR_Amount__1"}, "amount": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.", "$ref": "#/definitions/AmountType4Choice__1"}, "interbank_settlement_date": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.", "$ref": "#/definitions/ISODate"}, "requested_collection_date": {"description": "Date and time at which the creditor requests that the amount of money is to be collected from the debtor.", "$ref": "#/definitions/ISODate"}, "requested_execution_date": {"description": "Date at which the initiating party requests the clearing agent to process the payment.  Usage: This is the date on which the debtor's account is to be debited. If payment by cheque, the date when the cheque must be generated by the bank.", "$ref": "#/definitions/DateAndDateTime2Choice__1"}, "creditor_scheme_identification": {"description": "Credit party that signs the mandate.", "$ref": "#/definitions/PartyIdentification135__4"}, "settlement_information": {"description": "Specifies the details on how the settlement of the original transaction(s) between the instructing agent and the instructed agent was completed.", "$ref": "#/definitions/SettlementInstruction7__2"}, "payment_type_information": {"description": "Set of elements used to further specify the type of transaction.", "$ref": "#/definitions/PaymentTypeInformation27__1"}, "payment_method": {"description": "Specifies the means of payment that will be used to move the amount of money.", "$ref": "#/definitions/PaymentMethod4Code"}, "mandate_related_information": {"description": "Provides further details of the mandate signed between the creditor and the debtor.", "$ref": "#/definitions/MandateRelatedInformation14__1"}, "remittance_information": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.", "$ref": "#/definitions/RemittanceInformation16__1"}, "ultimate_debtor": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__1"}, "debtor": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__4"}, "debtor_account": {"description": "Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1"}, "debtor_agent": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__7"}, "debtor_agent_account": {"description": "Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "creditor_agent": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3"}, "creditor_agent_account": {"description": "Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1"}, "creditor": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__5"}, "creditor_account": {"description": "Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1"}, "ultimate_creditor": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__1"}, "purpose": {"description": "Underlying reason for the payment transaction.  Usage:  Purpose is used by the end customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/Purpose2Choice__1"}}}, "Party38Choice": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13"}}, "required": ["private_identification"]}]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1"}}, "required": ["private_identification"]}]}, "Party38Choice__2": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__2"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__2"}}, "required": ["private_identification"]}]}, "Party38Choice__3": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__3"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__3"}}, "required": ["private_identification"]}]}, "Party40Choice__1": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"party": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__1"}}, "required": ["party"]}]}, "Party40Choice__2": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"party": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__2"}}, "required": ["party"]}, {"type": "object", "additionalProperties": false, "properties": {"agent": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}}, "required": ["agent"]}]}, "Party40Choice__3": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"party": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__3"}}, "required": ["party"]}, {"type": "object", "additionalProperties": false, "properties": {"agent": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}}, "required": ["agent"]}]}, "Party40Choice__4": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"party": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__2"}}, "required": ["party"]}, {"type": "object", "additionalProperties": false, "properties": {"agent": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__6"}}, "required": ["agent"]}]}, "Party40Choice__5": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"party": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__3"}}, "required": ["party"]}, {"type": "object", "additionalProperties": false, "properties": {"agent": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__8"}}, "required": ["agent"]}]}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__2": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__2"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__3": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__4": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/Max140Text"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__5": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__3"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PaymentMethod4Code": {"type": "string", "description": "Specifies the transfer method that will be used to transfer an amount of money.\n*`CHK`-Written order to a bank to pay a certain amount of money from one person to another person.\n*`TRF`-Transfer of an amount of money in the books of the account servicer.\n*`DD`-Collection of an amount of money from the debtor's bank account by the creditor. The amount of money and dates of collections may vary.\n*`TRA`-Transfer of an amount of money in the books of the account servicer. An advice should be sent back to the account owner.", "enum": ["CHK", "TRF", "DD", "TRA"]}, "PaymentReturnReason6__1": {"type": "object", "description": "Provides further details on the reason of the return of the transaction.", "additionalProperties": false, "properties": {"originator": {"description": "Party that issues the return.", "$ref": "#/definitions/PartyIdentification135__3"}, "reason": {"description": "Specifies the reason for the return.", "$ref": "#/definitions/ReturnReason5Choice__1"}, "additional_information": {"type": "array", "maxItems": 2, "description": "Further details on the return reason.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax105Text"}}}, "required": ["reason"]}, "PaymentReturnV09": {"type": "object", "description": "Scope\r\nThe PaymentReturn message is sent by an agent to the previous agent in the payment chain to undo a payment previously settled.\r\nUsage\r\nThe PaymentReturn message is exchanged between agents to return funds after settlement of credit transfer instructions (that is FIToFICustomerCreditTransfer message and FinancialInstitutionCreditTransfer message) or direct debit instructions (FIToFICustomerDirectDebit message).\r\nThe PaymentReturn message should not be used between agents and non-financial institution customers. Non-financial institution customers will be informed about a debit or a credit on their account(s) through a BankToCustomerDebitCreditNotification message ('notification') and/or BankToCustomerAccountReport/BankToCustomerStatement message ('statement').\r\nThe PaymentReturn message can be used to return single instructions or multiple instructions from one or different files.\r\nThe PaymentReturn message can be used in domestic and cross-border scenarios.\r\nThe PaymentReturn message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.", "additionalProperties": false, "properties": {"group_header": {"description": "Set of characteristics shared by all individual transactions included in the message.", "$ref": "#/definitions/GroupHeader90__1"}, "transaction_information": {"description": "Information concerning the original transactions, to which the return message refers.", "$ref": "#/definitions/PaymentTransaction112__1"}}, "required": ["group_header", "transaction_information"]}, "PaymentTransaction112__1": {"type": "object", "description": "Provides further details on the reference and status on the original transactions, included in the original instruction, to which the return message applies.", "additionalProperties": false, "properties": {"return_identification": {"description": "Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the returned transaction. Usage: The instructing party is the party sending the return message and not the party that sent the original instruction that is being returned.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_group_information": {"description": "Provides information on the original message.", "$ref": "#/definitions/OriginalGroupInformation29__1"}, "original_instruction_identification": {"description": "Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_end_to_end_identification": {"description": "Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_transaction_identification": {"description": "Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_uetr": {"description": "Universally unique identifier to provide the original end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier"}, "original_clearing_system_reference": {"description": "Unique reference, as assigned by the original clearing system, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_interbank_settlement_amount": {"description": "Amount of money moved between the instructing agent and the instructed agent, as provided in the original instruction.", "$ref": "#/definitions/CBPR_Amount__1"}, "original_interbank_settlement_date": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.  Usage: the OriginalInterbankSettlementDate is the interbank settlement date of the original instruction return message, and not of the return message.", "$ref": "#/definitions/ISODate"}, "returned_interbank_settlement_amount": {"description": "Amount of money to be moved between the instructing agent and the instructed agent in the returned instruction.", "$ref": "#/definitions/CBPR_Amount__1"}, "interbank_settlement_date": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.  Usage: the InterbankSettlementDate is the interbank settlement date of the return message, and not of the original instruction.", "$ref": "#/definitions/ISODate"}, "settlement_priority": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.   Usage: the SettlementPriority is the settlement priority of the return message, and not of the original instruction.", "$ref": "#/definitions/Priority3Code"}, "settlement_time_indication": {"description": "Provides information on the occurred settlement time(s) of the payment transaction.", "$ref": "#/definitions/SettlementDateTimeIndication1__1"}, "returned_instructed_amount": {"description": "Amount of money to be moved between the debtor and the creditor, before deduction of charges, in the returned transaction. Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1"}, "exchange_rate": {"description": "Factor used to convert an amount from one currency into another. This reflects the price at which one currency was bought with another currency.", "$ref": "#/definitions/BaseOneRate"}, "charge_bearer": {"description": "Specifies which party/parties will bear the charges associated with the processing of the payment transaction.  Usage: The ChargeBearer applies to the return message, not to the original instruction.", "$ref": "#/definitions/ChargeBearerType1Code__1"}, "charges_information": {"type": "array", "description": "Provides information on the charges to be paid by the charge bearer(s) related to the processing of the return transaction.", "items": {"$ref": "#/definitions/Charges7__1"}}, "clearing_system_reference": {"description": "Unique reference, as assigned by the clearing system, to unambiguously identify the return instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "instructing_agent": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).  Usage: The instructing agent is the party sending the return message and not the party that sent the original instruction that is being returned.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2"}, "instructed_agent": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).  Usage: The instructed agent is the party receiving the return message and not the party that received the original instruction that is being returned.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2"}, "return_chain": {"description": "Provides all parties (agents and non-agents) involved in a return transaction.", "$ref": "#/definitions/TransactionParties7__1"}, "return_reason_information": {"description": "Provides detailed information on the return reason.", "$ref": "#/definitions/PaymentReturnReason6__1"}, "original_transaction_reference": {"description": "Key elements used to identify the original transaction that is being referred to.", "$ref": "#/definitions/OriginalTransactionReference28__1"}}, "required": ["original_end_to_end_identification", "original_uetr", "returned_interbank_settlement_amount", "interbank_settlement_date", "charge_bearer", "instructing_agent", "instructed_agent", "return_chain", "return_reason_information"]}, "PaymentTypeInformation27__1": {"type": "object", "description": "Provides further details of the type of payment.", "additionalProperties": false, "properties": {"instruction_priority": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.", "$ref": "#/definitions/Priority2Code"}, "clearing_channel": {"description": "Specifies the clearing channel to be used to process the payment instruction.", "$ref": "#/definitions/ClearingChannel2Code"}, "service_level": {"type": "array", "maxItems": 3, "description": "Agreement under which or rules under which the transaction should be processed.", "items": {"$ref": "#/definitions/ServiceLevel8Choice__1"}}, "local_instrument": {"description": "User community specific instrument.  Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.", "$ref": "#/definitions/LocalInstrument2Choice__1"}, "sequence_type": {"description": "Identifies the direct debit sequence, such as first, recurrent, final or one-off.", "$ref": "#/definitions/SequenceType3Code"}, "category_purpose": {"description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories. Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/CategoryPurpose1Choice__1"}}}, "PercentageRate": {"type": "string", "description": "Rate expressed as a percentage, that is, in hundredths, for example, 0.7 is 7/10 of a percent, and 7.0 is 7%.", "maxLength": 12}, "PersonIdentification13": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1"}, "other": {"type": "array", "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1"}}}}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}}}}, "PersonIdentification13__2": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__2"}}}}, "PersonIdentification13__3": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__3"}}}}, "PersonIdentificationSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "PersonIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}]}, "PersonIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}}}}, "PostalAddress24__2": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 2, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}}}, "required": ["town_name", "country"]}, "PostalAddress24__3": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/Max70Text"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/Max70Text"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/Max70Text"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/Max16Text"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/Max35Text"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/Max70Text"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/Max16Text"}, "room": {"description": "Building room number.", "$ref": "#/definitions/Max70Text"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/Max16Text"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/Max35Text"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/Max35Text"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/Max35Text"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/Max35Text"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/Max70Text"}}}}, "Priority2Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["HIGH", "NORM"]}, "Priority3Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`URGT`-Priority level is urgent (highest priority possible).\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["URGT", "HIGH", "NORM"]}, "ProxyAccountIdentification1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"type": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice"}, "identification": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/Max2048Text"}}, "required": ["identification"]}, "ProxyAccountIdentification1__1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"type": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice__1"}, "identification": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/CBPR_RestrictedFINXMax320Text_Extended"}}, "required": ["identification"]}, "ProxyAccountType1Choice": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text"}}, "required": ["proprietary"]}]}, "ProxyAccountType1Choice__1": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "Purpose2Choice__1": {"type": "object", "description": "Specifies the underlying reason for the payment transaction.\nUsage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Underlying reason for the payment transaction, as published in an external purpose code list.", "$ref": "#/definitions/ExternalPurpose1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ReferredDocumentInformation7__1": {"type": "object", "description": "Set of elements used to identify the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of referred document.", "$ref": "#/definitions/ReferredDocumentType4__1"}, "number": {"description": "Unique and unambiguous identification of the referred document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "related_date": {"description": "Date associated with the referred document.", "$ref": "#/definitions/ISODate"}, "line_details": {"type": "array", "description": "Set of elements used to provide the content of the referred document line.", "items": {"$ref": "#/definitions/DocumentLineInformation1__1"}}}}, "ReferredDocumentType3Choice__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Document type in a coded form.", "$ref": "#/definitions/DocumentType6Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "ReferredDocumentType4__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the referred document.", "$ref": "#/definitions/ReferredDocumentType3Choice__1"}, "issuer": {"description": "Identification of the issuer of the reference document type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["code_or_proprietary"]}, "RemittanceAmount2__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"due_payable_amount": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "discount_applied_amount": {"type": "array", "description": "Amount specified for the referred document is the amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}}, "credit_note_amount": {"description": "Amount specified for the referred document is the amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "tax_amount": {"type": "array", "description": "Quantity of cash resulting from the calculation of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__2"}}, "adjustment_amount_and_reason": {"type": "array", "description": "Specifies detailed information on the amount and reason of the document adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}}, "remitted_amount": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}}, "RemittanceAmount3__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"due_payable_amount": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "discount_applied_amount": {"type": "array", "description": "Amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1"}}, "credit_note_amount": {"description": "Amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "tax_amount": {"type": "array", "description": "Amount of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}}, "adjustment_amount_and_reason": {"type": "array", "description": "Specifies detailed information on the amount and reason of the adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}}, "remitted_amount": {"description": "Amount of money remitted.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}}, "RemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system.", "additionalProperties": false, "properties": {"unstructured": {"description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in an unstructured form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "structured": {"type": "array", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "items": {"$ref": "#/definitions/StructuredRemittanceInformation16__1"}}}}, "ReturnReason5Choice__1": {"type": "object", "description": "Specifies the reason for the return of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Reason for the return, as published in an external reason code list.", "$ref": "#/definitions/ExternalReturnReason1Code"}}, "required": ["code"]}]}, "SequenceType3Code": {"type": "string", "description": "Specifies the type of the current transaction that belongs to a sequence of transactions.\n*`FRST`-First collection of a series of direct debit instructions.\n*`RCUR`-Direct debit instruction where the debtor's authorisation is used for regular direct debit transactions initiated by the creditor.\n*`FNAL`-Final collection of a series of direct debit instructions.\n*`OOFF`-Direct debit instruction where the debtor's authorisation is used to initiate one single direct debit transaction.\n*`RPRE`-Collection used to re-present previously reversed or returned direct debit transactions.", "enum": ["FRST", "RCUR", "FNAL", "OOFF", "RPRE"]}, "ServiceLevel8Choice__1": {"type": "object", "description": "Specifies the service level of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.", "$ref": "#/definitions/ExternalServiceLevel1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies a pre-agreed service or level of service between the parties, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "SettlementDateTimeIndication1__1": {"type": "object", "description": "Information on the occurred settlement time(s) of the payment transaction.", "additionalProperties": false, "properties": {"debit_date_time": {"description": "Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime"}, "credit_date_time": {"description": "Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime"}}}, "SettlementInstruction7__1": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"settlement_method": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__1"}, "settlement_account": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1"}}, "required": ["settlement_method"]}, "SettlementInstruction7__2": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"settlement_method": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__2"}, "settlement_account": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__2"}, "instructing_reimbursement_agent": {"description": "Agent through which the instructing agent will reimburse the instructed agent.  Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "instructing_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the instructing reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2"}, "instructed_reimbursement_agent": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If InstructedReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch. Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "instructed_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the instructed reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2"}, "third_reimbursement_agent": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If ThirdReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "third_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the third reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2"}}, "required": ["settlement_method"]}, "SettlementMethod1Code__1": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.", "enum": ["INDA", "INGA"]}, "SettlementMethod1Code__2": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.\n*`COVE`-Settlement is done through a cover payment.", "enum": ["INDA", "INGA", "COVE"]}, "StructuredRemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "additionalProperties": false, "properties": {"referred_document_information": {"type": "array", "description": "Provides the identification and the content of the referred document.", "items": {"$ref": "#/definitions/ReferredDocumentInformation7__1"}}, "referred_document_amount": {"description": "Provides details on the amounts of the referred document.", "$ref": "#/definitions/RemittanceAmount2__1"}, "creditor_reference_information": {"description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "$ref": "#/definitions/CreditorReferenceInformation2__1"}, "invoicer": {"description": "Identification of the organisation issuing the invoice, when it is different from the creditor or ultimate creditor.", "$ref": "#/definitions/PartyIdentification135__5"}, "invoicee": {"description": "Identification of the party to whom an invoice is issued, when it is different from the debtor or ultimate debtor.", "$ref": "#/definitions/PartyIdentification135__5"}, "tax_remittance": {"description": "Provides remittance information about a payment made for tax-related purposes.", "$ref": "#/definitions/TaxInformation7__1"}, "garnishment_remittance": {"description": "Provides remittance information about a payment for garnishment-related purposes.", "$ref": "#/definitions/Garnishment3__1"}, "additional_remittance_information": {"type": "array", "maxItems": 3, "description": "Additional information, in free text form, to complement the structured remittance information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}}, "TaxAmount2": {"type": "object", "description": "Set of elements used to provide information on the tax amount(s) of tax record.", "additionalProperties": false, "properties": {"rate": {"description": "Rate used to calculate the tax.", "$ref": "#/definitions/PercentageRate"}, "taxable_base_amount": {"description": "Amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "total_amount": {"description": "Total amount that is the result of the calculation of the tax for the record.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "details": {"type": "array", "description": "Set of elements used to provide details on the tax period and amount.", "items": {"$ref": "#/definitions/TaxRecordDetails2"}}}}, "TaxAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__1"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "TaxAmountAndType1__2": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__2"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "TaxAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "TaxAmountType1Choice__2": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "TaxAuthorisation1__1": {"type": "object", "description": "Details of the authorised tax paying party.", "additionalProperties": false, "properties": {"title": {"description": "Title or position of debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "name": {"description": "Name of the debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}, "TaxInformation7__1": {"type": "object", "description": "Details about tax paid, or to be paid, to the government in accordance with the law, including pre-defined parameters such as thresholds and type of account.", "additionalProperties": false, "properties": {"creditor": {"description": "Party on the credit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty1__1"}, "debtor": {"description": "Identifies the party on the debit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty2__1"}, "ultimate_debtor": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the taxing authority.", "$ref": "#/definitions/TaxParty2__1"}, "administration_zone": {"description": "Territorial part of a country to which the tax payment is related.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "reference_number": {"description": "Tax reference information that is specific to a taxing agency.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "method": {"description": "Method used to indicate the underlying business or how the tax is paid.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "total_taxable_base_amount": {"description": "Total amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "total_tax_amount": {"description": "Total amount of money as result of the calculation of the tax.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "date": {"description": "Date by which tax is due.", "$ref": "#/definitions/ISODate"}, "sequence_number": {"description": "Sequential number of the tax report.", "$ref": "#/definitions/Number"}, "record": {"type": "array", "description": "Record of tax details.", "items": {"$ref": "#/definitions/TaxRecord2__1"}}}}, "TaxParty1__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"tax_identification": {"description": "Tax identification number of the creditor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "registration_identification": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "tax_type": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}}, "TaxParty2__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"tax_identification": {"description": "Tax identification number of the debtor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "registration_identification": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "tax_type": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "authorisation": {"description": "Details of the authorised tax paying party.", "$ref": "#/definitions/TaxAuthorisation1__1"}}}, "TaxPeriod2": {"type": "object", "description": "Period of time details related to the tax payment.", "additionalProperties": false, "properties": {"year": {"description": "Year related to the tax payment.", "$ref": "#/definitions/ISODate"}, "type": {"description": "Identification of the period related to the tax payment.", "$ref": "#/definitions/TaxRecordPeriod1Code"}, "from_to_date": {"description": "Range of time between a start date and an end date for which the tax report is provided.", "$ref": "#/definitions/DatePeriod2"}}}, "TaxRecord2__1": {"type": "object", "description": "Set of elements used to define the tax record.", "additionalProperties": false, "properties": {"type": {"description": "High level code to identify the type of tax details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "category": {"description": "Specifies the tax code as published by the tax authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "category_details": {"description": "Provides further details of the category tax code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "debtor_status": {"description": "Code provided by local authority to identify the status of the party that has drawn up the settlement document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "certificate_identification": {"description": "Identification number of the tax report as assigned by the taxing authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "forms_code": {"description": "Identifies, in a coded form, on which template the tax report is to be provided.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "period": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2"}, "tax_amount": {"description": "Set of elements used to provide information on the amount of the tax record.", "$ref": "#/definitions/TaxAmount2"}, "additional_information": {"description": "Further details of the tax record.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}, "TaxRecordDetails2": {"type": "object", "description": "Provides information on the individual tax amount(s) per period of the tax record.", "additionalProperties": false, "properties": {"period": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2"}, "amount": {"description": "Underlying tax amount related to the specified period.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "TaxRecordPeriod1Code": {"type": "string", "description": "Specifies the period related to the tax payment.\n*`MM01`-Tax is related to the second month of the period.\n*`MM02`-Tax is related to the first month of the period.\n*`MM03`-Tax is related to the third month of the period.\n*`MM04`-Tax is related to the fourth month of the period.\n*`MM05`-Tax is related to the fifth month of the period.\n*`MM06`-Tax is related to the sixth month of the period.\n*`MM07`-Tax is related to the seventh month of the period.\n*`MM08`-Tax is related to the eighth month of the period.\n*`MM09`-Tax is related to the ninth month of the period.\n*`MM10`-Tax is related to the tenth month of the period.\n*`MM11`-Tax is related to the eleventh month of the period.\n*`MM12`-Tax is related to the twelfth month of the period.\n*`QTR1`-Tax is related to the first quarter of the period.\n*`QTR2`-Tax is related to the second quarter of the period.\n*`QTR3`-Tax is related to the third quarter of the period.\n*`QTR4`-Tax is related to the forth quarter of the period.\n*`HLF1`-Tax is related to the first half of the period.\n*`HLF2`-Tax is related to the second half of the period.", "enum": ["MM01", "MM02", "MM03", "MM04", "MM05", "MM06", "MM07", "MM08", "MM09", "MM10", "MM11", "MM12", "QTR1", "QTR2", "QTR3", "QTR4", "HLF1", "HLF2"]}, "TransactionParties7__1": {"type": "object", "description": "Provides further details on the parties specific to the individual transaction.", "additionalProperties": false, "properties": {"ultimate_debtor": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__1"}, "debtor": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__2"}, "initiating_party": {"description": "Party that initiates the payment. Usage: This can be either the debtor or a party that initiates the credit transfer on behalf of the debtor.", "$ref": "#/definitions/Party40Choice__1"}, "debtor_agent": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent1": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent2": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent3": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent1": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent2": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent3": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "creditor_agent": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3"}, "creditor": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__3"}, "ultimate_creditor": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__1"}}, "required": ["debtor", "creditor"]}, "TrueFalseIndicator": {"type": "boolean", "description": "A flag indicating a True or False value."}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$"}}}