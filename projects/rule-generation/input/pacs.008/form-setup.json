{"description": "This file is not used for constructing the form, but instead is meant for planning composition of form components and division of fields into form pages. Nonetheless, this file is used during the 'generate-rules' script to check whether all fields from the JSON schema are represented in the form, pointing out all missing and extraneous fields. In addition, the individual form pages will throw an error during development to make sure that they are consistent with this setup. 'advanced' fields are automatically hidden when the toggle 'isAdvancedMode' is false in the advanced-mode-service.", "pages": {"essentials": {"document": {"basic": ["CdtTrfTxInf-Dbtr-Nm[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-Dbtr-PstlAdr[PostalAddress24__1]", "CdtTrfTxInf-Dbtr-CtryOfRes[CountryCode]", "CdtTrfTxInf-DbtrAcct-Id-IBAN", "CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-DbtrAgt[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-Cdtr-Nm[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-Cdtr-PstlAdr[PostalAddress24__1]", "CdtTrfTxInf-Cdtr-CtryOfRes[CountryCode]", "CdtTrfTxInf-CdtrAcct-Id-IBAN", "CdtTrfTxInf-CdtrAcct-Id-Othr-Id", "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd", "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI[BICFIDec2014Identifier]", "CdtTrfTxInf-IntrBkSttlmAmt[CBPR_Amount__1]", "CdtTrfTxInf-IntrBkSttlmDt[ISODate]", "CdtTrfTxInf-ChrgBr", "CdtTrfTxInf-RmtInf-Ustrd[CBPR_RestrictedFINXMax140Text_Extended]"], "advanced": ["CdtTrfTxInf-DbtrAcct-Tp-Cd", "CdtTrfTxInf-DbtrAcct-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-DbtrAcct-Ccy[ActiveOrHistoricCurrencyCode]", "CdtTrfTxInf-DbtrAcct-Nm", "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-DbtrAcct-Prxy-Id", "CdtTrfTxInf-CdtrAcct-Tp-Cd", "CdtTrfTxInf-CdtrAcct-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-CdtrAcct-Ccy[ActiveOrHistoricCurrencyCode]", "CdtTrfTxInf-CdtrAcct-Nm", "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-CdtrAcct-Prxy-Id", "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId[ClearingSystemMemberIdentification2__1]", "CdtTrfTxInf-CdtrAgt-FinInstnId-LEI[LEIIdentifier]", "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr[PostalAddress24__1]", "CdtTrfTxInf-CdtrAgt-BrnchId-Id[CBPR_RestrictedFINXMax35Text]"]}, "bah": {"basic": [], "advanced": []}}, "paymentBasics": {"document": {"basic": ["CdtTrfTxInf-PmtId-TxId[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-PmtTpInf-InstrPrty", "CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd", "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-PmtTpInf-ClrChanl", "CdtTrfTxInf-PmtTpInf-LclInstrm-Cd", "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-Purp-Cd", "CdtTrfTxInf-Purp-Prtry[CBPR_RestrictedFINXMax35Text]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "amountCurrency": {"document": {"basic": ["CdtTrfTxInf-InstdAmt[CBPR_Amount__1]", "CdtTrfTxInf-XchgRate"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "settlementInformation": {"document": {"basic": ["CdtTrfTxInf-SttlmPrty", "CdtTrfTxInf-SttlmTmIndctn-DbtDtTm[CBPR_DateTime]", "CdtTrfTxInf-SttlmTmIndctn-CdtDtTm[CBPR_DateTime]", "CdtTrfTxInf-SttlmTmReq-FrTm[CBPR_Time]", "CdtTrfTxInf-SttlmTmReq-TillTm[CBPR_Time]", "CdtTrfTxInf-SttlmTmReq-CLSTm[CBPR_Time]", "CdtTrfTxInf-SttlmTmReq-RjctTm[CBPR_Time]", "GrpHdr-SttlmInf-InstgRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]", "GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct[CashAccount38__1]", "GrpHdr-SttlmInf-InstdRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]", "GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct[CashAccount38__1]", "GrpHdr-SttlmInf-ThrdRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]", "GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct[CashAccount38__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "debtorInformation": {"document": {"basic": ["CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC[AnyBICDec2014Identifier]", "CdtTrfTxInf-Dbtr-Id-OrgId-LEI[LEIIdentifier]", "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd[ExternalOrganisationIdentification1Code]", "CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth[DateAndPlaceOfBirth1__1]", "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd[ExternalPersonIdentification1Code]", "CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-UltmtDbtr[PartyIdentification135__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "creditorInformation": {"document": {"basic": ["CdtTrfTxInf-Cdtr-Id[Party38Choice__1]", "CdtTrfTxInf-UltmtCdtr[PartyIdentification135__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "financialInstitutionInformation": {"document": {"basic": ["CdtTrfTxInf-InitgPty[PartyIdentification135__1]", "CdtTrfTxInf-DbtrAgtAcct[CashAccount38__1]", "CdtTrfTxInf-CdtrAgtAcct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt1[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt1Acct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt2[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt2Acct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt3[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt3Acct[CashAccount38__1]", "CdtTrfTxInf-InstrForCdtrAgt-Cd", "CdtTrfTxInf-InstrForCdtrAgt-InstrInf[CBPR_RestrictedFINXMax140Text]", "CdtTrfTxInf-InstrForNxtAgt-InstrInf[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-IntrmyAgt1[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt1Acct[CashAccount38__1]", "CdtTrfTxInf-IntrmyAgt2[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt2Acct[CashAccount38__1]", "CdtTrfTxInf-IntrmyAgt3[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt3Acct[CashAccount38__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "remittanceInformation": {"document": {"basic": ["CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt[ISODate]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt[ISODate]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt[DiscountAmountAndType1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt[TaxAmountAndType1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn[DocumentAdjustment1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt[DiscountAmountAndType1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt[TaxAmountAndType1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn[DocumentAdjustment1__1]", "CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd", "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-Invcr[PartyIdentification135__4]", "CdtTrfTxInf-RmtInf-Strd-Invcee[PartyIdentification135__4]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr[TaxParty2__1]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr[TaxParty2__1]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt[ISODate]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd[TaxPeriod2]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd[TaxPeriod2]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee[PartyIdentification135__4]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr[PartyIdentification135__4]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt[ISODate]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd[TrueFalseIndicator]", "CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd[TrueFalseIndicator]", "CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-RltdRmtInf-RmtId[CBPR_RestrictedFINXMax35Text_Extended]", "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd", "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr", "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm[CBPR_RestrictedFINXMax140Text_Extended]", "CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr[PostalAddress24__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "regulatoryReporting": {"document": {"basic": ["CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd", "CdtTrfTxInf-RgltryRptg-Authrty-Nm[CBPR_RestrictedFINXMax140Text]", "CdtTrfTxInf-RgltryRptg-Authrty-Ctry[CountryCode]", "CdtTrfTxInf-RgltryRptg-Dtls-Tp[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-RgltryRptg-Dtls-Dt[ISODate]", "CdtTrfTxInf-RgltryRptg-Dtls-Ctry[CountryCode]", "CdtTrfTxInf-RgltryRptg-Dtls-Cd", "CdtTrfTxInf-RgltryRptg-Dtls-Amt[CBPR_Amount__1]", "CdtTrfTxInf-RgltryRptg-Dtls-Inf[CBPR_RestrictedFINXMax35Text]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "SERVER": {"document": {"basic": ["GrpHdr-CreDtTm[CBPR_DateTime]", "GrpHdr-MsgId[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-PmtId-InstrId", "CdtTrfTxInf-PmtId-EndToEndId[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-PmtId-UETR", "GrpHdr-SttlmInf-SttlmMtd", "CdtTrfTxInf-PmtId-ClrSysRef[CBPR_RestrictedFINXMax35Text]", "GrpHdr-NbOfTxs", "GrpHdr-SttlmInf-SttlmAcct[CashAccount38__1]", "CdtTrfTxInf-PmtTpInf-SvcLvl-Cd", "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry[CBPR_RestrictedFINXMax35Text]", "CdtTrfTxInf-ChrgsInf-Amt[CBPR_Amount__1]", "CdtTrfTxInf-ChrgsInf-Agt[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-InstgAgt[BranchAndFinancialInstitutionIdentification6__2]", "CdtTrfTxInf-InstdAgt[BranchAndFinancialInstitutionIdentification6__2]"], "advanced": []}, "bah": {"basic": ["BizMsgIdr[Max35Text]", "BizSvc[Max35Text]", "CharSet[UnicodeChartsCode]", "CpyDplct[CopyDuplicate1Code]", "CreDt[CBPR_DateTime]", "Fr[Party44Choice__1]", "MktPrctc-Id", "MktPrctc-Regy", "MsgDefIdr[Max35Text]", "<PERSON><PERSON><PERSON>", "PssblDplct", "Rltd-BizMsgIdr[Max35Text]", "Rltd-BizSvc[Max35Text]", "Rltd-CharSet[UnicodeChartsCode]", "Rltd-CpyDplct[CopyDuplicate1Code]", "Rltd-CreDt[CBPR_DateTime]", "Rltd-Fr[Party44Choice__1]", "Rltd-MsgDefIdr[Max35Text]", "Rltd-Prty", "Rltd-To[Party44Choice__1]", "To[Party44Choice__1]"], "advanced": []}}}, "definitions": {"document": {"ActiveOrHistoricCurrencyAndAmount": ["Ccy[ActiveOrHistoricCurrencyCode]", "amount"], "ActiveOrHistoricCurrencyCode": [], "AnyBICDec2014Identifier": [], "BICFIDec2014Identifier": [], "BranchAndFinancialInstitutionIdentification6__1": ["FinInstnId[FinancialInstitutionIdentification18__1]"], "BranchAndFinancialInstitutionIdentification6__2": ["FinInstnId-BICFI[BICFIDec2014Identifier]", "FinInstnId-ClrSysMmbId[ClearingSystemMemberIdentification2__1]", "FinInstnId-LEI[LEIIdentifier]"], "CashAccount38__1": ["Id-IBAN", "Id-<PERSON><PERSON>r-<PERSON>d", "Id-Othr-SchmeNm-Cd", "Id-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>[CBPR_RestrictedFINXMax35Text]", "Id-<PERSON><PERSON><PERSON>-<PERSON><PERSON>r[CBPR_RestrictedFINXMax35Text]", "Tp-Cd", "Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "Ccy[ActiveOrHistoricCurrencyCode]", "Nm", "Prxy-Tp-Cd", "Prxy-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "Prxy-Id"], "CBPR_Amount__1": ["amount", "Ccy"], "CBPR_DateTime": [], "CBPR_RestrictedFINXMax16Text_Extended": [], "CBPR_RestrictedFINXMax35Text": [], "CBPR_RestrictedFINXMax35Text_Extended": [], "CBPR_RestrictedFINXMax70Text_Extended": [], "CBPR_RestrictedFINXMax140Text": [], "CBPR_RestrictedFINXMax140Text_Extended": [], "CBPR_Time": [], "ClearingSystemIdentification2Choice__1": ["Cd"], "ClearingSystemMemberIdentification2__1": ["ClrSysId[ClearingSystemIdentification2Choice__1]", "MmbId"], "CountryCode": [], "DateAndPlaceOfBirth1__1": ["BirthDt[ISODate]", "PrvcOfBirth[CBPR_RestrictedFINXMax35Text_Extended]", "CityOfBirth[CBPR_RestrictedFINXMax35Text_Extended]", "CtryOfBirth[CountryCode]"], "DiscountAmountAndType1__1": ["Tp-Cd", "Tp-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "Amt[ActiveOrHistoricCurrencyAndAmount]"], "DocumentAdjustment1__1": ["Amt[ActiveOrHistoricCurrencyAndAmount]", "CdtDbtInd", "Rsn", "AddtlInf[CBPR_RestrictedFINXMax140Text_Extended]"], "ExternalOrganisationIdentification1Code": [], "ExternalPersonIdentification1Code": [], "FinancialInstitutionIdentification18__1": ["BICFI[BICFIDec2014Identifier]", "ClrSysMmbId[ClearingSystemMemberIdentification2__1]", "LEI[LEIIdentifier]", "Nm[CBPR_RestrictedFINXMax140Text_Extended]", "PstlAdr[PostalAddress24__1]"], "ISODate": [], "LEIIdentifier": [], "Party38Choice__1": ["OrgId-AnyBIC[AnyBICDec2014Identifier]", "OrgId-LEI[LEIIdentifier]", "OrgId-Othr-Id[CBPR_RestrictedFINXMax35Text]", "OrgId-Othr-SchmeNm-Cd[ExternalOrganisationIdentification1Code]", "OrgId-<PERSON>thr-<PERSON>hmeNm-<PERSON><PERSON>ry[CBPR_RestrictedFINXMax35Text]", "OrgId-Othr-Issr[CBPR_RestrictedFINXMax35Text]", "PrvtId-DtAndPlcOfBirth[DateAndPlaceOfBirth1__1]", "PrvtId-Othr-Id[CBPR_RestrictedFINXMax35Text]", "PrvtId-Othr-SchmeNm-Cd[ExternalPersonIdentification1Code]", "PrvtId-<PERSON><PERSON>r-<PERSON>hmeNm-<PERSON><PERSON><PERSON>[CBPR_RestrictedFINXMax35Text]", "PrvtId-Othr-Issr[CBPR_RestrictedFINXMax35Text]"], "PartyIdentification135__1": ["Nm[CBPR_RestrictedFINXMax140Text_Extended]", "PstlAdr[PostalAddress24__2]", "CtryOfRes[CountryCode]", "Id[Party38Choice__1]"], "PartyIdentification135__4": ["Nm[CBPR_RestrictedFINXMax140Text_Extended]", "PstlAdr[PostalAddress24__2]", "CtryOfRes[CountryCode]", "Id-OrgId-AnyBIC[AnyBICDec2014Identifier]", "Id-OrgId-LEI[LEIIdentifier]", "Id-OrgId-Othr-Id[CBPR_RestrictedFINXMax35Text_Extended]", "Id-OrgId-Othr-SchmeNm-Cd[ExternalOrganisationIdentification1Code]", "Id-OrgId-Othr-<PERSON><PERSON>eNm-<PERSON><PERSON><PERSON>[CBPR_RestrictedFINXMax35Text_Extended]", "Id-OrgId-Othr-Issr[CBPR_RestrictedFINXMax35Text_Extended]", "Id-PrvtId-DtAndPlcOfBirth[DateAndPlaceOfBirth1__1]", "Id-PrvtId-Othr-Id[CBPR_RestrictedFINXMax35Text_Extended]", "Id-PrvtId-Othr-SchmeNm-Cd[ExternalPersonIdentification1Code]", "Id-PrvtId-Othr-<PERSON>hmeNm-<PERSON><PERSON><PERSON>[CBPR_RestrictedFINXMax35Text_Extended]", "Id-PrvtId-Othr-Issr[CBPR_RestrictedFINXMax35Text_Extended]"], "PostalAddress24__1": ["Dept[CBPR_RestrictedFINXMax70Text_Extended]", "SubDept[CBPR_RestrictedFINXMax70Text_Extended]", "StrtNm[CBPR_RestrictedFINXMax70Text_Extended]", "BldgNb[CBPR_RestrictedFINXMax16Text_Extended]", "BldgNm[CBPR_RestrictedFINXMax35Text_Extended]", "Flr[CBPR_RestrictedFINXMax70Text_Extended]", "PstBx[CBPR_RestrictedFINXMax16Text_Extended]", "Room[CBPR_RestrictedFINXMax70Text_Extended]", "PstCd[CBPR_RestrictedFINXMax16Text_Extended]", "TwnNm[CBPR_RestrictedFINXMax35Text_Extended]", "TwnLctnNm[CBPR_RestrictedFINXMax35Text_Extended]", "DstrctNm[CBPR_RestrictedFINXMax35Text_Extended]", "CtrySubDvsn[CBPR_RestrictedFINXMax35Text_Extended]", "Ctry[CountryCode]", "AdrLine[CBPR_RestrictedFINXMax70Text_Extended]"], "PostalAddress24__2": ["Dept[CBPR_RestrictedFINXMax70Text_Extended]", "SubDept[CBPR_RestrictedFINXMax70Text_Extended]", "StrtNm[CBPR_RestrictedFINXMax70Text_Extended]", "BldgNb[CBPR_RestrictedFINXMax16Text_Extended]", "BldgNm[CBPR_RestrictedFINXMax35Text_Extended]", "Flr[CBPR_RestrictedFINXMax70Text_Extended]", "PstBx[CBPR_RestrictedFINXMax16Text_Extended]", "Room[CBPR_RestrictedFINXMax70Text_Extended]", "PstCd[CBPR_RestrictedFINXMax16Text_Extended]", "TwnNm[CBPR_RestrictedFINXMax35Text_Extended]", "TwnLctnNm[CBPR_RestrictedFINXMax35Text_Extended]", "DstrctNm[CBPR_RestrictedFINXMax35Text_Extended]", "CtrySubDvsn[CBPR_RestrictedFINXMax35Text_Extended]", "Ctry[CountryCode]", "AdrLine[CBPR_RestrictedFINXMax70Text_Extended]"], "TaxAmountAndType1__1": ["Tp-Cd", "Tp-Prtry[CBPR_RestrictedFINXMax35Text_Extended]", "Amt[ActiveOrHistoricCurrencyAndAmount]"], "TaxParty2__1": ["TaxId[CBPR_RestrictedFINXMax35Text_Extended]", "RegnId[CBPR_RestrictedFINXMax35Text_Extended]", "TaxTp[CBPR_RestrictedFINXMax35Text_Extended]", "Authstn-Titl[CBPR_RestrictedFINXMax35Text_Extended]", "Authstn-Nm[CBPR_RestrictedFINXMax140Text_Extended]"], "TaxPeriod2": ["Yr[ISODate]", "Tp", "FrToDt-FrDt[ISODate]", "FrToDt-ToDt[ISODate]"], "TrueFalseIndicator": []}, "bah": {"CBPR_DateTime": [], "CopyDuplicate1Code": [], "Max35Text": [], "Party44Choice__1": ["FIId-FinInstnId-BICFI", "FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "FIId-FinInstnId-ClrSysMmbId-MmbId", "FIId-FinInstnId-LEI"], "UnicodeChartsCode": []}}}