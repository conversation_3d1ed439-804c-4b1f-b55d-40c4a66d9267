import type {
  CustomRuleWithBaseKeysAndUsageGuidelineRules,
  RequiredDefinition,
} from '@helaba/iso20022-lib/rules';

// TODO: Encode the following rules + <PERSON><PERSON><PERSON><PERSON>weise using a new rule type, e.g. 'guideline' that allows us to add a note to a field, also as a subrule in a conditional rule.
// CBPR_Agent_National_only_TextualRule - "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "Whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code (ClrSysMmbId I guess?) only may be used." -> BICFI only required if not in same country. We can add an info popup for this but cannot enforce it as a rule.
// CBPR_Agent_Point_To_Point_On_SWIFT_TextualRule - "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "If the transaction is exchanged on the SWIFT network (i.e. if the sender and receiver of the message are on SWIFT), then BIC is mandatory and other elements are optional, e.g. LEI"
// CBPR_Local_Instrument_Guideline - "The preferred option is coded information."
// CBPR_Category_Purpose_Guideline - "The preferred option is coded information."

// Define what it means to require certain definitions.
// The script parsing the rules will use these to expand the rules. E.g. it is sufficient to specifiy "PstlAddr" in a "require" rule and the script will figure out which definition that corresponds to and expand the rule to include all required subfields of that definition.
// IMPORTANT: This does not denote exclusive ors. An "or" here means that the rule is fulfilled if at least one of the constituents is present.
// IMPORTANT: This denotes the minimal fields that are required to be present. There might be additional rules that add more nuance. E.g. there are numerous rules that describe the relationship of the fields in 'BranchAndFinancialInstitutionIdentification6__1' (see CBPR_Agent_Option_1_TextualRule, CBPR_Agent_Option_2_TextualRule, CBPR_Agent_Option_3_TextualRule, CBPR_Agent_Name_Postal_Address_FormalRule), but to express that the 'InstgRmbrsmntAgt' is required, it is sufficient to say, that either the 'BICFI' or the 'Nm' is required. The other rules will then ensure that the other fields are present if one of them is present.
export const sharedRequiredDefinitions: Record<string, RequiredDefinition> = {
  PostalAddress24__1: {
    or: ['AdrLine', ['TwnNm', 'Ctry']],
  },
  BranchAndFinancialInstitutionIdentification6__1: {
    or: ['FinInstnId-BICFI', 'FinInstnId-Nm'],
  },
  Charges7__1: {
    and: ['Amt-Ccy', 'Amt-amount', 'Agt'],
  },
};

// TODO: Find a way to leave out 'id', 'description', and 'descriptionTranslationProposal'
export const customDocumentRules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
  undefined,
  string | undefined
>[] = [
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr'],
    target: 'NbOfTxs',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: '1',
    isEqual: true,
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId'],
    usageGuidelineRules: ['CBPR_Agent_Name_Postal_Address_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId'],
    usageGuidelineRules: ['CBPR_Agent_Name_Postal_Address_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt'],
    usageGuidelineRules: ['CBPR_Instruction_for_Creditor_Agent1_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'CHQB',
        field: 'Cd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'HOLD',
        isEqual: false,
        target: 'Cd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt'],
    usageGuidelineRules: ['CBPR_Instruction_for_Creditor_Agent2_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'PHOB',
        field: 'Cd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'TELB',
        isEqual: false,
        target: 'Cd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['CBPR_Agent_Option_1_TextualRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        field: 'BICFI',
        type: 'present',
        value: true,
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'ClrSysMmbId',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'Nm',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: [
      'CBPR_Agent_Option_2_TextualRule',
      'CBPR_Agent_Option_3_TextualRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ClrSysMmbId',
      },
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'BICFI',
      },
    ],
  },
  {
    id: undefined, // This is also covered by CBPR_Agent_Name_Postal_Address_FormalRule.
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: [
      'CBPR_Agent_Option_2_TextualRule',
      'CBPR_Agent_Option_3_TextualRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined, // This is also covered by CBPR_Agent_Name_Postal_Address_FormalRule.
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: [
      'CBPR_Agent_Option_2_TextualRule',
      'CBPR_Agent_Option_3_TextualRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['CBPR_Agent_Name_Postal_Address_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['CBPR_Agent_Name_Postal_Address_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['CBPR_GracePeriod_Hybrid_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'AdrLine',
      },
      {
        type: 'group',
        conditions: [
          {
            type: 'present',
            value: true,
            field: 'Dept',
          },
          {
            type: 'present',
            value: true,
            field: 'SubDept',
          },
          {
            type: 'present',
            value: true,
            field: 'StrtNm',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNb',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNm',
          },
          {
            type: 'present',
            value: true,
            field: 'Flr',
          },
          {
            type: 'present',
            value: true,
            field: 'PstBx',
          },
          {
            type: 'present',
            value: true,
            field: 'Room',
          },
          {
            type: 'present',
            value: true,
            field: 'PstCd',
          },
          {
            type: 'present',
            value: true,
            field: 'TwnLctnNm',
          },
          {
            type: 'present',
            value: true,
            field: 'DstrctNm',
          },
          {
            type: 'present',
            value: true,
            field: 'CtrySubDvsn',
          },
        ],
        conditionsConnector: 'or',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'TwnNm',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'Ctry',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['CBPR_GracePeriod_Hybrid_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'AdrLine',
      },
      {
        type: 'group',
        conditions: [
          {
            type: 'present',
            value: true,
            field: 'Dept',
          },
          {
            type: 'present',
            value: true,
            field: 'SubDept',
          },
          {
            type: 'present',
            value: true,
            field: 'StrtNm',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNb',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNm',
          },
          {
            type: 'present',
            value: true,
            field: 'Flr',
          },
          {
            type: 'present',
            value: true,
            field: 'PstBx',
          },
          {
            type: 'present',
            value: true,
            field: 'Room',
          },
          {
            type: 'present',
            value: true,
            field: 'PstCd',
          },
          {
            type: 'present',
            value: true,
            field: 'TwnLctnNm',
          },
          {
            type: 'present',
            value: true,
            field: 'DstrctNm',
          },
          {
            type: 'present',
            value: true,
            field: 'CtrySubDvsn',
          },
        ],
        conditionsConnector: 'or',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxItems',
        value: 2,
        target: 'AdrLine',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['CBPR_Duplication_Postal_Address_TextualRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'contains',
    target: 'AdrLine',
    value: [
      'Dept',
      'SubDept',
      'StrtNm',
      'BldgNb',
      'BldgNm',
      'Flr',
      'PstBx',
      'Room',
      'PstCd',
      'TwnNm',
      'TwnLctnNm',
      'DstrctNm',
      'CtrySubDvsn',
      'Ctry',
    ],
    contains: false,
  },
  {
    id: undefined,
    baseKeys: [
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['CBPR_GracePeriod_Unstructured_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: false,
        field: 'Dept',
      },
      {
        type: 'present',
        value: false,
        field: 'SubDept',
      },
      {
        type: 'present',
        value: false,
        field: 'StrtNm',
      },
      {
        type: 'present',
        value: false,
        field: 'BldgNb',
      },
      {
        type: 'present',
        value: false,
        field: 'BldgNm',
      },
      {
        type: 'present',
        value: false,
        field: 'Flr',
      },
      {
        type: 'present',
        value: false,
        field: 'PstBx',
      },
      {
        type: 'present',
        value: false,
        field: 'Room',
      },
      {
        type: 'present',
        value: false,
        field: 'PstCd',
      },
      {
        type: 'present',
        value: false,
        field: 'TwnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'TwnLctnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'DstrctNm',
      },
      {
        type: 'present',
        value: false,
        field: 'CtrySubDvsn',
      },
      {
        type: 'present',
        value: false,
        field: 'Ctry',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxLength',
        value: 35,
        target: 'AdrLine',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['ThirdReimbursementAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'InstgRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['ThirdReimbursementAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'InstdRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined, // Note: "/Document/FIToFICstmrCdtTrf/GrpHdr/SttlmInf/ClrSys" was removed from the guidelines and is thus not included in this rule.
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'INDA',
        field: 'SttlmMtd',
      },
      {
        type: 'value',
        value: 'INGA',
        field: 'SttlmMtd',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'InstgRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'ThrdRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined, // Note: "/Document/FIToFICstmrCdtTrf/GrpHdr/SttlmInf/ClrSys" was removed from the guidelines and is thus not included in this rule.
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodCoverRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'SttlmAcct',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodCoverAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'InstgRmbrsmntAgt',
      },
    ],
    rulesConnector: 'or',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: [
      'CBPR_Related_Remit_Info_Remit_Info_Mutually_Exclusive_FormalRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'RltdRmtInf',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'RmtInf',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: [
      'CBPR_Related_Remit_Info_Remit_Info_Mutually_Exclusive_FormalRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'RmtInf',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'RltdRmtInf',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf'],
    usageGuidelineRules: ['CBPR_Remittance_Mutually_Exclusive_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Strd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'Ustrd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf'],
    usageGuidelineRules: ['CBPR_Remittance_Mutually_Exclusive_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Ustrd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        target: 'Strd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: ['CBPR_CRED_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'CRED',
        field: 'ChrgBr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'ChrgsInf',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: [
      'CBPR_Instruction_For_Creditor_Presence_Code_FormalRule',
    ],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'InstrForCdtrAgt-Cd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxItems',
        value: 1,
        target: 'InstrForCdtrAgt',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: ['CBPR_DEBT_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'DEBT',
        field: 'ChrgBr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxItems',
        value: 1,
        target: 'ChrgsInf',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId'],
    usageGuidelineRules: ['CBPR_Instruction_Identification_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'pattern',
    value: '^(?!/)(?!.*//)(?!.*/$).*$',
    target: 'InstrId',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt'],
    usageGuidelineRules: ['CBPR_Interbank_Settlement_Currency_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'XAU',
    isEqual: false,
    target: 'Ccy',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt'],
    usageGuidelineRules: ['CBPR_Interbank_Settlement_Currency_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'XAG',
    isEqual: false,
    target: 'Ccy',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt'],
    usageGuidelineRules: ['CBPR_Interbank_Settlement_Currency_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'XPD',
    isEqual: false,
    target: 'Ccy',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt'],
    usageGuidelineRules: ['CBPR_Interbank_Settlement_Currency_FormalRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: 'XPT',
    isEqual: false,
    target: 'Ccy',
  },
  {
    id: undefined,
    baseKeys: ['Document-FIToFICstmrCdtTrf-CdtTrfTxInf'],
    usageGuidelineRules: ['CBPR_DEBT_Rule_1_TextualRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'equal',
        value: true,
        field: 'IntrBkSttlmAmt-Ccy',
        otherField: 'InstdAmt-Ccy',
      },
      {
        type: 'value',
        value: 'DEBT',
        field: 'ChrgBr',
      },
      {
        type: 'greaterThan',
        field: 'IntrBkSttlmAmt-amount',
        otherField: 'InstdAmt-amount',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        target: 'ChrgsInf',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value',
        value: '0',
        isEqual: false,
        target: 'ChrgsInf-Amt-amount',
      },
    ],
    rulesConnector: 'and',
  },
];

export const customBAHRules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
  undefined,
  string | undefined
>[] = [];
