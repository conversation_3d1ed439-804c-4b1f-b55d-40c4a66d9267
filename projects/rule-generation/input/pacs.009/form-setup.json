{"description": "This file is not used for constructing the form, but instead is meant for planning composition of form components and division of fields into form pages. Nonetheless, this file is used during the 'generate-rules' script to check whether all fields from the JSON schema are represented in the form, pointing out all missing and extraneous fields. In addition, the individual form pages will throw an error during development to make sure that they are consistent with this setup. 'advanced' fields are automatically hidden when the toggle 'isAdvancedMode' is false in the advanced-mode-service.", "pages": {"essentials": {"document": {"basic": ["CdtTrfTxInf-Cdtr[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-CdtrAcct[CashAccount38__1]", "CdtTrfTxInf-CdtrAgt[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-CdtrAgtAcct[CashAccount38__1]", "CdtTrfTxInf-Dbtr[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-DbtrAcct[CashAccount38__1]", "CdtTrfTxInf-DbtrAgt[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-DbtrAgtAcct[CashAccount38__1]", "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "CdtTrfTxInf-InstrForCdtrAgt-Cd", "CdtTrfTxInf-InstrForCdtrAgt-InstrInf", "CdtTrfTxInf-InstrForNxtAgt-InstrInf", "CdtTrfTxInf-IntrBkSttlmAmt-amount", "CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "CdtTrfTxInf-IntrBkSttlmDt", "CdtTrfTxInf-PmtId-ClrSysRef", "CdtTrfTxInf-PmtId-EndToEndId", "CdtTrfTxInf-PmtId-InstrId", "CdtTrfTxInf-PmtId-TxId", "CdtTrfTxInf-PmtId-UETR", "CdtTrfTxInf-PmtTpInf-ClrChanl", "CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd", "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "CdtTrfTxInf-PmtTpInf-InstrPrty", "CdtTrfTxInf-PmtTpInf-LclInstrm-Cd", "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "CdtTrfTxInf-PmtTpInf-SvcLvl-Cd", "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "CdtTrfTxInf-Purp-Cd", "CdtTrfTxInf-Purp-Prtry", "CdtTrfTxInf-RmtInf-Ustrd", "CdtTrfTxInf-SttlmPrty", "CdtTrfTxInf-SttlmTmIndctn-CdtDtTm", "CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "CdtTrfTxInf-SttlmTmReq-CLSTm", "CdtTrfTxInf-SttlmTmReq-FrTm", "CdtTrfTxInf-SttlmTmReq-RjctTm", "CdtTrfTxInf-SttlmTmReq-TillTm", "GrpHdr-CreDtTm", "GrpHdr-MsgId", "GrpHdr-NbOfTxs", "GrpHdr-SttlmInf-SttlmAcct[CashAccount38__1]", "GrpHdr-SttlmInf-SttlmMtd"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "IntermediaryAgents": {"document": {"basic": ["CdtTrfTxInf-IntrmyAgt1[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt1Acct[CashAccount38__1]", "CdtTrfTxInf-IntrmyAgt2[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt2Acct[CashAccount38__1]", "CdtTrfTxInf-IntrmyAgt3[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-IntrmyAgt3Acct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt1[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt1Acct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt2[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt2Acct[CashAccount38__1]", "CdtTrfTxInf-PrvsInstgAgt3[BranchAndFinancialInstitutionIdentification6__1]", "CdtTrfTxInf-PrvsInstgAgt3Acct[CashAccount38__1]"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "SERVER": {"document": {"basic": [], "advanced": []}, "bah": {"basic": ["BizMsgIdr[Max35Text]", "BizSvc[Max35Text]", "CharSet[UnicodeChartsCode]", "CpyDplct[CopyDuplicate1Code]", "CreDt[CBPR_DateTime]", "Fr[Party44Choice__1]", "MktPrctc-Id", "MktPrctc-Regy", "MsgDefIdr[Max35Text]", "<PERSON><PERSON><PERSON>", "PssblDplct", "Rltd-BizMsgIdr[Max35Text]", "Rltd-BizSvc[Max35Text]", "Rltd-CharSet[UnicodeChartsCode]", "Rltd-CpyDplct[CopyDuplicate1Code]", "Rltd-CreDt[CBPR_DateTime]", "Rltd-Fr[Party44Choice__1]", "Rltd-MsgDefIdr[Max35Text]", "Rltd-Prty", "Rltd-To[Party44Choice__1]", "To[Party44Choice__1]"], "advanced": []}}}, "definitions": {"document": {"CountryCode": [], "CBPR_RestrictedFINXMax16Text_Extended": [], "CBPR_RestrictedFINXMax35Text_Extended": [], "CBPR_RestrictedFINXMax70Text_Extended": [], "CBPR_RestrictedFINXMax140Text_Extended": [], "PostalAddress24__1": ["Dept[CBPR_RestrictedFINXMax70Text_Extended]", "SubDept[CBPR_RestrictedFINXMax70Text_Extended]", "StrtNm[CBPR_RestrictedFINXMax70Text_Extended]", "BldgNb[CBPR_RestrictedFINXMax16Text_Extended]", "BldgNm[CBPR_RestrictedFINXMax35Text_Extended]", "Flr[CBPR_RestrictedFINXMax70Text_Extended]", "PstBx[CBPR_RestrictedFINXMax16Text_Extended]", "Room[CBPR_RestrictedFINXMax70Text_Extended]", "PstCd[CBPR_RestrictedFINXMax16Text_Extended]", "TwnNm[CBPR_RestrictedFINXMax35Text_Extended]", "TwnLctnNm[CBPR_RestrictedFINXMax35Text_Extended]", "DstrctNm[CBPR_RestrictedFINXMax35Text_Extended]", "CtrySubDvsn[CBPR_RestrictedFINXMax35Text_Extended]", "Ctry[CountryCode]", "AdrLine[CBPR_RestrictedFINXMax70Text_Extended]"], "ActiveOrHistoricCurrencyCode": [], "CBPR_RestrictedFINXMax35Text": [], "CashAccount38__1": ["Id-IBAN", "Id-<PERSON><PERSON>r-<PERSON>d", "Id-Othr-SchmeNm-Cd", "Id-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>[CBPR_RestrictedFINXMax35Text]", "Id-<PERSON><PERSON><PERSON>-<PERSON><PERSON>r[CBPR_RestrictedFINXMax35Text]", "Tp-Cd", "Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "Ccy[ActiveOrHistoricCurrencyCode]", "Nm", "Prxy-Tp-Cd", "Prxy-Tp-Prtry[CBPR_RestrictedFINXMax35Text]", "Prxy-Id"], "BranchAndFinancialInstitutionIdentification6__1": ["FinInstnId[FinancialInstitutionIdentification18__1]"], "BICFIDec2014Identifier": [], "LEIIdentifier": [], "ClearingSystemIdentification2Choice__1": ["Cd"], "ClearingSystemMemberIdentification2__1": ["ClrSysId[ClearingSystemIdentification2Choice__1]", "MmbId"], "FinancialInstitutionIdentification18__1": ["BICFI[BICFIDec2014Identifier]", "ClrSysMmbId[ClearingSystemMemberIdentification2__1]", "LEI[LEIIdentifier]", "Nm[CBPR_RestrictedFINXMax140Text_Extended]", "PstlAdr[PostalAddress24__1]"]}, "bah": {"CBPR_DateTime": [], "CopyDuplicate1Code": [], "Max35Text": [], "Party44Choice__1": ["FIId-FinInstnId-BICFI", "FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "FIId-FinInstnId-ClrSysMmbId-MmbId", "FIId-FinInstnId-LEI"], "UnicodeChartsCode": []}}}