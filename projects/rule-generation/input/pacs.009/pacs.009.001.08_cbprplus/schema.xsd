<?xml version="1.0" encoding="UTF-8"?>
<!--- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Legal Notices

SWIFT SCRL@2016. All rights reserved.

This schema is a component of MyStandards, the SWIFT collaborative Web application used to manage
standards definitions and industry usage.

This is a licensed product, which may only be used and distributed in accordance with MyStandards License
Terms as specified in MyStandards Service Description and the related Terms of Use.

Unless otherwise agreed in writing with SWIFT SCRL, the user has no right to:
 - authorise external end users to use this component for other purposes than their internal use.
 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.
 - re-sell or authorise another party e.g. software and service providers, to re-sell this component.

This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties
with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual 
property rights of whatever nature in this component will remain the exclusive property of SWIFT or its 
licensors.

Trademarks
SWIFT is the trade name of S.W.I.F.T. SCRL.
The following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.
Other product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

Group: Cross Border Payments and Reporting Plus (CBPR+)
Collection: CBPRPlus SR2025 (Combined)
Usage Guideline: CBPRPlus-pacs.009.001.08_FinancialInstitutionCreditTransfer
Base Message: pacs.009.001.08
Date of publication: 13 December 2024
URL: https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0rCoJpiEe6MIJTGjiktfA
Description: Principles:

1. AGENTS IDENTIFICATION - Textual Rules

-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.

-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.
Exception: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.

Note: "Instructing/ Instructed Agents" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.


2. Single transactions only are allowed.


3. Character Set:

All proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.

All Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:

  !#$&%*=^_’{|}~";<>@[\]

< is replaced with &lt;
> is replaced with &gt;


4. CBPR_Agent_PointToPointOnSWIFT:

If the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI


Generated by the MyStandards web platform [https://www.swift.com/mystandards] on 2025-02-28T20:10:06Z
-->
<!---->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.009.001.08" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.009.001.08">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AccountIdentification4Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the unique identification of an account as assigned by the account servicer.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="IBAN" type="IBAN2007Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IBAN</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 "Banking and related financial services - International Bank Account Number (IBAN)" version 1997-10-01, or later revisions.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Othr" type="GenericAccountIdentification1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of an account, as assigned by the account servicer, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AccountSchemeName1Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AccountSchemeName1Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalAccountIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ActiveCurrencyCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ActiveCurrencyCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 "Codes for the representation of currencies and funds".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ActiveOrHistoricCurrencyCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 "Codes for the representation of currencies and funds".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BICFIDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchAndFinancialInstitutionIdentification6__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution or a branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchAndFinancialInstitutionIdentification6__2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution or a branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CBPR_Amount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="14"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CBPR_Amount">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_Amount</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="CBPR_Amount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveCurrencyCode" use="required">
                    <xs:annotation>
                        <xs:documentation source="Name" xml:lang="EN">Currency</xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="CBPR_DateTime">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_DateTime</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). &#13;
&#13;
This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.&#13;
Note on the time format:&#13;
1) beginning / end of calendar day&#13;
00:00:00 = the beginning of a calendar day&#13;
24:00:00 = the end of a calendar day&#13;
2) fractions of second in time format&#13;
Decimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.&#13;
&#13;
Example: 2020-07-16T19:20:30.45+01:00</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:dateTime">
            <xs:pattern value=".*(\+|-)((0[0-9])|(1[0-4])):[0-5][0-9]"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax140Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax140Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax140Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax140Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax16Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax16Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax16Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax16Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax28Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax28Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="28"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax320Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax320Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]&#13;
</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="320"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax34Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax34Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash "/" at the beginning and end of line and double slash "//" within the line.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="([0-9a-zA-Z\-\?:\(\)\.,'\+ ]([0-9a-zA-Z\-\?:\(\)\.,'\+ ]*(/[0-9a-zA-Z\-\?:\(\)\.,'\+ ])?)*)"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax35Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax35Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax35Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax35Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax70Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax70Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax70Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax70Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_Time">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_Time</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time defined by a mandatory time component, expressed in local time with UTC offset format (hh:mm:ss.sss+/-hh:mm). &#13;
&#13;
Note on the time format:&#13;
1) beginning / end of calendar day&#13;
00:00:00 = the beginning of a calendar day&#13;
24:00:00 = the end of a calendar day&#13;
2) fractions of second in time format&#13;
Decimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.&#13;
&#13;
Example: 19:20:30.45+01:00</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:time">
            <xs:pattern value=".*(\+|-)((0[0-9])|(1[0-4])):[0-5][0-9]"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CashAccount38__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CashAccount38__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides the details to identify an account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="AccountIdentification4Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification for the account between the account owner and the account servicer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use of the account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Currency</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the currency in which the account is held. 

Usage: Currency should only be used in case one and the same account number covers several currencies
and the initiating party needs to identify which currency needs to be used for settlement on the account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CBPR_RestrictedFINXMax70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.

Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Prxy" type="ProxyAccountIdentification1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proxy</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies an alternate assumed name for the identification of the account. </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CashAccountType2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashAccountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Account type, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CategoryPurpose1Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CategoryPurpose1Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the instruction based on a set of pre-defined categories.
Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalCategoryPurpose1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, as published in an external category purpose code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ClearingChannel2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingChannel2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RTGS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RealTimeGrossSettlementSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a real-time gross settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RTNS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RealTimeNetSettlementSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a real-time net settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MPNS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MassPaymentNetSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a mass payment net settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BOOK">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BookTransfer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Payment through internal book transfer.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of a clearing system identifier.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a clearing system, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ClrSysId" type="ClearingSystemIdentification2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MmbId" type="CBPR_RestrictedFINXMax28Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a member of a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CountryCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CreditTransferTransaction36__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditTransferTransaction36__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provide further details specific to the individual transaction(s) included in the message.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="PmtId" type="PaymentIdentification7__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PaymentIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to reference a payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation28__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PaymentTypeInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to further specify the type of transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="IntrBkSttlmAmt" type="CBPR_Amount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InterbankSettlementAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money moved between the instructing agent and the instructed agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="IntrBkSttlmDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InterbankSettlementDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmPrty" type="Priority3Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementPriority</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmTmIndctn" type="SettlementDateTimeIndication1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementTimeIndication</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides information on the occurred settlement time(s) of the payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmTmReq" type="SettlementTimeRequest2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementTimeRequest</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides information on the requested settlement time(s) of the payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt1" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent1</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent immediately prior to the instructing agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt1Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent1Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt2" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent2</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent immediately prior to the instructing agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt2Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent2Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt3" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent3</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent immediately prior to the instructing agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt3Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreviousInstructingAgent3Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6__2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that instructs the next party in the chain to carry out the (set of) instruction(s).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6__2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt1" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent1</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent between the debtor's agent and the creditor's agent.

Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt1Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent1Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the intermediary agent 1 at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt2" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent2</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent between the debtor's agent and the creditor's agent.

Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt2Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent2Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the intermediary agent 2 at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt3" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent3</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent between the debtor's agent and the creditor's agent.

Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt3Acct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntermediaryAgent3Account</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the intermediary agent 3 at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Dbtr" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Debtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution that owes an amount of money to the (ultimate) financial institutional creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution servicing an account for the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgtAcct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution servicing an account for the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgtAcct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Cdtr" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Creditor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution that receives an amount of money from the financial institutional debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="2" minOccurs="0" name="InstrForCdtrAgt" type="InstructionForCreditorAgent2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionForCreditorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="6" minOccurs="0" name="InstrForNxtAgt" type="InstructionForNextAgent1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionForNextAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further information related to the processing of the payment instruction that may need to be acted upon by the next agent. 

Usage: The next agent may not be the creditor agent.
The instruction can relate to a level of service, can be an instruction that has to be executed by the agent, or can be information required by the next agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Purpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Underlying reason for the payment transaction.
Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittanceInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FICdtTrf" type="FinancialInstitutionCreditTransferV08"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ExternalAccountIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalAccountIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashAccountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalCashAccountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCategoryPurpose1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalCategoryPurpose1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the category purpose, as published in an external category purpose code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalClearingSystemIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing system identification code, as published in an external clearing system identification code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalLocalInstrument1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalLocalInstrument1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalProxyAccountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalProxyAccountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external proxy account type code, as published in the proxy account type external code set.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPurpose1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPurpose1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external purpose code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalServiceLevel1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalServiceLevel1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external service level code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FinancialInstitutionCreditTransferV08">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionCreditTransferV08</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Scope&#13;
The FinancialInstitutionCreditTransfer message is sent by a debtor financial institution to a creditor financial institution, directly or through other agents and/or a payment clearing and settlement system.&#13;
It is used to move funds from a debtor account to a creditor, where both debtor and creditor are financial institutions.&#13;
Usage&#13;
The FinancialInstitutionCreditTransfer message is exchanged between agents and can contain one or more credit transfer instructions where debtor and creditor are both financial institutions.&#13;
The FinancialInstitutionCreditTransfer message does not allow for grouping: a CreditTransferTransactionInformation block must be present for each credit transfer transaction.&#13;
The FinancialInstitutionCreditTransfer message can be used in domestic and cross-border scenarios.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader93__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GroupHeader</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CdtTrfTxInf" type="CreditTransferTransaction36__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditTransferTransactionInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements providing information specific to the individual credit transfer(s).</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification18__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details to identify a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BICFI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information used to identify a member within a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identifier of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CBPR_RestrictedFINXMax140Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which an agent is known and which is usually used to identify that agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostalAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18__2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification18__2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details to identify a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="BICFI" type="BICFIDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BICFI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information used to identify a member within a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identifier of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericAccountIdentification1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to a generic account identification.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="CBPR_RestrictedFINXMax34Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification assigned by an institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="AccountSchemeName1Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader93__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GroupHeader93__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the message.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="MsgId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message.
Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CreDtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="NbOfTxs" type="Max15NumericText_fixed">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">NumberOfTransactions</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number of individual transactions contained in the message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="SttlmInf" type="SettlementInstruction7__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">IBAN2007Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard "Banking and related financial services - International Bank Account Number (IBAN)" and replaced by the more recent edition of the standard.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ISODate</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="Instruction5Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Instruction5Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies further instructions concerning the processing of a payment instruction, provided by the sending clearing agent to the next agent(s).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PHOB">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PhoneBeneficiary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Please advise/contact (ultimate) creditor/claimant by phone.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TELB">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Telecom</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Please advise/contact (ultimate) creditor/claimant by the most efficient means of telecommunication.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="InstructionForCreditorAgent2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">InstructionForCreditorAgent2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Further information related to the processing of the payment instruction that may need to be acted upon by the creditor's agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the creditor's agent.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cd" type="Instruction5Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Coded information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor's agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrInf" type="CBPR_RestrictedFINXMax140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further information complementing the coded instruction or instruction to the creditor's agent that is bilaterally agreed or specific to a user community.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="InstructionForNextAgent1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">InstructionForNextAgent1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Further information related to the processing of the payment instruction that may need to be acted upon by an other agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the other agent.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrInf" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further information complementing the coded instruction or instruction to the next agent that is bilaterally agreed or specific to a user community.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="LEIIdentifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LEIIdentifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Legal Entity Identifier is a code allocated to a party as described in ISO 17442 "Financial Services - Legal Entity Identifier (LEI)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="LocalInstrument2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LocalInstrument2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements that further identifies the type of local instruments being requested by the initiating party.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalLocalInstrument1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the local instrument, as published in an external local instrument code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the local instrument, as a proprietary code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="Max15NumericText_fixed">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max15NumericText_fixed</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">1</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PaymentIdentification7__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentIdentification7__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further means of referencing a payment transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="InstrId" type="CBPR_RestrictedFINXMax16Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the instruction.

Usage: The instruction identification is a point to point reference that can be used between the instructing party and the instructed party to refer to the individual instruction. It can be included in several messages related to the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="EndToEndId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EndToEndIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the initiating party, to unambiguously identify the transaction. This identification is passed on, unchanged, throughout the entire end-to-end chain.

Usage: The end-to-end identification can be used for reconciliation or to link tasks relating to the transaction. It can be included in several messages related to the transaction.

Usage: In case there are technical limitations to pass on multiple references, the end-to-end identification must be passed on throughout the entire end-to-end chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TxId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransactionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the first instructing agent, to unambiguously identify the transaction that is passed on, unchanged, throughout the entire interbank chain. 
Usage: The transaction identification can be used for reconciliation, tracking or to link tasks relating to the transaction on the interbank level. 
Usage: The instructing agent has to make sure that the transaction identification is unique for a pre-agreed period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="UETR" type="UUIDv4Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">UETR</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Universally unique identifier to provide an end-to-end reference of a payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTypeInformation28__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentTypeInformation28__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details of the type of payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrPrty" type="Priority2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionPriority</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrChanl" type="ClearingChannel2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingChannel</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing channel to be used to process the payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="3" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ServiceLevel</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agreement under which or rules under which the transaction should be processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LocalInstrument</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">User community specific instrument.

Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CategoryPurpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the instruction based on a set of pre-defined categories.
Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PostalAddress24__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PostalAddress24__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Department</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SubDepartment</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a sub-division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StreetName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a street or thoroughfare.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number that identifies the position of a building on a street.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the building or house.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Floor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Floor or storey within a building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostBox</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Room</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Building room number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostCode</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a built-up area, with defined boundaries, and a local government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownLocationName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specific location name within the town.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DistrictName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision within a country sub-division.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountrySubDivision</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision of a country such as state, region, county.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Country</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Nation with its own government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="3" minOccurs="0" name="AdrLine" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AddressLine</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services, presented in free format text.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Priority2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Priority2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the priority level of an event.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGH">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">High</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is high.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NORM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Normal</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is normal.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Priority3Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Priority3Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the priority level of an event.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="URGT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Urgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is urgent (highest priority possible).</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HIGH">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">High</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is high.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NORM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Normal</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is normal.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ProxyAccountIdentification1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ProxyAccountIdentification1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to a proxy  identification of the account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ProxyAccountType1Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of the proxy identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Id" type="CBPR_RestrictedFINXMax320Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification used to indicate the account identification under another specified name.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProxyAccountType1Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ProxyAccountType1Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the scheme used for the identification of an account alias.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalProxyAccountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Purpose2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Purpose2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the underlying reason for the payment transaction.
Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalPurpose1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Underlying reason for the payment transaction, as published in an external purpose code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Purpose, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">RemittanceInformation2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching of an entry with the items that the transfer is intended to settle.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Ustrd" type="CBPR_RestrictedFINXMax140Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Unstructured</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, for example, commercial invoices in an accounts' receivable system in an unstructured form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ServiceLevel8Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ServiceLevel8Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the service level of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalServiceLevel1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a pre-agreed service or level of service between the parties, as a proprietary code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="SettlementDateTimeIndication1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementDateTimeIndication1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information on the occurred settlement time(s) of the payment transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtDtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebitDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SettlementInstruction7__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementInstruction7__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details on the settlement of the instruction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="SttlmMtd" type="SettlementMethod1Code__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementMethod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Method used to settle the (batch of) payment instructions.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmAcct" type="CashAccount38__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">A specific purpose account used to post debit and credit entries as a result of the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SettlementMethod1Code__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementMethod1Code__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the method used to settle the credit transfer instruction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done by the agent instructed to execute a payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INGA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SettlementTimeRequest2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementTimeRequest2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides information on the requested settlement time(s) of the payment instruction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="CLSTm" type="CBPR_Time">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CLSTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Time by which the amount of money must be credited, with confirmation, to the CLS Bank's account at the central bank.
Usage: Time must be expressed in Central European Time (CET).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TillTm" type="CBPR_Time">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TillTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Time until when the payment may be settled.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FrTm" type="CBPR_Time">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FromTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Time as from when the payment may be settled.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RjctTm" type="CBPR_Time">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RejectTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Time by when the payment must be settled to avoid rejection.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">UUIDv4Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 "Universally Unique IDentifier (UUID) URN Namespace".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>