import type {
  CustomRuleWithBaseKeysAndUsageGuidelineRules,
  RequiredDefinition,
} from '@helaba/iso20022-lib/rules';

// TODO: Encode the following rules + <PERSON><PERSON><PERSON><PERSON>weise using a new rule type, e.g. 'guideline' that allows us to add a note to a field, also as a subrule in a conditional rule.
// CBPR_Agent_National_only_TextualRule - "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "Whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code (ClrSysMmbId I guess?) only may be used." -> BICFI only required if not in same country. We can add an info popup for this but cannot enforce it as a rule.
// CBPR_Agent_Point_To_Point_On_SWIFT_TextualRule - "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "If the transaction is exchanged on the SWIFT network (i.e. if the sender and receiver of the message are on SWIFT), then BIC is mandatory and other elements are optional, e.g. LEI"
// CBPR_Local_Instrument_Guideline - "The preferred option is coded information."
// CBPR_Category_Purpose_Guideline - "The preferred option is coded information."

// Define what it means to require certain definitions.
// The script parsing the rules will use these to expand the rules. E.g. it is sufficient to specifiy "PstlAddr" in a "require" rule and the script will figure out which definition that corresponds to and expand the rule to include all required subfields of that definition.
// IMPORTANT: This does not denote exclusive ors. An "or" here means that the rule is fulfilled if at least one of the constituents is present.
// IMPORTANT: This denotes the minimal fields that are required to be present. There might be additional rules that add more nuance. E.g. there are numerous rules that describe the relationship of the fields in 'BranchAndFinancialInstitutionIdentification6__1' (see CBPR_Agent_Option_1_TextualRule, CBPR_Agent_Option_2_TextualRule, CBPR_Agent_Option_3_TextualRule, CBPR_Agent_Name_Postal_Address_FormalRule), but to express that the 'InstgRmbrsmntAgt' is required, it is sufficient to say, that either the 'BICFI' or the 'Nm' is required. The other rules will then ensure that the other fields are present if one of them is present.
export const sharedRequiredDefinitions: Record<string, RequiredDefinition> = {
};

// TODO: Find a way to leave out 'id', 'description', and 'descriptionTranslationProposal'
export const customDocumentRules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
  undefined,
  string | undefined
>[] = [
 
];

import { customBAHRules as pacs008Bah } from "../../pacs.008/pacs.008.001.08_cbprplus/custom_rules";

export const customBAHRules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
  undefined,
  string | undefined
>[] = pacs008Bah;