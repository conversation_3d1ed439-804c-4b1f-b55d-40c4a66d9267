{"$comment": {"legalNotices": "S.W.I.F.T. SC © 2025. All rights reserved.\nThis schema is a component of MyStandards, the Swift collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with S.W.I.F.T. SC, the user has no right to:\n- authorise external end users to use this component for other purposes than their internal use.\n- remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n- re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. Swift does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual\nproperty rights of whatever nature in this component will remain the exclusive property of Swift or its\nlicensors.\n\nTrademarks and Patents\nSwift is the trade name of S.W.I.F.T. SC.\nThe following are registered trademarks of Swift: 3SKey, Innotribe, MyStandards, Sibos, Swift, SwiftNet, Swift Institute, the Standards Forum logo, the Swiftlogo, Swift GPI with logo, the Swift GPI logo, and UETR.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.\n", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.002.001.10_FIToFIPaymentStatusReport", "baseMessage": "pacs.002.001.10", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0mxMJpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1. AGENTS IDENTIFICATION - Textual Rules:\r\n\r\n-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n\r\n2. The pacs.002 will provide the Status of 1 single transaction only. Its usage to provide notification of a rejected (negative) status is required by all agents, whereas the usage to provide a positive status will always be governed by a bilateral agreement between the agents.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema FIToFIPaymentStatusReportV10 (pacs.002.001.10) Generated by SWIFT MyStandards 2025-08-28 03:34:47", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.002.001.10"}, "fi_to_fi_payment_status_report_v10": {"$ref": "#/definitions/FIToFIPaymentStatusReportV10"}}, "definitions": {"AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1"}}, "required": ["financial_institution_identification"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "CBPR_RestrictedFINXMax105Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 105 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 105, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax16Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code"}}, "required": ["code"]}]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"clearing_system_identification": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1"}, "member_identification": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text"}}, "required": ["clearing_system_identification", "member_identification"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code)."}, "DateAndDateTime2Choice__1": {"type": "object", "description": "Choice between a date or a date and time format.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"date": {"description": "Specified date.", "$ref": "#/definitions/ISODate"}}, "required": ["date"]}, {"type": "object", "additionalProperties": false, "properties": {"date_time": {"description": "Specified date and time.", "$ref": "#/definitions/CBPR_DateTime"}}, "required": ["date_time"]}]}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"birth_date": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate"}, "province_of_birth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "city_of_birth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_of_birth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode"}}, "required": ["birth_date", "city_of_birth", "country_of_birth"]}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPaymentTransactionStatus1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the status of an individual payment instructions, as published in an external payment transaction status code set.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalStatusReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the status reason, as published in an external status reason code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "FIToFIPaymentStatusReportV10": {"type": "object", "description": "Scope\r\nThe FIToFIPaymentStatusReport message is sent by an instructed agent to the previous party in the payment chain. It is used to inform this party about the positive or negative status of an instruction (either single or file). It is also used to report on a pending instruction.\r\nUsage\r\nThe FIToFIPaymentStatusReport message is exchanged between agents to provide status information about instructions previously sent. Its usage will always be governed by a bilateral agreement between the agents.\r\nThe FIToFIPaymentStatusReport message can be used to provide information about the status (e.g. rejection, acceptance) of a credit transfer instruction, a direct debit instruction, as well as other intra-agent instructions (for example FIToFIPaymentCancellationRequest).\r\nThe FIToFIPaymentStatusReport message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.\r\nThe FIToFIPaymentStatusReport message can be used in domestic and cross-border scenarios.\r\nThe FIToFIPaymentStatusReport may also be sent to the receiver of the payment in a real time payment scenario, as both sides of the transactions must be informed of the status of the transaction (for example either the beneficiary is credited, or the transaction is rejected).", "additionalProperties": false, "properties": {"group_header": {"description": "Set of characteristics shared by all individual transactions included in the status report message.", "$ref": "#/definitions/GroupHeader91__1"}, "transaction_information_and_status": {"description": "Information concerning the original transactions, to which the status report message refers.", "$ref": "#/definitions/PaymentTransaction110__1"}}, "required": ["group_header", "transaction_information_and_status"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}}, "required": ["bicfi"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GroupHeader91__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"message_identification": {"description": "Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "creation_date_time": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime"}}, "required": ["message_identification", "creation_date_time"]}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$"}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}}}}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "OriginalGroupInformation29__1": {"type": "object", "description": "Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.", "additionalProperties": false, "properties": {"original_message_identification": {"description": "Point to point reference assigned by the original instructing party to unambiguously identify the original message.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_message_name_identification": {"description": "Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_creation_date_time": {"description": "Original date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime"}}, "required": ["original_message_identification", "original_message_name_identification"]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1"}}, "required": ["private_identification"]}]}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PaymentTransaction110__1": {"type": "object", "description": "Provides further details on the original transactions, to which the status report message refers.", "additionalProperties": false, "properties": {"original_group_information": {"description": "Point to point reference, as assigned by the original instructing party, to unambiguously identify the original message.", "$ref": "#/definitions/OriginalGroupInformation29__1"}, "original_instruction_identification": {"description": "Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text"}, "original_end_to_end_identification": {"description": "Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_transaction_identification": {"description": "Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "original_uetr": {"description": "Universally unique identifier to provide the original end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier"}, "transaction_status": {"description": "Specifies the status of a transaction, in a coded form.", "$ref": "#/definitions/ExternalPaymentTransactionStatus1Code"}, "status_reason_information": {"description": "Provides detailed information on the status reason.", "$ref": "#/definitions/StatusReasonInformation12__1"}, "effective_interbank_settlement_date": {"description": "Date and time at which a transaction is completed and cleared, that is, payment is effected.", "$ref": "#/definitions/DateAndDateTime2Choice__1"}, "clearing_system_reference": {"description": "Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "instructing_agent": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).  Usage: The instructing agent is the party sending the status message and not the party that sent the original instruction that is being reported on.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "instructed_agent": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).  Usage: The instructed agent is the party receiving the status message and not the party that received the original instruction that is being reported on.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}}, "required": ["original_group_information", "original_end_to_end_identification", "original_uetr", "transaction_status", "instructing_agent", "instructed_agent"]}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}}}}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}}}}, "StatusReason6Choice__1": {"type": "object", "description": "Specifies the reason for the status of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Reason for the status, as published in an external reason code list.", "$ref": "#/definitions/ExternalStatusReason1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Reason for the status, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "StatusReasonInformation12__1": {"type": "object", "description": "Provides information on the status reason of the transaction.", "additionalProperties": false, "properties": {"originator": {"description": "Party that issues the status.", "$ref": "#/definitions/PartyIdentification135__1"}, "reason": {"description": "Specifies the reason for the status report.", "$ref": "#/definitions/StatusReason6Choice__1"}, "additional_information": {"type": "array", "maxItems": 2, "description": "Further details on the status reason.  Usage: Additional information can be used for several purposes such as the reporting of repaired information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax105Text"}}}}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$"}}}