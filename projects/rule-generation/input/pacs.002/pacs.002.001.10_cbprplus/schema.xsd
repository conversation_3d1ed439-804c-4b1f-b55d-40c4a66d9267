<?xml version="1.0" encoding="UTF-8"?>
<!--- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Legal Notices

SWIFT SCRL@2016. All rights reserved.

This schema is a component of MyStandards, the SWIFT collaborative Web application used to manage
standards definitions and industry usage.

This is a licensed product, which may only be used and distributed in accordance with MyStandards License
Terms as specified in MyStandards Service Description and the related Terms of Use.

Unless otherwise agreed in writing with SWIFT SCRL, the user has no right to:
 - authorise external end users to use this component for other purposes than their internal use.
 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.
 - re-sell or authorise another party e.g. software and service providers, to re-sell this component.

This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties
with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual 
property rights of whatever nature in this component will remain the exclusive property of SWIFT or its 
licensors.

Trademarks
SWIFT is the trade name of S.W.I.F.T. SCRL.
The following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.
Other product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

Group: Cross Border Payments and Reporting Plus (CBPR+)
Collection: CBPRPlus SR2025 (Combined)
Usage Guideline: CBPRPlus-pacs.002.001.10_FIToFIPaymentStatusReport
Base Message: pacs.002.001.10
Date of publication: 13 December 2024
URL: https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0mxMJpiEe6MIJTGjiktfA
Description: Principles:

1. AGENTS IDENTIFICATION - Textual Rules:

-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.

-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.
Exception: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.

Note: "Instructing/ Instructed Agents" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.


2. The pacs.002 will provide the Status of 1 single transaction only. Its usage to provide notification of a rejected (negative) status is required by all agents, whereas the usage to provide a positive status will always be governed by a bilateral agreement between the agents.


3. Character Set:

All proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.

All Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:

  !#$&%*=^_’{|}~";<>@[\]

< is replaced with &lt;
> is replaced with &gt;


4. CBPR_Agent_PointToPointOnSWIFT:

If the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI

Generated by the MyStandards web platform [https://www.swift.com/mystandards] on 2025-03-02T10:01:02Z
-->
<!---->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.10" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.10">
    <xs:element name="Document" type="Document"/>
    <xs:simpleType name="AnyBICDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AnyBICDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BICFIDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchAndFinancialInstitutionIdentification6__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution or a branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CBPR_DateTime">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_DateTime</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). &#13;
&#13;
This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.&#13;
Note on the time format:&#13;
1) beginning / end of calendar day&#13;
00:00:00 = the beginning of a calendar day&#13;
24:00:00 = the end of a calendar day&#13;
2) fractions of second in time format&#13;
Decimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.&#13;
&#13;
Example: 2020-07-16T19:20:30.45+01:00</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:dateTime">
            <xs:pattern value=".*(\+|-)((0[0-9])|(1[0-4])):[0-5][0-9]"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax105Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax105Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 105 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="105"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax140Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax140Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax16Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax16Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax16Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax16Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax28Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax28Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="28"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax35Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax35Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax35Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax35Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CBPR_RestrictedFINXMax70Text_Extended">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CBPR_RestrictedFINXMax70Text_Extended</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&amp;*=^_`{|}~";&lt;&gt;@[\]</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ !#$%&amp;\*=^_`\{\|\}~&quot;;&lt;&gt;@\[\\\]]+"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of a clearing system identifier.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a clearing system, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification2__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ClrSysId" type="ClearingSystemIdentification2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MmbId" type="CBPR_RestrictedFINXMax28Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a member of a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CountryCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DateAndDateTime2Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DateAndDateTime2Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice between a date or a date and time format.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Dt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Date</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specified date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="DtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specified date and time.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DateAndPlaceOfBirth1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DateAndPlaceOfBirth1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Date and place of birth of a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="BirthDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BirthDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which a person is born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvcOfBirth" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ProvinceOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Province where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CityOfBirth" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CityOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">City where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CtryOfBirth" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountryOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Country where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FIToFIPmtStsRpt" type="FIToFIPaymentStatusReportV10"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalClearingSystemIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing system identification code, as published in an external clearing system identification code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalOrganisationIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPaymentTransactionStatus1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPaymentTransactionStatus1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the status of an individual payment instructions, as published in an external payment transaction status code set.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPersonIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalStatusReason1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalStatusReason1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the status reason, as published in an external status reason code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FIToFIPaymentStatusReportV10">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FIToFIPaymentStatusReportV10</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Scope&#13;
The FIToFIPaymentStatusReport message is sent by an instructed agent to the previous party in the payment chain. It is used to inform this party about the positive or negative status of an instruction (either single or file). It is also used to report on a pending instruction.&#13;
Usage&#13;
The FIToFIPaymentStatusReport message is exchanged between agents to provide status information about instructions previously sent. Its usage will always be governed by a bilateral agreement between the agents.&#13;
The FIToFIPaymentStatusReport message can be used to provide information about the status (e.g. rejection, acceptance) of a credit transfer instruction, a direct debit instruction, as well as other intra-agent instructions (for example FIToFIPaymentCancellationRequest).&#13;
The FIToFIPaymentStatusReport message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.&#13;
The FIToFIPaymentStatusReport message can be used in domestic and cross-border scenarios.&#13;
The FIToFIPaymentStatusReport may also be sent to the receiver of the payment in a real time payment scenario, as both sides of the transactions must be informed of the status of the transaction (for example either the beneficiary is credited, or the transaction is rejected).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader91__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GroupHeader</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the status report message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="TxInfAndSts" type="PaymentTransaction110__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransactionInformationAndStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information concerning the original transactions, to which the status report message refers.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification18__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details to identify a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="BICFI" type="BICFIDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BICFI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information used to identify a member within a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identifier of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericOrganisationIdentification1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification of an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification assigned by an institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification1__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericPersonIdentification1__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification of a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader91__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GroupHeader91__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the message.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="MsgId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message.
Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CreDtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ISODate">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ISODate</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="LEIIdentifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LEIIdentifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Legal Entity Identifier is a code allocated to a party as described in ISO 17442 "Financial Services - Legal Entity Identifier (LEI)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="OrganisationIdentification29__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OrganisationIdentification29__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AnyBIC" type="AnyBICDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AnyBIC</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Business identification code of the organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identification as an alternate identification for a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="2" minOccurs="0" name="Othr" type="GenericOrganisationIdentification1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of an organisation, as assigned by an institution, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OrganisationIdentificationSchemeName1Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the organisation identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalOrganisationIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="OriginalGroupInformation29__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OriginalGroupInformation29__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference assigned by the original instructing party to unambiguously identify the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="OrgnlMsgNmId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageNameIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="CBPR_DateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party38Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Party38Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="OrgId" type="OrganisationIdentification29__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OrganisationIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify an organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="PrvtId" type="PersonIdentification13__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PrivateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a person, for example a passport.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="PartyIdentification135__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PartyIdentification135__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the identification of a person or an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CBPR_RestrictedFINXMax140Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which a party is known and which is usually used to identify that party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostalAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party38Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtryOfRes" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountryOfResidence</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTransaction110__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentTransaction110__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details on the original transactions, to which the status report message refers.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="OrgnlGrpInf" type="OriginalGroupInformation29__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalGroupInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the original instructing party, to unambiguously identify the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlInstrId" type="CBPR_RestrictedFINXMax16Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalInstructionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="OrgnlEndToEndId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalEndToEndIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxId" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalTransactionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="OrgnlUETR" type="UUIDv4Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalUETR</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Universally unique identifier to provide the original end-to-end reference of a payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="TxSts" type="ExternalPaymentTransactionStatus1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransactionStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the status of a transaction, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="StsRsnInf" type="StatusReasonInformation12__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StatusReasonInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides detailed information on the status reason.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FctvIntrBkSttlmDt" type="DateAndDateTime2Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EffectiveInterbankSettlementDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which a transaction is completed and cleared, that is, payment is effected.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that instructs the next party in the chain to carry out the (set of) instruction(s).

Usage: The instructing agent is the party sending the status message and not the party that sent the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).

Usage: The instructed agent is the party receiving the status message and not the party that received the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentification13__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PersonIdentification13__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateAndPlaceOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and place of birth of a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="2" minOccurs="0" name="Othr" type="GenericPersonIdentification1__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of a person, as assigned by an institution, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PersonIdentificationSchemeName1Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalPersonIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="PostalAddress24__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PostalAddress24__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Department</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SubDepartment</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a sub-division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StreetName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a street or thoroughfare.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number that identifies the position of a building on a street.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the building or house.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Floor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Floor or storey within a building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostBox</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Room</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Building room number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="CBPR_RestrictedFINXMax16Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostCode</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a built-up area, with defined boundaries, and a local government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownLocationName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specific location name within the town.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DistrictName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision within a country sub-division.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="CBPR_RestrictedFINXMax35Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountrySubDivision</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision of a country such as state, region, county.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Country</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Nation with its own government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="3" minOccurs="0" name="AdrLine" type="CBPR_RestrictedFINXMax70Text_Extended">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AddressLine</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services, presented in free format text.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StatusReason6Choice__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">StatusReason6Choice__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the status of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalStatusReason1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the status, as published in an external reason code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="CBPR_RestrictedFINXMax35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the status, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="StatusReasonInformation12__1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">StatusReasonInformation12__1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides information on the status reason of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Orgtr" type="PartyIdentification135__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Originator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party that issues the status.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="StatusReason6Choice__1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the status report.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="2" minOccurs="0" name="AddtlInf" type="CBPR_RestrictedFINXMax105Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdditionalInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further details on the status reason.

Usage: Additional information can be used for several purposes such as the reporting of repaired information.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">UUIDv4Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 "Universally Unique IDentifier (UUID) URN Namespace".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>