{"description": "This file is not used for constructing the form, but instead is meant for planning composition of form components and division of fields into form pages. Nonetheless, this file is used during the 'generate-rules' script to check whether all fields from the JSON schema are represented in the form, pointing out all missing and extraneous fields. In addition, the individual form pages will throw an error during development to make sure that they are consistent with this setup. 'advanced' fields are automatically hidden when the toggle 'isAdvancedMode' is false in the advanced-mode-service.", "pages": {"essentials": {"document": {"basic": ["GrpHdr-CreDtTm", "GrpHdr-MsgId", "TxInfAndSts-ClrSysRef", "TxInfAndSts-FctvIntrBkSttlmDt-Dt", "TxInfAndSts-FctvIntrBkSttlmDt-DtTm", "TxInfAndSts-InstdAgt-FinInstnId-BICFI", "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "TxInfAndSts-InstdAgt-FinInstnId-LEI", "TxInfAndSts-InstgAgt-FinInstnId-BICFI", "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "TxInfAndSts-InstgAgt-FinInstnId-LEI", "TxInfAndSts-OrgnlEndToEndId", "TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm", "TxInfAndSts-OrgnlGrpInf-OrgnlMsgId", "TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId", "TxInfAndSts-OrgnlInstrId", "TxInfAndSts-OrgnlTxId", "TxInfAndSts-OrgnlUETR", "TxInfAndSts-StsRsnInf-AddtlInf", "TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd", "TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd", "TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "TxInfAndSts-StsRsnInf-Orgtr-Nm", "TxInfAndSts-StsRsnInf-Orgtr-PstlAdr[PostalAddress24__1]", "TxInfAndSts-StsRsnInf-Rsn-Cd", "TxInfAndSts-StsRsnInf-Rsn-Prtry", "TxInfAndSts-TxSts"], "advanced": []}, "bah": {"basic": [], "advanced": []}}, "SERVER": {"document": {"basic": [], "advanced": []}, "bah": {"basic": ["BizMsgIdr[Max35Text]", "BizSvc[Max35Text]", "CharSet[UnicodeChartsCode]", "CpyDplct[CopyDuplicate1Code]", "CreDt[CBPR_DateTime]", "Fr[Party44Choice__1]", "MktPrctc-Id", "MktPrctc-Regy", "MsgDefIdr[Max35Text]", "<PERSON><PERSON><PERSON>", "PssblDplct", "Rltd-BizMsgIdr[Max35Text]", "Rltd-BizSvc[Max35Text]", "Rltd-CharSet[UnicodeChartsCode]", "Rltd-CpyDplct[CopyDuplicate1Code]", "Rltd-CreDt[CBPR_DateTime]", "Rltd-Fr[Party44Choice__1]", "Rltd-MsgDefIdr[Max35Text]", "Rltd-Prty", "Rltd-To[Party44Choice__1]", "To[Party44Choice__1]"], "advanced": []}}}, "definitions": {"document": {"CountryCode": [], "CBPR_RestrictedFINXMax16Text_Extended": [], "CBPR_RestrictedFINXMax35Text_Extended": [], "CBPR_RestrictedFINXMax70Text_Extended": [], "PostalAddress24__1": ["Dept[CBPR_RestrictedFINXMax70Text_Extended]", "SubDept[CBPR_RestrictedFINXMax70Text_Extended]", "StrtNm[CBPR_RestrictedFINXMax70Text_Extended]", "BldgNb[CBPR_RestrictedFINXMax16Text_Extended]", "BldgNm[CBPR_RestrictedFINXMax35Text_Extended]", "Flr[CBPR_RestrictedFINXMax70Text_Extended]", "PstBx[CBPR_RestrictedFINXMax16Text_Extended]", "Room[CBPR_RestrictedFINXMax70Text_Extended]", "PstCd[CBPR_RestrictedFINXMax16Text_Extended]", "TwnNm[CBPR_RestrictedFINXMax35Text_Extended]", "TwnLctnNm[CBPR_RestrictedFINXMax35Text_Extended]", "DstrctNm[CBPR_RestrictedFINXMax35Text_Extended]", "CtrySubDvsn[CBPR_RestrictedFINXMax35Text_Extended]", "Ctry[CountryCode]", "AdrLine[CBPR_RestrictedFINXMax70Text_Extended]"]}, "bah": {"CBPR_DateTime": [], "CopyDuplicate1Code": [], "Max35Text": [], "Party44Choice__1": ["FIId-FinInstnId-BICFI", "FIId-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "FIId-FinInstnId-ClrSysMmbId-MmbId", "FIId-FinInstnId-LEI"], "UnicodeChartsCode": []}}}