{"fullName": "Document", "abbrName": "Document", "nestedAbbrName": "Document", "isArray": false, "children": [{"fullName": "FIToFIPaymentStatusReportV10", "abbrName": "FIToFIPmtStsRpt", "nestedAbbrName": "Document-FIToFIPmtStsRpt", "isArray": false, "children": [{"fullName": "GroupHeader", "abbrName": "GrpHdr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-GrpHdr", "isArray": false, "children": [{"fullName": "MessageIdentification", "abbrName": "MsgId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-GrpHdr-MsgId", "isArray": false, "children": []}, {"fullName": "CreationDateTime", "abbrName": "CreDtTm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm", "isArray": false, "children": []}]}, {"fullName": "TransactionInformationAndStatus", "abbrName": "TxInfAndSts", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts", "isArray": false, "children": [{"fullName": "OriginalGroupInformation", "abbrName": "OrgnlGrpInf", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf", "isArray": false, "children": [{"fullName": "OriginalMessageIdentification", "abbrName": "OrgnlMsgId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId", "isArray": false, "children": []}, {"fullName": "OriginalMessageNameIdentification", "abbrName": "OrgnlMsgNmId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId", "isArray": false, "children": []}, {"fullName": "OriginalCreationDateTime", "abbrName": "OrgnlCreDtTm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm", "isArray": false, "children": []}]}, {"fullName": "OriginalInstructionIdentification", "abbrName": "OrgnlInstrId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId", "isArray": false, "children": []}, {"fullName": "OriginalEndToEndIdentification", "abbrName": "OrgnlEndToEndId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId", "isArray": false, "children": []}, {"fullName": "OriginalTransactionIdentification", "abbrName": "OrgnlTxId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId", "isArray": false, "children": []}, {"fullName": "OriginalUETR", "abbrName": "OrgnlUETR", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR", "isArray": false, "children": []}, {"fullName": "TransactionStatus", "abbrName": "TxSts", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts", "isArray": false, "children": []}, {"fullName": "StatusReasonInformation", "abbrName": "StsRsnInf", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf", "isArray": false, "children": [{"fullName": "Originator", "abbrName": "Orgtr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "Reason", "abbrName": "Rsn", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry", "isArray": false, "children": []}]}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf", "isArray": true, "children": []}]}, {"fullName": "EffectiveInterbankSettlementDate", "abbrName": "FctvIntrBkSttlmDt", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt", "isArray": false, "children": [{"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt", "isArray": false, "children": []}, {"fullName": "DateTime", "abbrName": "DtTm", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm", "isArray": false, "children": []}]}, {"fullName": "ClearingSystemReference", "abbrName": "ClrSysRef", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef", "isArray": false, "children": []}, {"fullName": "InstructingAgent", "abbrName": "InstgAgt", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}, {"fullName": "InstructedAgent", "abbrName": "InstdAgt", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}]}]}]}