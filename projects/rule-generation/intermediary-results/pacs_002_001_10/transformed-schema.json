{"$comment": {"legalNotices": "S.W.I.F.T. SC © 2025. All rights reserved.\nThis schema is a component of MyStandards, the Swift collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with S.W.I.F.T. SC, the user has no right to:\n- authorise external end users to use this component for other purposes than their internal use.\n- remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n- re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. Swift does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual\nproperty rights of whatever nature in this component will remain the exclusive property of Swift or its\nlicensors.\n\nTrademarks and Patents\nSwift is the trade name of S.W.I.F.T. SC.\nThe following are registered trademarks of Swift: 3SKey, Innotribe, MyStandards, Sibos, Swift, SwiftNet, Swift Institute, the Standards Forum logo, the Swiftlogo, Swift GPI with logo, the Swift GPI logo, and UETR.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.\n", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.002.001.10_FIToFIPaymentStatusReport", "baseMessage": "pacs.002.001.10", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0mxMJpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1. AGENTS IDENTIFICATION - Textual Rules:\r\n\r\n-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n\r\n2. The pacs.002 will provide the Status of 1 single transaction only. Its usage to provide notification of a rejected (negative) status is required by all agents, whereas the usage to provide a positive status will always be governed by a bilateral agreement between the agents.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema FIToFIPaymentStatusReportV10 (pacs.002.001.10) Generated by SWIFT MyStandards 2025-08-28 03:34:47", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.002.001.10"}, "FIToFIPmtStsRpt": {"$ref": "#/definitions/FIToFIPaymentStatusReportV10", "name": "FIToFIPaymentStatusReportV10", "nestedFieldNames": ["Document-FIToFIPmtStsRpt"]}}, "required": ["FIToFIPmtStsRpt"], "definitions": {"FIToFIPaymentStatusReportV10": {"type": "object", "description": "Scope\r\nThe FIToFIPaymentStatusReport message is sent by an instructed agent to the previous party in the payment chain. It is used to inform this party about the positive or negative status of an instruction (either single or file). It is also used to report on a pending instruction.\r\nUsage\r\nThe FIToFIPaymentStatusReport message is exchanged between agents to provide status information about instructions previously sent. Its usage will always be governed by a bilateral agreement between the agents.\r\nThe FIToFIPaymentStatusReport message can be used to provide information about the status (e.g. rejection, acceptance) of a credit transfer instruction, a direct debit instruction, as well as other intra-agent instructions (for example FIToFIPaymentCancellationRequest).\r\nThe FIToFIPaymentStatusReport message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.\r\nThe FIToFIPaymentStatusReport message can be used in domestic and cross-border scenarios.\r\nThe FIToFIPaymentStatusReport may also be sent to the receiver of the payment in a real time payment scenario, as both sides of the transactions must be informed of the status of the transaction (for example either the beneficiary is credited, or the transaction is rejected).", "additionalProperties": false, "properties": {"GrpHdr": {"description": "Set of characteristics shared by all individual transactions included in the status report message.", "$ref": "#/definitions/GroupHeader91__1", "name": "GroupHeader", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-GrpHdr"]}, "TxInfAndSts": {"description": "Information concerning the original transactions, to which the status report message refers.", "$ref": "#/definitions/PaymentTransaction110__1", "name": "TransactionInformationAndStatus", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts"]}}, "required": ["GrpHdr", "TxInfAndSts"]}, "GroupHeader91__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"MsgId": {"description": "Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "MessageIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-GrpHdr-MsgId"]}, "CreDtTm": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "CreationDateTime", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm"]}}, "required": ["MsgId", "CreDtTm"]}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-GrpHdr-MsgId", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry", "Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-GrpHdr-CreDtTm", "Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm", "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm"]}, "PaymentTransaction110__1": {"type": "object", "description": "Provides further details on the original transactions, to which the status report message refers.", "additionalProperties": false, "properties": {"OrgnlGrpInf": {"description": "Point to point reference, as assigned by the original instructing party, to unambiguously identify the original message.", "$ref": "#/definitions/OriginalGroupInformation29__1", "name": "OriginalGroupInformation", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf"]}, "OrgnlInstrId": {"description": "Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text", "name": "OriginalInstructionIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId"]}, "OrgnlEndToEndId": {"description": "Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalEndToEndIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlEndToEndId"]}, "OrgnlTxId": {"description": "Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalTransactionIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlTxId"]}, "OrgnlUETR": {"description": "Universally unique identifier to provide the original end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier", "name": "OriginalUETR", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR"]}, "TxSts": {"description": "Specifies the status of a transaction, in a coded form.", "$ref": "#/definitions/ExternalPaymentTransactionStatus1Code", "name": "TransactionStatus", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts"]}, "StsRsnInf": {"description": "Provides detailed information on the status reason.", "$ref": "#/definitions/StatusReasonInformation12__1", "name": "StatusReasonInformation", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf"]}, "FctvIntrBkSttlmDt": {"description": "Date and time at which a transaction is completed and cleared, that is, payment is effected.", "$ref": "#/definitions/DateAndDateTime2Choice__1", "name": "EffectiveInterbankSettlementDate", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt"]}, "ClrSysRef": {"description": "Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "ClearingSystemReference", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-ClrSysRef"]}, "InstgAgt": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).  Usage: The instructing agent is the party sending the status message and not the party that sent the original instruction that is being reported on.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructingAgent", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt"]}, "InstdAgt": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).  Usage: The instructed agent is the party receiving the status message and not the party that received the original instruction that is being reported on.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructedAgent", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt"]}}, "required": ["OrgnlGrpInf", "OrgnlEndToEndId", "OrgnlUETR", "TxSts", "InstgAgt", "InstdAgt"]}, "OriginalGroupInformation29__1": {"type": "object", "description": "Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.", "additionalProperties": false, "properties": {"OrgnlMsgId": {"description": "Point to point reference assigned by the original instructing party to unambiguously identify the original message.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalMessageIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgId"]}, "OrgnlMsgNmId": {"description": "Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalMessageNameIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlMsgNmId"]}, "OrgnlCreDtTm": {"description": "Original date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "OriginalCreationDateTime", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlGrpInf-OrgnlCreDtTm"]}}, "required": ["OrgnlMsgId", "OrgnlMsgNmId"]}, "CBPR_RestrictedFINXMax16Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlInstrId"]}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-OrgnlUETR"]}, "ExternalPaymentTransactionStatus1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the status of an individual payment instructions, as published in an external payment transaction status code set.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-TxSts"]}, "StatusReasonInformation12__1": {"type": "object", "description": "Provides information on the status reason of the transaction.", "additionalProperties": false, "properties": {"Orgtr": {"description": "Party that issues the status.", "$ref": "#/definitions/PartyIdentification135__1", "name": "Originator", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr"]}, "Rsn": {"description": "Specifies the reason for the status report.", "$ref": "#/definitions/StatusReason6Choice__1", "name": "Reason", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn"]}, "AddtlInf": {"type": "array", "maxItems": 2, "description": "Further details on the status reason.  Usage: Additional information can be used for several purposes such as the reporting of repaired information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax105Text"}, "name": "AdditionalInformation", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf"]}}}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1", "name": "Identification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes"]}}}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Nm"]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine"]}}}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Dept", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-SubDept", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-StrtNm", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Flr", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Room", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-AdrLine"]}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNb", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstBx", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-PstCd"]}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-BldgNm", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnNm", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-TwnLctnNm", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-DstrctNm", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-CtrySubDvsn", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-PstlAdr-Ctry", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-CtryOfRes"]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1", "name": "PrivateIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr"]}}}, "AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-AnyBIC"]}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd"]}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr"]}}}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"BirthDt": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate", "name": "BirthDate", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"]}, "PrvcOfBirth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "ProvinceOfBirth", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"]}, "CityOfBirth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CityOfBirth", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CtryOfBirth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode", "name": "CountryOfBirth", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"]}}, "required": ["BirthDt", "CityOfBirth", "CtryOfBirth"]}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd"]}, "StatusReason6Choice__1": {"type": "object", "description": "Specifies the reason for the status of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Reason for the status, as published in an external reason code list.", "$ref": "#/definitions/ExternalStatusReason1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Reason for the status, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalStatusReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the status reason, as published in an external status reason code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-Rsn-Cd"]}, "CBPR_RestrictedFINXMax105Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 105 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 105, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-StsRsnInf-AddtlInf"]}, "DateAndDateTime2Choice__1": {"type": "object", "description": "Choice between a date or a date and time format.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Dt": {"description": "Specified date.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-Dt"]}}, "required": ["Dt"]}, {"type": "object", "additionalProperties": false, "properties": {"DtTm": {"description": "Specified date and time.", "$ref": "#/definitions/CBPR_DateTime", "name": "DateTime", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-FctvIntrBkSttlmDt-DtTm"]}}, "required": ["DtTm"]}]}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-LEI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-LEI"]}}, "required": ["BICFI"]}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-BICFI", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-BICFI"]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["ClrSysId", "MmbId"]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFIPmtStsRpt-TxInfAndSts-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFIPmtStsRpt-TxInfAndSts-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"]}}}