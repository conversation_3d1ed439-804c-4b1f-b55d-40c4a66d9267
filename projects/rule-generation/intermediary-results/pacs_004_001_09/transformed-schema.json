{"$comment": {"legalNotices": "SWIFT SCRL@2023. All rights reserved.\n\nThis schema is a component of MyStandards, the SWIFT collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with SWIFT SCRL, the user has no right to:\n - authorise external end users to use this component for other purposes than their internal use.\n - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n - re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual \nproperty rights of whatever nature in this component will remain the exclusive property of SWIFT or its \nlicensors.\n\nTrademarks\nSWIFT is the trade name of S.W.I.F.T. SCRL.\nThe following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.004.001.09_PaymentReturn", "baseMessage": "pacs.004.001.09", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0y-cJpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1A. AGENTS IDENTIFICATION - Textual Rules:\r\n\r\n-> If BIC<PERSON> is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n1B. DEBTOR/CREDITOR - PARTY IDENTIFICATION - Textual Rules:\r\n\r\n-> If AnyBIC is present, then (Name and Postal Address) is NOT allowed (other elements remain optional) - However, in case of conflicting information, AnyBIC will always take precedence.\r\n\r\n-> If Name is present, it is recommended to use Postal Address.\r\n\r\n\r\n2. The pacs.004 is also used for a return of pacs.009 COV following the serial flow. Single transactions only are allowed.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema PaymentReturnV09 (pacs.004.001.09) Generated by SWIFT MyStandards 2025-04-23 08:11:38", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.004.001.09"}, "PmtRtr": {"$ref": "#/definitions/PaymentReturnV09", "name": "PaymentReturnV09", "nestedFieldNames": ["Document-PmtRtr"]}}, "required": ["PmtRtr"], "definitions": {"PaymentReturnV09": {"type": "object", "description": "Scope\r\nThe PaymentReturn message is sent by an agent to the previous agent in the payment chain to undo a payment previously settled.\r\nUsage\r\nThe PaymentReturn message is exchanged between agents to return funds after settlement of credit transfer instructions (that is FIToFICustomerCreditTransfer message and FinancialInstitutionCreditTransfer message) or direct debit instructions (FIToFICustomerDirectDebit message).\r\nThe PaymentReturn message should not be used between agents and non-financial institution customers. Non-financial institution customers will be informed about a debit or a credit on their account(s) through a BankToCustomerDebitCreditNotification message ('notification') and/or BankToCustomerAccountReport/BankToCustomerStatement message ('statement').\r\nThe PaymentReturn message can be used to return single instructions or multiple instructions from one or different files.\r\nThe PaymentReturn message can be used in domestic and cross-border scenarios.\r\nThe PaymentReturn message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.", "additionalProperties": false, "properties": {"GrpHdr": {"description": "Set of characteristics shared by all individual transactions included in the message.", "$ref": "#/definitions/GroupHeader90__1", "name": "GroupHeader", "nestedFieldNames": ["Document-PmtRtr-GrpHdr"]}, "TxInf": {"description": "Information concerning the original transactions, to which the return message refers.", "$ref": "#/definitions/PaymentTransaction112__1", "name": "TransactionInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf"]}}, "required": ["GrpHdr", "TxInf"]}, "GroupHeader90__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"MsgId": {"description": "Point to point reference, as assigned by the instructing party and sent to the next party in the chain, to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "MessageIdentification", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-MsgId"]}, "CreDtTm": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "CreationDateTime", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-CreDtTm"]}, "NbOfTxs": {"description": "Number of individual transactions contained in the message.", "$ref": "#/definitions/Max15NumericText_fixed", "name": "NumberOfTransactions", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-NbOfTxs"]}, "SttlmInf": {"description": "Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.", "$ref": "#/definitions/SettlementInstruction7__1", "name": "SettlementInformation", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf"]}}, "required": ["MsgId", "CreDtTm", "NbOfTxs", "SttlmInf"]}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-MsgId", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-RtrId", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgId", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgNmId", "Document-PmtRtr-TxInf-OrgnlInstrId", "Document-PmtRtr-TxInf-OrgnlEndToEndId", "Document-PmtRtr-TxInf-OrgnlTxId", "Document-PmtRtr-TxInf-OrgnlClrSysRef", "Document-PmtRtr-TxInf-ClrSysRef", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-BrnchId-Id", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-BrnchId-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Prtry"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-CreDtTm", "Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlCreDtTm", "Document-PmtRtr-TxInf-SttlmTmIndctn-DbtDtTm", "Document-PmtRtr-TxInf-SttlmTmIndctn-CdtDtTm", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-DtTm"]}, "Max15NumericText_fixed": {"type": "string", "description": "\n*`1`-null", "enum": ["1"], "nestedFieldNames": ["Document-PmtRtr-GrpHdr-NbOfTxs"]}, "SettlementInstruction7__1": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"SttlmMtd": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__1", "name": "SettlementMethod", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmMtd"]}, "SttlmAcct": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "SettlementAccount", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct"]}}, "required": ["SttlmMtd"]}, "SettlementMethod1Code__1": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.", "enum": ["INDA", "INGA"], "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmMtd"]}, "CashAccount38__1": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id"]}, "Tp": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp"]}, "Ccy": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Ccy"]}, "Nm": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Nm"]}, "Prxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1", "name": "Proxy", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy"]}}, "required": ["Id"]}, "AccountIdentification4Choice__1": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"IBAN": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier", "name": "IBAN", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-IBAN"]}}, "required": ["IBAN"]}, {"type": "object", "additionalProperties": false, "properties": {"Othr": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1__1", "name": "Other", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "IBAN2007Identifier": {"type": "string", "description": "The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard \"Banking and related financial services - International Bank Account Number (IBAN)\" and replaced by the more recent edition of the standard.", "pattern": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-IBAN"]}, "GenericAccountIdentification1__1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax34Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Issr"]}}, "required": ["Id"]}, "CBPR_RestrictedFINXMax34Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash \"/\" at the beginning and end of line and double slash \"//\" within the line.", "minLength": 1, "maxLength": 34, "pattern": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-Id"]}, "AccountSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalAccountIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Id-Othr-SchmeNm-Cd"]}, "CashAccountType2Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCashAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Tp-Cd"]}, "ActiveOrHistoricCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-CcyOfTrf", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Ccy"]}, "CBPR_RestrictedFINXMax70Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Nm"]}, "ProxyAccountIdentification1__1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"Tp": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp"]}, "Id": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/CBPR_RestrictedFINXMax320Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Id"]}}, "required": ["Id"]}, "ProxyAccountType1Choice__1": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalProxyAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external proxy account type code, as published in the proxy account type external code set.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Tp-Cd"]}, "CBPR_RestrictedFINXMax320Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]\r\n", "minLength": 1, "maxLength": 320, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct-Prxy-Id"]}, "PaymentTransaction112__1": {"type": "object", "description": "Provides further details on the reference and status on the original transactions, included in the original instruction, to which the return message applies.", "additionalProperties": false, "properties": {"RtrId": {"description": "Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the returned transaction. Usage: The instructing party is the party sending the return message and not the party that sent the original instruction that is being returned.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "ReturnIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrId"]}, "OrgnlGrpInf": {"description": "Provides information on the original message.", "$ref": "#/definitions/OriginalGroupInformation29__1", "name": "OriginalGroupInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlGrpInf"]}, "OrgnlInstrId": {"description": "Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalInstructionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlInstrId"]}, "OrgnlEndToEndId": {"description": "Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalEndToEndIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlEndToEndId"]}, "OrgnlTxId": {"description": "Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalTransactionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxId"]}, "OrgnlUETR": {"description": "Universally unique identifier to provide the original end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier", "name": "OriginalUETR", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlUETR"]}, "OrgnlClrSysRef": {"description": "Unique reference, as assigned by the original clearing system, to unambiguously identify the original instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalClearingSystemReference", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlClrSysRef"]}, "OrgnlIntrBkSttlmAmt": {"description": "Amount of money moved between the instructing agent and the instructed agent, as provided in the original instruction.", "$ref": "#/definitions/CBPR_Amount__1", "name": "OriginalInterbankSettlementAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt"]}, "OrgnlIntrBkSttlmDt": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.  Usage: the OriginalInterbankSettlementDate is the interbank settlement date of the original instruction return message, and not of the return message.", "$ref": "#/definitions/ISODate", "name": "OriginalInterbankSettlementDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmDt"]}, "RtrdIntrBkSttlmAmt": {"description": "Amount of money to be moved between the instructing agent and the instructed agent in the returned instruction.", "$ref": "#/definitions/CBPR_Amount__1", "name": "ReturnedInterbankSettlementAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt"]}, "IntrBkSttlmDt": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.  Usage: the InterbankSettlementDate is the interbank settlement date of the return message, and not of the original instruction.", "$ref": "#/definitions/ISODate", "name": "InterbankSettlementDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-IntrBkSttlmDt"]}, "SttlmPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.   Usage: the SettlementPriority is the settlement priority of the return message, and not of the original instruction.", "$ref": "#/definitions/Priority3Code", "name": "SettlementPriority", "nestedFieldNames": ["Document-PmtRtr-TxInf-SttlmPrty"]}, "SttlmTmIndctn": {"description": "Provides information on the occurred settlement time(s) of the payment transaction.", "$ref": "#/definitions/SettlementDateTimeIndication1__1", "name": "SettlementTimeIndication", "nestedFieldNames": ["Document-PmtRtr-TxInf-SttlmTmIndctn"]}, "RtrdInstdAmt": {"description": "Amount of money to be moved between the debtor and the creditor, before deduction of charges, in the returned transaction. Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1", "name": "ReturnedInstructedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrdInstdAmt"]}, "XchgRate": {"description": "Factor used to convert an amount from one currency into another. This reflects the price at which one currency was bought with another currency.", "$ref": "#/definitions/BaseOneRate", "name": "ExchangeRate", "nestedFieldNames": ["Document-PmtRtr-TxInf-XchgRate"]}, "ChrgBr": {"description": "Specifies which party/parties will bear the charges associated with the processing of the payment transaction.  Usage: The ChargeBearer applies to the return message, not to the original instruction.", "$ref": "#/definitions/ChargeBearerType1Code__1", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgBr"]}, "ChrgsInf": {"type": "array", "description": "Provides information on the charges to be paid by the charge bearer(s) related to the processing of the return transaction.", "items": {"$ref": "#/definitions/Charges7__1"}, "name": "ChargesInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf"]}, "ClrSysRef": {"description": "Unique reference, as assigned by the clearing system, to unambiguously identify the return instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "ClearingSystemReference", "nestedFieldNames": ["Document-PmtRtr-TxInf-ClrSysRef"]}, "InstgAgt": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).  Usage: The instructing agent is the party sending the return message and not the party that sent the original instruction that is being returned.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructingAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstgAgt"]}, "InstdAgt": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).  Usage: The instructed agent is the party receiving the return message and not the party that received the original instruction that is being returned.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructedAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstdAgt"]}, "RtrChain": {"description": "Provides all parties (agents and non-agents) involved in a return transaction.", "$ref": "#/definitions/TransactionParties7__1", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain"]}, "RtrRsnInf": {"description": "Provides detailed information on the return reason.", "$ref": "#/definitions/PaymentReturnReason6__1", "name": "ReturnReasonInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf"]}, "OrgnlTxRef": {"description": "Key elements used to identify the original transaction that is being referred to.", "$ref": "#/definitions/OriginalTransactionReference28__1", "name": "OriginalTransactionReference", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef"]}}, "required": ["OrgnlEndToEndId", "OrgnlUETR", "RtrdIntrBkSttlmAmt", "IntrBkSttlmDt", "ChrgBr", "InstgAgt", "InstdAgt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RtrRsnInf"]}, "OriginalGroupInformation29__1": {"type": "object", "description": "Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.", "additionalProperties": false, "properties": {"OrgnlMsgId": {"description": "Point to point reference assigned by the original instructing party to unambiguously identify the original message.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalMessageIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgId"]}, "OrgnlMsgNmId": {"description": "Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "OriginalMessageNameIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlMsgNmId"]}, "OrgnlCreDtTm": {"description": "Original date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "OriginalCreationDateTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlGrpInf-OrgnlCreDtTm"]}}, "required": ["OrgnlMsgId", "OrgnlMsgNmId"]}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlUETR"]}, "CBPR_Amount__1": {"type": "object", "additionalProperties": false, "properties": {"Ccy": {"$ref": "#/definitions/ActiveCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-RtrdInstdAmt-Ccy", "Document-PmtRtr-TxInf-ChrgsInf-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt-Ccy"]}, "amount": {"type": "string", "maxLength": 15, "pattern": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$", "name": "amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt-amount", "Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt-amount", "Document-PmtRtr-TxInf-RtrdInstdAmt-amount", "Document-PmtRtr-TxInf-ChrgsInf-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt-amount"]}}, "required": ["Ccy", "amount"]}, "ActiveCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-RtrdIntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-RtrdInstdAmt-Ccy", "Document-PmtRtr-TxInf-ChrgsInf-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt-Ccy"]}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlIntrBkSttlmDt", "Document-PmtRtr-TxInf-IntrBkSttlmDt", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmDt", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdColltnDt", "Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-Dt", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-DtOfSgntr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFnlColltnDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FrstColltnDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FnlColltnDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-RltdDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Dt", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt"]}, "Priority3Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`URGT`-Priority level is urgent (highest priority possible).\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["URGT", "HIGH", "NORM"], "nestedFieldNames": ["Document-PmtRtr-TxInf-SttlmPrty"]}, "SettlementDateTimeIndication1__1": {"type": "object", "description": "Information on the occurred settlement time(s) of the payment transaction.", "additionalProperties": false, "properties": {"DbtDtTm": {"description": "Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "DebitDateTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-SttlmTmIndctn-DbtDtTm"]}, "CdtDtTm": {"description": "Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "CreditDateTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-SttlmTmIndctn-CdtDtTm"]}}}, "BaseOneRate": {"type": "string", "description": "Rate expressed as a decimal, for example, 0.7 is 7/10 and 70%.", "maxLength": 12, "nestedFieldNames": ["Document-PmtRtr-TxInf-XchgRate"]}, "ChargeBearerType1Code__1": {"type": "string", "description": "Specifies which party(ies) will pay charges due for processing of the instruction.\n*`CRED`-All transaction charges are to be borne by the creditor.\n*`SHAR`-In a credit transfer context, means that transaction charges on the sender side are to be borne by the debtor, transaction charges on the receiver side are to be borne by the creditor. In a direct debit context, means that transaction charges on the sender side are to be borne by the creditor, transaction charges on the receiver side are to be borne by the debtor.", "enum": ["CRED", "SHAR"], "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgBr"]}, "Charges7__1": {"type": "object", "description": "Provides information on the charges related to the payment transaction.", "additionalProperties": false, "properties": {"Amt": {"description": "Transaction charges to be paid by the charge bearer.", "$ref": "#/definitions/CBPR_Amount__1", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Amt"]}, "Agt": {"description": "Agent that takes the transaction charges or to which the transaction charges are due.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Agent", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt"]}}, "required": ["Amt", "Agt"]}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr"]}}}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-BICFI"]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["ClrSysId", "MmbId"]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId-MmbId"]}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-InstgAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-LEI"]}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Ustrd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-RefNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RefNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-AddtlRmtInf", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Nm"]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine"]}}}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-AdrLine"]}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstCd"]}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Nb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Desc", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Ref", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-RegnId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxTp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-AdmstnZone", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Mtd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Ctgy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CertId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).", "nestedFieldNames": ["Document-PmtRtr-TxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-CtryOfRes"]}, "BranchAndFinancialInstitutionIdentification6__2": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__2", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstgAgt-FinInstnId", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__2": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstgAgt-FinInstnId-BICFI", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstgAgt-FinInstnId-ClrSysMmbId", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-InstgAgt-FinInstnId-LEI", "Document-PmtRtr-TxInf-InstdAgt-FinInstnId-LEI"]}}, "required": ["BICFI"]}, "TransactionParties7__1": {"type": "object", "description": "Provides further details on the parties specific to the individual transaction.", "additionalProperties": false, "properties": {"UltmtDbtr": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__1", "name": "UltimateDebtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr"]}, "Dbtr": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__2", "name": "Debtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr"]}, "InitgPty": {"description": "Party that initiates the payment. Usage: This can be either the debtor or a party that initiates the credit transfer on behalf of the debtor.", "$ref": "#/definitions/Party40Choice__1", "name": "Initiating<PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-InitgPty"]}, "DbtrAgt": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "DebtorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-DbtrAgt"]}, "PrvsInstgAgt1": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent1", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt1"]}, "PrvsInstgAgt2": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent2", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt2"]}, "PrvsInstgAgt3": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent3", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-PrvsInstgAgt3"]}, "IntrmyAgt1": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent1", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-IntrmyAgt1"]}, "IntrmyAgt2": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent2", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-IntrmyAgt2"]}, "IntrmyAgt3": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent3", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-IntrmyAgt3"]}, "CdtrAgt": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3", "name": "CreditorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-CdtrAgt"]}, "Cdtr": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__3", "name": "Creditor", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr"]}, "UltmtCdtr": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__1", "name": "UltimateCreditor", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtCdtr"]}}, "required": ["Dbtr", "Cdtr"]}, "Party40Choice__1": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": false, "properties": {"Pty": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__1", "name": "Party", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty"]}}, "required": ["Pty"]}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Nm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-CtryOfRes"]}}}, "PostalAddress24__2": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 2, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-PstlAdr-AdrLine"]}}, "required": ["TwnNm", "Ctry"]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1", "name": "PrivateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr"]}}}, "AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-AnyBIC"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-OrgId-Othr-SchmeNm-Cd"]}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr"]}}}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"BirthDt": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate", "name": "BirthDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-BirthDt"]}, "PrvcOfBirth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "ProvinceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"]}, "CityOfBirth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CityOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CtryOfBirth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode", "name": "CountryOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"]}}, "required": ["BirthDt", "CityOfBirth", "CtryOfBirth"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-InitgPty-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrChain-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd"]}, "Party40Choice__2": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Pty": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__2", "name": "Party", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty"]}}, "required": ["Pty"]}, {"type": "object", "additionalProperties": false, "properties": {"Agt": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Agent", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Agt"]}}, "required": ["Agt"]}]}, "PartyIdentification135__2": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__2", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-CtryOfRes"]}}}, "Party38Choice__2": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__2", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__2", "name": "PrivateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__2": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__2"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr"]}}}, "GenericOrganisationIdentification1__2": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__2", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-Issr"]}}, "required": ["Id", "SchmeNm"]}, "OrganisationIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, "PersonIdentification13__2": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__2"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr"]}}}, "GenericPersonIdentification1__2": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__2", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-Issr"]}}, "required": ["Id", "SchmeNm"]}, "PersonIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, "BranchAndFinancialInstitutionIdentification6__3": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-CdtrAgt-FinInstnId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-FinInstnId"]}, "BrnchId": {"description": "Identifies a specific branch of a financial institution.  Usage: This component should be used in case the identification information in the financial institution component does not provide identification up to branch level.", "$ref": "#/definitions/BranchData3__1", "name": "BranchIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-CdtrAgt-BrnchId", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-BrnchId"]}}, "required": ["FinInstnId"]}, "BranchData3__1": {"type": "object", "description": "Information that locates and identifies a specific branch of a financial institution.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a branch of a financial institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-CdtrAgt-BrnchId-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt-BrnchId-Id"]}}}, "Party40Choice__3": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Pty": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__3", "name": "Party", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty"]}}, "required": ["Pty"]}, {"type": "object", "additionalProperties": false, "properties": {"Agt": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Agent", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Agt"]}}, "required": ["Agt"]}]}, "PartyIdentification135__3": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Nm", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-PstlAdr", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-Id", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrChain-Cdtr-Pty-CtryOfRes", "Document-PmtRtr-TxInf-RtrRsnInf-Orgtr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty-CtryOfRes"]}}}, "PaymentReturnReason6__1": {"type": "object", "description": "Provides further details on the reason of the return of the transaction.", "additionalProperties": false, "properties": {"Orgtr": {"description": "Party that issues the return.", "$ref": "#/definitions/PartyIdentification135__3", "name": "Originator", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-Orgtr"]}, "Rsn": {"description": "Specifies the reason for the return.", "$ref": "#/definitions/ReturnReason5Choice__1", "name": "Reason", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-Rsn"]}, "AddtlInf": {"type": "array", "maxItems": 2, "description": "Further details on the return reason.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax105Text"}, "name": "AdditionalInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-AddtlInf"]}}, "required": ["Rsn"]}, "ReturnReason5Choice__1": {"type": "object", "description": "Specifies the reason for the return of the transaction.", "additionalProperties": false, "properties": {"Cd": {"description": "Reason for the return, as published in an external reason code list.", "$ref": "#/definitions/ExternalReturnReason1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-Rsn-Cd"]}}, "required": ["Cd"]}, "ExternalReturnReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the return reason, as published in an external return reason code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-Rsn-Cd"]}, "CBPR_RestrictedFINXMax105Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 105 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 105, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-RtrRsnInf-AddtlInf"]}, "OriginalTransactionReference28__1": {"type": "object", "description": "Key elements used to refer the original transaction.", "additionalProperties": false, "properties": {"IntrBkSttlmAmt": {"description": "Amount of money moved between the instructing agent and the instructed agent.", "$ref": "#/definitions/CBPR_Amount__1", "name": "InterbankSettlementAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmAmt"]}, "Amt": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.", "$ref": "#/definitions/AmountType4Choice__1", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Amt"]}, "IntrBkSttlmDt": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.", "$ref": "#/definitions/ISODate", "name": "InterbankSettlementDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-IntrBkSttlmDt"]}, "ReqdColltnDt": {"description": "Date and time at which the creditor requests that the amount of money is to be collected from the debtor.", "$ref": "#/definitions/ISODate", "name": "RequestedCollectionDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-ReqdColltnDt"]}, "ReqdExctnDt": {"description": "Date at which the initiating party requests the clearing agent to process the payment.  Usage: This is the date on which the debtor's account is to be debited. If payment by cheque, the date when the cheque must be generated by the bank.", "$ref": "#/definitions/DateAndDateTime2Choice__1", "name": "RequestedExecutionDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt"]}, "CdtrSchmeId": {"description": "Credit party that signs the mandate.", "$ref": "#/definitions/PartyIdentification135__4", "name": "CreditorSchemeIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId"]}, "SttlmInf": {"description": "Specifies the details on how the settlement of the original transaction(s) between the instructing agent and the instructed agent was completed.", "$ref": "#/definitions/SettlementInstruction7__2", "name": "SettlementInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf"]}, "PmtTpInf": {"description": "Set of elements used to further specify the type of transaction.", "$ref": "#/definitions/PaymentTypeInformation27__1", "name": "PaymentTypeInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf"]}, "PmtMtd": {"description": "Specifies the means of payment that will be used to move the amount of money.", "$ref": "#/definitions/PaymentMethod4Code", "name": "PaymentMethod", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtMtd"]}, "MndtRltdInf": {"description": "Provides further details of the mandate signed between the creditor and the debtor.", "$ref": "#/definitions/MandateRelatedInformation14__1", "name": "MandateRelatedInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf"]}, "RmtInf": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.", "$ref": "#/definitions/RemittanceInformation16__1", "name": "RemittanceInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf"]}, "UltmtDbtr": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__1", "name": "UltimateDebtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-UltmtDbtr"]}, "Dbtr": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/Party40Choice__4", "name": "Debtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr"]}, "DbtrAcct": {"description": "Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAcct"]}, "DbtrAgt": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__7", "name": "DebtorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt"]}, "DbtrAgtAcct": {"description": "Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgtAcct"]}, "CdtrAgt": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3", "name": "CreditorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgt"]}, "CdtrAgtAcct": {"description": "Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAgtAcct"]}, "Cdtr": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__5", "name": "Creditor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr"]}, "CdtrAcct": {"description": "Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrAcct"]}, "UltmtCdtr": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/Party40Choice__1", "name": "UltimateCreditor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-UltmtCdtr"]}, "Purp": {"description": "Underlying reason for the payment transaction.  Usage:  Purpose is used by the end customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/Purpose2Choice__1", "name": "Purpose", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Purp"]}}}, "AmountType4Choice__1": {"type": "object", "description": "Specifies the amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"InstdAmt": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.  Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1", "name": "InstructedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Amt-InstdAmt"]}}, "required": ["InstdAmt"]}, {"type": "object", "additionalProperties": false, "properties": {"EqvtAmt": {"description": "Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.", "$ref": "#/definitions/EquivalentAmount2__1", "name": "EquivalentAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt"]}}, "required": ["EqvtAmt"]}]}, "EquivalentAmount2__1": {"type": "object", "description": "Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.", "additionalProperties": false, "properties": {"Amt": {"description": "Amount of money to be moved between debtor and creditor, before deduction of charges, expressed in the currency of the debtor's account, and to be moved in a different currency. Usage: The first agent will convert the equivalent amount into the amount to be moved.", "$ref": "#/definitions/CBPR_Amount__1", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-Amt"]}, "CcyOfTrf": {"description": "Specifies the currency of the to be transferred amount, which is different from the currency of the debtor's account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "CurrencyOfTransfer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Amt-EqvtAmt-CcyOfTrf"]}}, "required": ["Amt", "CcyOfTrf"]}, "DateAndDateTime2Choice__1": {"type": "object", "description": "Choice between a date or a date and time format.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Dt": {"description": "Specified date.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-Dt"]}}, "required": ["Dt"]}, {"type": "object", "additionalProperties": false, "properties": {"DtTm": {"description": "Specified date and time.", "$ref": "#/definitions/CBPR_DateTime", "name": "DateTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-ReqdExctnDt-DtTm"]}}, "required": ["DtTm"]}]}, "PartyIdentification135__4": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/Max140Text", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-CtryOfRes"]}}}, "Max140Text": {"type": "string", "description": "Specifies a character string with a maximum length of 140 characters.", "minLength": 1, "maxLength": 140, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-Nm"]}, "PostalAddress24__3": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/Max70Text", "name": "Department", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/Max70Text", "name": "SubDepartment", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/Max70Text", "name": "StreetName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/Max16Text", "name": "BuildingNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/Max35Text", "name": "BuildingName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/Max70Text", "name": "Floor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/Max16Text", "name": "PostBox", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/Max70Text", "name": "Room", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/Max16Text", "name": "PostCode", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/Max35Text", "name": "TownName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/Max35Text", "name": "TownLocationName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/Max35Text", "name": "DistrictName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/Max35Text", "name": "CountrySubDivision", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Ctry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/Max70Text"}, "name": "AddressLine", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-AdrLine"]}}}, "Max70Text": {"type": "string", "description": "Specifies a character string with a maximum length of 70characters.", "minLength": 1, "maxLength": 70, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Dept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Flr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-Room", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Prtry"]}, "Max16Text": {"type": "string", "description": "Specifies a character string with a maximum length of 16 characters.", "minLength": 1, "maxLength": 16, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-PstCd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-PstCd"]}, "Max35Text": {"type": "string", "description": "Specifies a character string with a maximum length of 35 characters.", "minLength": 1, "maxLength": 35, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-MndtId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlMndtId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry"]}, "Party38Choice": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13", "name": "PrivateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr"]}}}, "GenericOrganisationIdentification1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/Max35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "PersonIdentification13": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr"]}}}, "DateAndPlaceOfBirth1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"BirthDt": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate", "name": "BirthDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"]}, "PrvcOfBirth": {"description": "Province where a person was born.", "$ref": "#/definitions/Max35Text", "name": "ProvinceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"]}, "CityOfBirth": {"description": "City where a person was born.", "$ref": "#/definitions/Max35Text", "name": "CityOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CtryOfBirth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode", "name": "CountryOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"]}}, "required": ["BirthDt", "CityOfBirth", "CtryOfBirth"]}, "GenericPersonIdentification1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/Max35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-CdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "SettlementInstruction7__2": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"SttlmMtd": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__2", "name": "SettlementMethod", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmMtd"]}, "SttlmAcct": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__2", "name": "SettlementAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct"]}, "InstgRmbrsmntAgt": {"description": "Agent through which the instructing agent will reimburse the instructed agent.  Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructingReimbursementAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgt"]}, "InstgRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the instructing reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2", "name": "InstructingReimbursementAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct"]}, "InstdRmbrsmntAgt": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If InstructedReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch. Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructedReimbursementAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgt"]}, "InstdRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the instructed reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2", "name": "InstructedReimbursementAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct"]}, "ThrdRmbrsmntAgt": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If ThirdReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "ThirdReimbursementAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgt"]}, "ThrdRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the third reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__2", "name": "ThirdReimbursementAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct"]}}, "required": ["SttlmMtd"]}, "SettlementMethod1Code__2": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.\n*`COVE`-Settlement is done through a cover payment.", "enum": ["INDA", "INGA", "COVE"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmMtd"]}, "CashAccount38__2": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Id"]}, "Tp": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Tp"]}, "Ccy": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy"]}, "Nm": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Nm"]}, "Prxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1", "name": "Proxy", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-SttlmAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstgRmbrsmntAgtAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-InstdRmbrsmntAgtAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy"]}}, "required": ["Id"]}, "PaymentTypeInformation27__1": {"type": "object", "description": "Provides further details of the type of payment.", "additionalProperties": false, "properties": {"InstrPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.", "$ref": "#/definitions/Priority2Code", "name": "InstructionPriority", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-InstrPrty"]}, "ClrChanl": {"description": "Specifies the clearing channel to be used to process the payment instruction.", "$ref": "#/definitions/ClearingChannel2Code", "name": "ClearingChannel", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-ClrChanl"]}, "SvcLvl": {"type": "array", "maxItems": 3, "description": "Agreement under which or rules under which the transaction should be processed.", "items": {"$ref": "#/definitions/ServiceLevel8Choice__1"}, "name": "ServiceLevel", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl"]}, "LclInstrm": {"description": "User community specific instrument.  Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.", "$ref": "#/definitions/LocalInstrument2Choice__1", "name": "LocalInstrument", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm"]}, "SeqTp": {"description": "Identifies the direct debit sequence, such as first, recurrent, final or one-off.", "$ref": "#/definitions/SequenceType3Code", "name": "SequenceType", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SeqTp"]}, "CtgyPurp": {"description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories. Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/CategoryPurpose1Choice__1", "name": "CategoryPurpose", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp"]}}}, "Priority2Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["HIGH", "NORM"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-InstrPrty"]}, "ClearingChannel2Code": {"type": "string", "description": "Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.\n*`RTGS`-Clearing channel is a real-time gross settlement system.\n*`RTNS`-Clearing channel is a real-time net settlement system.\n*`MPNS`-Clearing channel is a mass payment net settlement system.\n*`BOOK`-Payment through internal book transfer.", "enum": ["RTGS", "RTNS", "MPNS", "BOOK"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-ClrChanl"]}, "ServiceLevel8Choice__1": {"type": "object", "description": "Specifies the service level of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.", "$ref": "#/definitions/ExternalServiceLevel1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies a pre-agreed service or level of service between the parties, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalServiceLevel1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external service level code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SvcLvl-Cd"]}, "LocalInstrument2Choice__1": {"type": "object", "description": "Set of elements that further identifies the type of local instruments being requested by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the local instrument, as published in an external local instrument code list.", "$ref": "#/definitions/ExternalLocalInstrument1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the local instrument, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalLocalInstrument1Code": {"type": "string", "minLength": 1, "maxLength": 35, "description": "Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-LclInstrm-Cd"]}, "SequenceType3Code": {"type": "string", "description": "Specifies the type of the current transaction that belongs to a sequence of transactions.\n*`FRST`-First collection of a series of direct debit instructions.\n*`RCUR`-Direct debit instruction where the debtor's authorisation is used for regular direct debit transactions initiated by the creditor.\n*`FNAL`-Final collection of a series of direct debit instructions.\n*`OOFF`-Direct debit instruction where the debtor's authorisation is used to initiate one single direct debit transaction.\n*`RPRE`-Collection used to re-present previously reversed or returned direct debit transactions.", "enum": ["FRST", "RCUR", "FNAL", "OOFF", "RPRE"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-SeqTp"]}, "CategoryPurpose1Choice__1": {"type": "object", "description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories.\nUsage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Category purpose, as published in an external category purpose code list.", "$ref": "#/definitions/ExternalCategoryPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Category purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCategoryPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the category purpose, as published in an external category purpose code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtTpInf-CtgyPurp-Cd"]}, "PaymentMethod4Code": {"type": "string", "description": "Specifies the transfer method that will be used to transfer an amount of money.\n*`CHK`-Written order to a bank to pay a certain amount of money from one person to another person.\n*`TRF`-Transfer of an amount of money in the books of the account servicer.\n*`DD`-Collection of an amount of money from the debtor's bank account by the creditor. The amount of money and dates of collections may vary.\n*`TRA`-Transfer of an amount of money in the books of the account servicer. An advice should be sent back to the account owner.", "enum": ["CHK", "TRF", "DD", "TRA"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-PmtMtd"]}, "MandateRelatedInformation14__1": {"type": "object", "description": "Provides further details related to a direct debit mandate signed between the creditor and the debtor.", "additionalProperties": false, "properties": {"MndtId": {"description": "Unique identification, as assigned by the creditor, to unambiguously identify the mandate.", "$ref": "#/definitions/Max35Text", "name": "MandateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-MndtId"]}, "DtOfSgntr": {"description": "Date on which the direct debit mandate has been signed by the debtor.", "$ref": "#/definitions/ISODate", "name": "DateOfSignature", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-DtOfSgntr"]}, "AmdmntInd": {"description": "Indicator notifying whether the underlying mandate is amended or not.", "$ref": "#/definitions/TrueFalseIndicator", "name": "AmendmentIndicator", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInd"]}, "AmdmntInfDtls": {"description": "List of mandate elements that have been modified.", "$ref": "#/definitions/AmendmentInformationDetails13__1", "name": "AmendmentInformationDetails", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls"]}, "ElctrncSgntr": {"description": "Additional security provisions, such as a digital signature, as provided by the debtor.", "$ref": "#/definitions/Max1025Text", "name": "ElectronicSignature", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-ElctrncSgntr"]}, "FrstColltnDt": {"description": "Date of the first collection of a direct debit as per the mandate.", "$ref": "#/definitions/ISODate", "name": "FirstCollectionDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FrstColltnDt"]}, "FnlColltnDt": {"description": "Date of the final collection of a direct debit as per the mandate.", "$ref": "#/definitions/ISODate", "name": "FinalCollectionDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-FnlColltnDt"]}, "Frqcy": {"description": "Regularity with which direct debit instructions are to be created and processed.", "$ref": "#/definitions/Frequency36Choice", "name": "Frequency", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy"]}, "Rsn": {"description": "Reason for the direct debit mandate to allow the user to distinguish between different mandates for the same creditor.", "$ref": "#/definitions/MandateSetupReason1Choice", "name": "Reason", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn"]}, "TrckgDays": {"description": "Specifies the number of days the direct debit instruction must be tracked.", "$ref": "#/definitions/Exact2NumericText", "name": "TrackingDays", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-TrckgDays"]}}}, "TrueFalseIndicator": {"type": "boolean", "description": "A flag indicating a True or False value.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd"]}, "AmendmentInformationDetails13__1": {"type": "object", "description": "Provides further details on the list of direct debit mandate elements that have been modified when the amendment indicator has been set.", "additionalProperties": false, "properties": {"OrgnlMndtId": {"description": "Unique identification, as assigned by the creditor, to unambiguously identify the original mandate.", "$ref": "#/definitions/Max35Text", "name": "OriginalMandateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlMndtId"]}, "OrgnlCdtrSchmeId": {"description": "Original creditor scheme identification that has been modified.", "$ref": "#/definitions/PartyIdentification135__4", "name": "OriginalCreditorSchemeIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrSchmeId"]}, "OrgnlCdtrAgt": {"description": "Original creditor agent that has been modified.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__4", "name": "OriginalCreditorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt"]}, "OrgnlCdtrAgtAcct": {"description": "Original creditor agent account that has been modified.", "$ref": "#/definitions/CashAccount38", "name": "OriginalCreditorAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct"]}, "OrgnlDbtr": {"description": "Original debtor that has been modified.", "$ref": "#/definitions/PartyIdentification135__4", "name": "OriginalDebtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtr"]}, "OrgnlDbtrAcct": {"description": "Original debtor account that has been modified.", "$ref": "#/definitions/CashAccount38", "name": "OriginalDebtorAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct"]}, "OrgnlDbtrAgt": {"description": "Original debtor agent that has been modified.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__5", "name": "OriginalDebtorAgent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt"]}, "OrgnlDbtrAgtAcct": {"description": "Original debtor agent account that has been modified.", "$ref": "#/definitions/CashAccount38", "name": "OriginalDebtorAgentAccount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct"]}, "OrgnlFnlColltnDt": {"description": "Original final collection date that has been modified.", "$ref": "#/definitions/ISODate", "name": "OriginalFinalCollectionDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFnlColltnDt"]}, "OrgnlFrqcy": {"description": "Original frequency that has been modified.", "$ref": "#/definitions/Frequency36Choice", "name": "OriginalFrequency", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy"]}, "OrgnlRsn": {"description": "Original reason for the mandate to allow the user to distinguish between different mandates for the same creditor.", "$ref": "#/definitions/MandateSetupReason1Choice", "name": "OriginalReason", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn"]}, "OrgnlTrckgDays": {"description": "Original number of tracking days that has been modified.", "$ref": "#/definitions/Exact2NumericText", "name": "OriginalTrackingDays", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlTrckgDays"]}}}, "BranchAndFinancialInstitutionIdentification6__4": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__3", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__3": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/Max140Text", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgt-FinInstnId-PstlAdr"]}}}, "CashAccount38": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id"]}, "Tp": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp"]}, "Ccy": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Ccy"]}, "Nm": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/Max70Text", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Nm"]}, "Prxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1", "name": "Proxy", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy"]}}, "required": ["Id"]}, "AccountIdentification4Choice": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"IBAN": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier", "name": "IBAN", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-IBAN", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-IBAN"]}}, "required": ["IBAN"]}, {"type": "object", "additionalProperties": false, "properties": {"Othr": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1", "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "GenericAccountIdentification1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/Max34Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/Max35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Issr"]}}, "required": ["Id"]}, "Max34Text": {"type": "string", "description": "Specifies a character string with a maximum length of 34 characters.", "minLength": 1, "maxLength": 34, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-Id"]}, "AccountSchemeName1Choice": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Id-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "CashAccountType2Choice": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ProxyAccountIdentification1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"Tp": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp"]}, "Id": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/Max2048Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Id"]}}, "required": ["Id"]}, "ProxyAccountType1Choice": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Tp-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "Max2048Text": {"type": "string", "description": "Specifies a character string with a maximum length of 2048 characters.", "minLength": 1, "maxLength": 2048, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlCdtrAgtAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAcct-Prxy-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgtAcct-Prxy-Id"]}, "BranchAndFinancialInstitutionIdentification6__5": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__4", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__4": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/Max140Text", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__3", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-PstlAdr"]}}}, "ClearingSystemMemberIdentification2": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/Max35Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["MmbId"]}, "ClearingSystemIdentification2Choice": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Identification code for a clearing system, that has not yet been identified in the list of clearing systems.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlDbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "Frequency36Choice": {"type": "object", "description": "Choice of format for a frequency, for example, the frequency of payment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies a frequency in terms of a specified period type.", "$ref": "#/definitions/Frequency6Code", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Tp"]}}, "required": ["Tp"]}, {"type": "object", "additionalProperties": false, "properties": {"Prd": {"description": "Specifies a frequency in terms of a count per period within a specified period type.", "$ref": "#/definitions/FrequencyPeriod1", "name": "Period", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd"]}}, "required": ["Prd"]}, {"type": "object", "additionalProperties": false, "properties": {"PtInTm": {"description": "Specifies a frequency in terms of an exact point in time or moment within a specified period type.", "$ref": "#/definitions/FrequencyAndMoment1", "name": "PointInTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm"]}}, "required": ["PtInTm"]}]}, "Frequency6Code": {"type": "string", "description": "Specifies the regularity of an event.\n*`YEAR`-Event takes place every year or once a year.\n*`MNTH`-Event takes place every month or once a month.\n*`QURT`-Event takes place every three months or four times a year.\n*`MIAN`-Event takes place every six months or two times a year.\n*`WEEK`-Event takes place once a week.\n*`DAIL`-Event takes place every day.\n*`ADHO`-Event takes place on request or as necessary.\n*`INDA`-Event takes place several times a day.\n*`FRTN`-Event takes place every two weeks.", "enum": ["YEAR", "MNTH", "QURT", "MIAN", "WEEK", "DAIL", "ADHO", "INDA", "FRTN"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-Tp"]}, "FrequencyPeriod1": {"type": "object", "description": "Defines a frequency in terms on counts per period for a specific period type.", "additionalProperties": false, "properties": {"Tp": {"description": "Period for which the number of instructions are to be created and processed.", "$ref": "#/definitions/Frequency6Code", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-Tp"]}, "CntPerPrd": {"description": "Number of instructions to be created and processed during the specified period.", "$ref": "#/definitions/DecimalNumber", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-CntPerPrd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-CntPerPrd"]}}, "required": ["Tp", "CntPerPrd"]}, "DecimalNumber": {"type": "string", "description": "Number of objects represented as a decimal number, for example 0.75 or 45.6.", "maxLength": 19, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-Prd-CntPerPrd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-Prd-CntPerPrd"]}, "FrequencyAndMoment1": {"type": "object", "description": "Defines a frequency in terms a specific moment within a specified period type.", "additionalProperties": false, "properties": {"Tp": {"description": "Period for which the number of instructions are to be created and processed.", "$ref": "#/definitions/Frequency6Code", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-Tp"]}, "PtInTm": {"description": "Further information on the exact point in time the event should take place.", "$ref": "#/definitions/Exact2NumericText", "name": "PointInTime", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-PtInTm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-PtInTm"]}}, "required": ["Tp", "PtInTm"]}, "Exact2NumericText": {"type": "string", "description": "Specifies a numeric string with an exact length of 2 digits.", "pattern": "^[0-9]{2}$", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlFrqcy-PtInTm-PtInTm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlTrckgDays", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Frqcy-PtInTm-PtInTm", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-TrckgDays"]}, "MandateSetupReason1Choice": {"type": "object", "description": "Specifies the reason for the setup of the mandate.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Reason for the mandate setup, as published in an external reason code list.", "$ref": "#/definitions/ExternalMandateSetupReason1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Reason for the mandate setup, in a proprietary form.", "$ref": "#/definitions/Max70Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalMandateSetupReason1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external mandate setup reason code in the format of character string with a maximum length of 4 characters.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-AmdmntInfDtls-OrgnlRsn-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-Rsn-Cd"]}, "Max1025Text": {"type": "string", "description": "Specifies a character string with a maximum length of 1025 characters.", "minLength": 1, "maxLength": 1025, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-MndtRltdInf-ElctrncSgntr"]}, "RemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system.", "additionalProperties": false, "properties": {"Ustrd": {"description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in an unstructured form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Unstructured", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Ustrd"]}, "Strd": {"type": "array", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "items": {"$ref": "#/definitions/StructuredRemittanceInformation16__1"}, "name": "Structured", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd"]}}}, "StructuredRemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "additionalProperties": false, "properties": {"RfrdDocInf": {"type": "array", "description": "Provides the identification and the content of the referred document.", "items": {"$ref": "#/definitions/ReferredDocumentInformation7__1"}, "name": "ReferredDocumentInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf"]}, "RfrdDocAmt": {"description": "Provides details on the amounts of the referred document.", "$ref": "#/definitions/RemittanceAmount2__1", "name": "ReferredDocumentAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt"]}, "CdtrRefInf": {"description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "$ref": "#/definitions/CreditorReferenceInformation2__1", "name": "CreditorReferenceInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf"]}, "Invcr": {"description": "Identification of the organisation issuing the invoice, when it is different from the creditor or ultimate creditor.", "$ref": "#/definitions/PartyIdentification135__5", "name": "Invoicer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr"]}, "Invcee": {"description": "Identification of the party to whom an invoice is issued, when it is different from the debtor or ultimate debtor.", "$ref": "#/definitions/PartyIdentification135__5", "name": "Invoicee", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee"]}, "TaxRmt": {"description": "Provides remittance information about a payment made for tax-related purposes.", "$ref": "#/definitions/TaxInformation7__1", "name": "TaxRemittance", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt"]}, "GrnshmtRmt": {"description": "Provides remittance information about a payment for garnishment-related purposes.", "$ref": "#/definitions/Garnishment3__1", "name": "GarnishmentRemittance", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt"]}, "AddtlRmtInf": {"type": "array", "maxItems": 3, "description": "Additional information, in free text form, to complement the structured remittance information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "name": "AdditionalRemittanceInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-AddtlRmtInf"]}}}, "ReferredDocumentInformation7__1": {"type": "object", "description": "Set of elements used to identify the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of referred document.", "$ref": "#/definitions/ReferredDocumentType4__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp"]}, "Nb": {"description": "Unique and unambiguous identification of the referred document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Number", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Nb"]}, "RltdDt": {"description": "Date associated with the referred document.", "$ref": "#/definitions/ISODate", "name": "RelatedDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-RltdDt"]}, "LineDtls": {"type": "array", "description": "Set of elements used to provide the content of the referred document line.", "items": {"$ref": "#/definitions/DocumentLineInformation1__1"}, "name": "LineDetails", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls"]}}}, "ReferredDocumentType4__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the referred document.", "$ref": "#/definitions/ReferredDocumentType3Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the reference document type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "ReferredDocumentType3Choice__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Document type in a coded form.", "$ref": "#/definitions/DocumentType6Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "DocumentType6Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`MSIN`-Document is an invoice claiming payment for the supply of metered services, for example gas or electricity supplied to a fixed meter.\n*`CNFA`-Document is a credit note for the final amount settled for a commercial transaction.\n*`DNFA`-Document is a debit note for the final amount settled for a commercial transaction.\n*`CINV`-Document is an invoice.\n*`CREN`-Document is a credit note.\n*`DEBN`-Document is a debit note.\n*`HIRI`-Document is an invoice for the hiring of human resources or renting goods or equipment.\n*`SBIN`-Document is an invoice issued by the debtor.\n*`CMCN`-Document is an agreement between the parties, stipulating the terms and conditions of the delivery of goods or services.\n*`SOAC`-Document is a statement of the transactions posted to the debtor's account at the supplier.\n*`DISP`-Document is a dispatch advice.\n*`BOLD`-Document is a shipping notice.\n*`VCHR`-Document is an electronic payment document.\n*`AROI`-Document is a payment that applies to a specific source document.\n*`TSUT`-Document is a transaction identifier as assigned by the Trade Services Utility.\n*`PUOR`-Document is a purchase order.", "enum": ["MSIN", "CNFA", "DNFA", "CINV", "CREN", "DEBN", "HIRI", "SBIN", "CMCN", "SOAC", "DISP", "BOLD", "VCHR", "AROI", "TSUT", "PUOR"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd"]}, "DocumentLineInformation1__1": {"type": "object", "description": "Provides document line information.\r\n", "additionalProperties": false, "properties": {"Id": {"type": "array", "description": "Provides identification of the document line.", "items": {"$ref": "#/definitions/DocumentLineIdentification1__1"}, "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id"]}, "Desc": {"description": "Description associated with the document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Description", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Desc"]}, "Amt": {"description": "Provides details on the amounts of the document line.", "$ref": "#/definitions/RemittanceAmount3__1", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt"]}}, "required": ["Id"]}, "DocumentLineIdentification1__1": {"type": "object", "description": "Identifies the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of referred document line identification.", "$ref": "#/definitions/DocumentLineType1__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp"]}, "Nb": {"description": "Identification of the type specified for the referred document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Number", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb"]}, "RltdDt": {"description": "Date associated with the referred document line.", "$ref": "#/definitions/ISODate", "name": "RelatedDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt"]}}}, "DocumentLineType1__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the referred document line identification.", "$ref": "#/definitions/DocumentLineType1Choice", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the reference document line identificationtype.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "DocumentLineType1Choice": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Line identification type in a coded form.", "$ref": "#/definitions/ExternalDocumentLineType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalDocumentLineType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the document line type as published in an external document type code list.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd"]}, "RemittanceAmount3__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"DuePyblAmt": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "DuePayableAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt"]}, "DscntApldAmt": {"type": "array", "description": "Amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1"}, "name": "DiscountAppliedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt"]}, "CdtNoteAmt": {"description": "Amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "CreditNoteAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt"]}, "TaxAmt": {"type": "array", "description": "Amount of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}, "name": "TaxAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt"]}, "AdjstmntAmtAndRsn": {"type": "array", "description": "Specifies detailed information on the amount and reason of the adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}, "name": "AdjustmentAmountAndReason", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn"]}, "RmtdAmt": {"description": "Amount of money remitted.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt"]}}}, "ActiveOrHistoricCurrencyAndAmount": {"type": "object", "description": "A number of monetary units specified in an active or a historic currency where the unit of currency is explicit and compliant with ISO 4217.", "additionalProperties": false, "properties": {"Ccy": {"$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy"]}, "amount": {"type": "string", "maxLength": 19, "pattern": "^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$", "name": "amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount"]}}, "required": ["Ccy", "amount"]}, "DiscountAmountAndType1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt"]}}, "required": ["Amt"]}, "DiscountAmountType1Choice": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/Max35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalDiscountAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd"]}, "TaxAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt"]}}, "required": ["Amt"]}, "TaxAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalTaxAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd"]}, "DocumentAdjustment1__1": {"type": "object", "description": "Set of elements used to provide information on the amount and reason of the document adjustment.", "additionalProperties": false, "properties": {"Amt": {"description": "Amount of money of the document adjustment.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt"]}, "CdtDbtInd": {"description": "Specifies whether the adjustment must be subtracted or added to the total amount.", "$ref": "#/definitions/CreditDebitCode", "name": "CreditDebitIndicator", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd"]}, "Rsn": {"description": "Specifies the reason for the adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax4Text_Extended", "name": "Reason", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn"]}, "AddtlInf": {"description": "Provides further details on the document adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "AdditionalInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf"]}}, "required": ["Amt"]}, "CreditDebitCode": {"type": "string", "description": "Specifies if an operation is an increase or a decrease.\n*`CRDT`-Operation is an increase.\n*`DBIT`-Operation is a decrease.", "enum": ["CRDT", "DBIT"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd"]}, "CBPR_RestrictedFINXMax4Text_Extended": {"type": "string", "description": "Specifies a character string witha minimum length of 1, and a maximum length of 4 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 4, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn"]}, "RemittanceAmount2__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"DuePyblAmt": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "DuePayableAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DuePyblAmt"]}, "DscntApldAmt": {"type": "array", "description": "Amount specified for the referred document is the amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}, "name": "DiscountAppliedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt"]}, "CdtNoteAmt": {"description": "Amount specified for the referred document is the amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "CreditNoteAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt"]}, "TaxAmt": {"type": "array", "description": "Quantity of cash resulting from the calculation of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__2"}, "name": "TaxAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt"]}, "AdjstmntAmtAndRsn": {"type": "array", "description": "Specifies detailed information on the amount and reason of the document adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}, "name": "AdjustmentAmountAndReason", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn"]}, "RmtdAmt": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-RmtdAmt"]}}}, "DiscountAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt"]}}, "required": ["Amt"]}, "DiscountAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "TaxAmountAndType1__2": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__2", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt"]}}, "required": ["Amt"]}, "TaxAmountType1Choice__2": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "CreditorReferenceInformation2__1": {"type": "object", "description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of creditor reference.", "$ref": "#/definitions/CreditorReferenceType2__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp"]}, "Ref": {"description": "Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.  Usage: If available, the initiating party should provide this reference in the structured remittance information, to enable reconciliation by the creditor upon receipt of the amount of money.  If the business context requires the use of a creditor reference or a payment remit identification, and only one identifier can be passed through the end-to-end chain, the creditor's reference or payment remittance identification should be quoted in the end-to-end transaction identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Reference", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Ref"]}}}, "CreditorReferenceType2__1": {"type": "object", "description": "Specifies the type of creditor reference.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Coded or proprietary format creditor reference type.", "$ref": "#/definitions/CreditorReferenceType1Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry"]}, "Issr": {"description": "Entity that assigns the credit reference type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "CreditorReferenceType1Choice__1": {"type": "object", "description": "Specifies the type of document referred by the creditor.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Type of creditor reference, in a coded form.", "$ref": "#/definitions/DocumentType3Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Creditor reference type, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "DocumentType3Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`RADM`-Document is a remittance advice sent separately from the current transaction.\n*`RPIN`-Document is a linked payment instruction to which the current payment instruction is related, for example, in a cover scenario.\n*`FXDR`-Document is a pre-agreed or pre-arranged foreign exchange transaction to which the payment transaction refers.\n*`DISP`-Document is a dispatch advice.\n*`PUOR`-Document is a purchase order.\n*`SCOR`-Document is a structured communication reference provided by the creditor to identify the referred transaction.", "enum": ["RADM", "RPIN", "FXDR", "DISP", "PUOR", "SCOR"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd"]}, "PartyIdentification135__5": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__3", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes"]}}}, "Party38Choice__3": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__3", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__3", "name": "PrivateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__3": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__3"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr"]}}}, "GenericOrganisationIdentification1__3": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__3", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "PersonIdentification13__3": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__3"}, "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr"]}}}, "GenericPersonIdentification1__3": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__3", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "TaxInformation7__1": {"type": "object", "description": "Details about tax paid, or to be paid, to the government in accordance with the law, including pre-defined parameters such as thresholds and type of account.", "additionalProperties": false, "properties": {"Cdtr": {"description": "Party on the credit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty1__1", "name": "Creditor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr"]}, "Dbtr": {"description": "Identifies the party on the debit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty2__1", "name": "Debtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr"]}, "UltmtDbtr": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the taxing authority.", "$ref": "#/definitions/TaxParty2__1", "name": "UltimateDebtor", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr"]}, "AdmstnZone": {"description": "Territorial part of a country to which the tax payment is related.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "AdministrationZone", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-AdmstnZone"]}, "RefNb": {"description": "Tax reference information that is specific to a taxing agency.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "ReferenceNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-RefNb"]}, "Mtd": {"description": "Method used to indicate the underlying business or how the tax is paid.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Method", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Mtd"]}, "TtlTaxblBaseAmt": {"description": "Total amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalTaxableBaseAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt"]}, "TtlTaxAmt": {"description": "Total amount of money as result of the calculation of the tax.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalTaxAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-TtlTaxAmt"]}, "Dt": {"description": "Date by which tax is due.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dt"]}, "SeqNb": {"description": "Sequential number of the tax report.", "$ref": "#/definitions/Number", "name": "SequenceNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-SeqNb"]}, "Rcrd": {"type": "array", "description": "Record of tax details.", "items": {"$ref": "#/definitions/TaxRecord2__1"}, "name": "Record", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd"]}}}, "TaxParty1__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"TaxId": {"description": "Tax identification number of the creditor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxId"]}, "RegnId": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "RegistrationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-RegnId"]}, "TaxTp": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxType", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Cdtr-TaxTp"]}}}, "TaxParty2__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"TaxId": {"description": "Tax identification number of the debtor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId"]}, "RegnId": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "RegistrationIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId"]}, "TaxTp": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxType", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp"]}, "Authstn": {"description": "Details of the authorised tax paying party.", "$ref": "#/definitions/TaxAuthorisation1__1", "name": "Authorisation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn"]}}}, "TaxAuthorisation1__1": {"type": "object", "description": "Details of the authorised tax paying party.", "additionalProperties": false, "properties": {"Titl": {"description": "Title or position of debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Title", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl"]}, "Nm": {"description": "Name of the debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm"]}}}, "Number": {"type": "string", "description": "Number of objects represented as an integer.", "maxLength": 19, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-SeqNb"]}, "TaxRecord2__1": {"type": "object", "description": "Set of elements used to define the tax record.", "additionalProperties": false, "properties": {"Tp": {"description": "High level code to identify the type of tax details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Tp"]}, "Ctgy": {"description": "Specifies the tax code as published by the tax authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Category", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Ctgy"]}, "CtgyDtls": {"description": "Provides further details of the category tax code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CategoryDetails", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls"]}, "DbtrSts": {"description": "Code provided by local authority to identify the status of the party that has drawn up the settlement document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DebtorS<PERSON>us", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts"]}, "CertId": {"description": "Identification number of the tax report as assigned by the taxing authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CertificateIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-CertId"]}, "FrmsCd": {"description": "Identifies, in a coded form, on which template the tax report is to be provided.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "FormsCode", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd"]}, "Prd": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2", "name": "Period", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd"]}, "TaxAmt": {"description": "Set of elements used to provide information on the amount of the tax record.", "$ref": "#/definitions/TaxAmount2", "name": "TaxAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt"]}, "AddtlInf": {"description": "Further details of the tax record.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "AdditionalInformation", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf"]}}}, "TaxPeriod2": {"type": "object", "description": "Period of time details related to the tax payment.", "additionalProperties": false, "properties": {"Yr": {"description": "Year related to the tax payment.", "$ref": "#/definitions/ISODate", "name": "Year", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr"]}, "Tp": {"description": "Identification of the period related to the tax payment.", "$ref": "#/definitions/TaxRecordPeriod1Code", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp"]}, "FrToDt": {"description": "Range of time between a start date and an end date for which the tax report is provided.", "$ref": "#/definitions/DatePeriod2", "name": "FromToDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt"]}}}, "TaxRecordPeriod1Code": {"type": "string", "description": "Specifies the period related to the tax payment.\n*`MM01`-Tax is related to the second month of the period.\n*`MM02`-Tax is related to the first month of the period.\n*`MM03`-Tax is related to the third month of the period.\n*`MM04`-Tax is related to the fourth month of the period.\n*`MM05`-Tax is related to the fifth month of the period.\n*`MM06`-Tax is related to the sixth month of the period.\n*`MM07`-Tax is related to the seventh month of the period.\n*`MM08`-Tax is related to the eighth month of the period.\n*`MM09`-Tax is related to the ninth month of the period.\n*`MM10`-Tax is related to the tenth month of the period.\n*`MM11`-Tax is related to the eleventh month of the period.\n*`MM12`-Tax is related to the twelfth month of the period.\n*`QTR1`-Tax is related to the first quarter of the period.\n*`QTR2`-Tax is related to the second quarter of the period.\n*`QTR3`-Tax is related to the third quarter of the period.\n*`QTR4`-Tax is related to the forth quarter of the period.\n*`HLF1`-Tax is related to the first half of the period.\n*`HLF2`-Tax is related to the second half of the period.", "enum": ["MM01", "MM02", "MM03", "MM04", "MM05", "MM06", "MM07", "MM08", "MM09", "MM10", "MM11", "MM12", "QTR1", "QTR2", "QTR3", "QTR4", "HLF1", "HLF2"], "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp"]}, "DatePeriod2": {"type": "object", "description": "Range of time defined by a start date and an end date.", "additionalProperties": false, "properties": {"FrDt": {"description": "Start date of the range.", "$ref": "#/definitions/ISODate", "name": "FromDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt"]}, "ToDt": {"description": "End date of the range.", "$ref": "#/definitions/ISODate", "name": "ToDate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt"]}}, "required": ["FrDt", "ToDt"]}, "TaxAmount2": {"type": "object", "description": "Set of elements used to provide information on the tax amount(s) of tax record.", "additionalProperties": false, "properties": {"Rate": {"description": "Rate used to calculate the tax.", "$ref": "#/definitions/PercentageRate", "name": "Rate", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate"]}, "TaxblBaseAmt": {"description": "Amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TaxableBaseAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt"]}, "TtlAmt": {"description": "Total amount that is the result of the calculation of the tax for the record.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt"]}, "Dtls": {"type": "array", "description": "Set of elements used to provide details on the tax period and amount.", "items": {"$ref": "#/definitions/TaxRecordDetails2"}, "name": "Details", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls"]}}}, "PercentageRate": {"type": "string", "description": "Rate expressed as a percentage, that is, in hundredths, for example, 0.7 is 7/10 of a percent, and 7.0 is 7%.", "maxLength": 12, "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate"]}, "TaxRecordDetails2": {"type": "object", "description": "Provides information on the individual tax amount(s) per period of the tax record.", "additionalProperties": false, "properties": {"Prd": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2", "name": "Period", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd"]}, "Amt": {"description": "Underlying tax amount related to the specified period.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt"]}}, "required": ["Amt"]}, "Garnishment3__1": {"type": "object", "description": "Provides remittance information about a payment for garnishment-related purposes.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of garnishment.", "$ref": "#/definitions/GarnishmentType1__1", "name": "Type", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp"]}, "Grnshee": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the garnisher.", "$ref": "#/definitions/PartyIdentification135__5", "name": "Garnishee", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Grnshee"]}, "GrnshmtAdmstr": {"description": "Party on the credit side of the transaction who administers the garnishment on behalf of the ultimate beneficiary.", "$ref": "#/definitions/PartyIdentification135__5", "name": "GarnishmentAdministrator", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr"]}, "RefNb": {"description": "Reference information that is specific to the agency receiving the garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "ReferenceNumber", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RefNb"]}, "Dt": {"description": "Date of payment which garnishment was taken from.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Dt"]}, "RmtdAmt": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-RmtdAmt"]}, "FmlyMdclInsrncInd": {"description": "Indicates if the person to whom the garnishment applies (that is, the ultimate debtor) has family medical insurance coverage available.", "$ref": "#/definitions/TrueFalseIndicator", "name": "FamilyMedicalInsuranceIndicator", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd"]}, "MplyeeTermntnInd": {"description": "Indicates if the employment of the person to whom the garnishment applies (that is, the ultimate debtor) has been terminated.", "$ref": "#/definitions/TrueFalseIndicator", "name": "EmployeeTerminationIndicator", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd"]}}, "required": ["Tp"]}, "GarnishmentType1__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the garnishment.", "$ref": "#/definitions/GarnishmentType1Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the garnishment type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "GarnishmentType1Choice__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Garnishment type in a coded form. Would suggest this to be an External Code List to contain: GNCS    Garnishment from a third party payer for Child Support GNDP    Garnishment from a Direct Payer for Child Support GTPP     Garnishment from a third party payer to taxing agency.", "$ref": "#/definitions/ExternalGarnishmentType1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalGarnishmentType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the garnishment type as published in an external document type code list.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd"]}, "Party40Choice__4": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Pty": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__2", "name": "Party", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Pty"]}}, "required": ["Pty"]}, {"type": "object", "additionalProperties": false, "properties": {"Agt": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__6", "name": "Agent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt"]}}, "required": ["Agt"]}]}, "BranchAndFinancialInstitutionIdentification6__6": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__5", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__5": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__2", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-PstlAdr"]}, "Othr": {"description": "Unique identification of an agent, as assigned by an institution, using an identification scheme.", "$ref": "#/definitions/GenericFinancialIdentification1__1", "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr"]}}}, "ClearingSystemMemberIdentification2__2": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__2", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["ClrSysId", "MmbId"]}, "ClearingSystemIdentification2Choice__2": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Identification code for a clearing system, that has not yet been identified in the list of clearing systems.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "GenericFinancialIdentification1__1": {"type": "object", "description": "Information related to an identification of a financial institution.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Id", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/FinancialIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-Issr", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-Issr"]}}, "required": ["Id"]}, "FinancialIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalFinancialInstitutionIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Prtry", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalFinancialInstitutionIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external financial institution identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Dbtr-Agt-FinInstnId-Othr-SchmeNm-Cd", "Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr-SchmeNm-Cd"]}, "BranchAndFinancialInstitutionIdentification6__7": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__6", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__6": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__2", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-DbtrAgt-FinInstnId-PstlAdr"]}}}, "Party40Choice__5": {"type": "object", "description": "Identification of a person, an organisation or a financial institution.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Pty": {"description": "Identification of a person or an organisation.", "$ref": "#/definitions/PartyIdentification135__3", "name": "Party", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Pty"]}}, "required": ["Pty"]}, {"type": "object", "additionalProperties": false, "properties": {"Agt": {"description": "Identification of a financial institution.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__8", "name": "Agent", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt"]}}, "required": ["Agt"]}]}, "BranchAndFinancialInstitutionIdentification6__8": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__7", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__7": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-PstlAdr"]}, "Othr": {"description": "Unique identification of an agent, as assigned by an institution, using an identification scheme.", "$ref": "#/definitions/GenericFinancialIdentification1__1", "name": "Other", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Cdtr-Agt-FinInstnId-Othr"]}}}, "Purpose2Choice__1": {"type": "object", "description": "Specifies the underlying reason for the payment transaction.\nUsage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Underlying reason for the payment transaction, as published in an external purpose code list.", "$ref": "#/definitions/ExternalPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external purpose code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-PmtRtr-TxInf-OrgnlTxRef-Purp-Cd"]}}}