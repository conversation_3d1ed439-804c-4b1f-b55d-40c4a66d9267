{"$comment": {"legalNotices": "SWIFT SCRL@2023. All rights reserved.\n\nThis schema is a component of MyStandards, the SWIFT collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with SWIFT SCRL, the user has no right to:\n - authorise external end users to use this component for other purposes than their internal use.\n - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n - re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual \nproperty rights of whatever nature in this component will remain the exclusive property of SWIFT or its \nlicensors.\n\nTrademarks\nSWIFT is the trade name of S.W.I.F.T. SCRL.\nThe following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.008.001.08_FIToFICustomerCreditTransfer", "baseMessage": "pacs.008.001.08", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0wiMZpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1A. AGENTS IDENTIFICATION -Textual Rules\r\n\r\n-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n1B. DEBTOR/CREDITOR - PARTY IDENTIFICATION - Textual Rules\r\n\r\n-> If AnyBIC is present, then (Name and Postal Address) is NOT allowed (other elements remain optional) - However, in case of conflicting information, AnyBIC will always take precedence.\r\n\r\n-> If Name is present, it is recommended to use Postal Address.\r\n\r\n\r\n2. Single transactions only are allowed.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema FIToFICustomerCreditTransferV08 (pacs.008.001.08) Generated by SWIFT MyStandards 2025-04-18 11:49:27", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.008.001.08"}, "FIToFICstmrCdtTrf": {"$ref": "#/definitions/FIToFICustomerCreditTransferV08", "name": "FIToFICustomerCreditTransferV08", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf"]}}, "required": ["FIToFICstmrCdtTrf"], "definitions": {"FIToFICustomerCreditTransferV08": {"type": "object", "description": "Scope\r\nThe FinancialInstitutionToFinancialInstitutionCustomerCreditTransfer message is sent by the debtor agent to the creditor agent, directly or through other agents and/or a payment clearing and settlement system. It is used to move funds from a debtor account to a creditor.\r\nUsage\r\nThe FIToFICustomerCreditTransfer message is exchanged between agents and can contain one or more customer credit transfer instructions.\r\nThe FIToFICustomerCreditTransfer message does not allow for grouping: a CreditTransferTransactionInformation block must be present for each credit transfer transaction.\r\nThe FIToFICustomerCreditTransfer message can be used in different ways:\r\n- If the instructing agent and the instructed agent wish to use their direct account relationship in the currency of the transfer then the message contains both the funds for the customer transfer(s) as well as the payment details;\r\n- If the instructing agent and the instructed agent have no direct account relationship in the currency of the transfer, or do not wish to use their account relationship, then other (reimbursement) agents will be involved to cover for the customer transfer(s). The FIToFICustomerCreditTransfer contains only the payment details and the instructing agent must cover the customer transfer by sending a FinancialInstitutionCreditTransfer to a reimbursement agent. This payment method is called the Cover method;\r\n- If more than two financial institutions are involved in the payment chain and if the FIToFICustomerCreditTransfer is sent from one financial institution to the next financial institution in the payment chain, then the payment method is called the Serial method.\r\nThe FIToFICustomerCreditTransfer message can be used in domestic and cross-border scenarios.", "additionalProperties": false, "properties": {"GrpHdr": {"description": "Set of characteristics shared by all individual transactions included in the message.", "$ref": "#/definitions/GroupHeader93__1", "name": "GroupHeader", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr"]}, "CdtTrfTxInf": {"description": "Set of elements providing information specific to the individual credit transfer(s).", "$ref": "#/definitions/CreditTransferTransaction39__1", "name": "CreditTransferTransactionInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf"]}}, "required": ["GrpHdr", "CdtTrfTxInf"]}, "GroupHeader93__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"MsgId": {"description": "Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "MessageIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-MsgId"]}, "CreDtTm": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "CreationDateTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-CreDtTm"]}, "NbOfTxs": {"description": "Number of individual transactions contained in the message.", "$ref": "#/definitions/Max15NumericText_fixed", "name": "NumberOfTransactions", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-NbOfTxs"]}, "SttlmInf": {"description": "Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.", "$ref": "#/definitions/SettlementInstruction7__1", "name": "SettlementInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf"]}}, "required": ["MsgId", "CreDtTm", "NbOfTxs", "SttlmInf"]}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-MsgId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-CreDtTm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"]}, "Max15NumericText_fixed": {"type": "string", "description": "\n*`1`-null", "enum": ["1"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-NbOfTxs"]}, "SettlementInstruction7__1": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"SttlmMtd": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__1", "name": "SettlementMethod", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"]}, "SttlmAcct": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "SettlementAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct"]}, "InstgRmbrsmntAgt": {"description": "Agent through which the instructing agent will reimburse the instructed agent.  Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructingReimbursementAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt"]}, "InstgRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the instructing reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "InstructingReimbursementAgentAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct"]}, "InstdRmbrsmntAgt": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If InstructedReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch. Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "InstructedReimbursementAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt"]}, "InstdRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the instructed reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "InstructedReimbursementAgentAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct"]}, "ThrdRmbrsmntAgt": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If ThirdReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "ThirdReimbursementAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt"]}, "ThrdRmbrsmntAgtAcct": {"description": "Unambiguous identification of the account of the third reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "ThirdReimbursementAgentAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct"]}}, "required": ["SttlmMtd"]}, "SettlementMethod1Code__1": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.\n*`COVE`-Settlement is done through a cover payment.", "enum": ["INDA", "INGA", "COVE"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"]}, "CashAccount38__1": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id"]}, "Tp": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp"]}, "Ccy": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy"]}, "Nm": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm"]}, "Prxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1", "name": "Proxy", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy"]}}, "required": ["Id"]}, "AccountIdentification4Choice__1": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"IBAN": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier", "name": "IBAN", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"]}}, "required": ["IBAN"]}, {"type": "object", "additionalProperties": false, "properties": {"Othr": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1__1", "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "IBAN2007Identifier": {"type": "string", "description": "The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard \"Banking and related financial services - International Bank Account Number (IBAN)\" and replaced by the more recent edition of the standard.", "pattern": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"]}, "GenericAccountIdentification1__1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax34Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr"]}}, "required": ["Id"]}, "CBPR_RestrictedFINXMax34Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash \"/\" at the beginning and end of line and double slash \"//\" within the line.", "minLength": 1, "maxLength": 34, "pattern": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"]}, "AccountSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalAccountIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"]}, "CashAccountType2Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCashAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"]}, "ActiveOrHistoricCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy"]}, "CBPR_RestrictedFINXMax70Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm"]}, "ProxyAccountIdentification1__1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"Tp": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp"]}, "Id": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/CBPR_RestrictedFINXMax320Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"]}}, "required": ["Id"]}, "ProxyAccountType1Choice__1": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalProxyAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external proxy account type code, as published in the proxy account type external code set.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"]}, "CBPR_RestrictedFINXMax320Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]\r\n", "minLength": 1, "maxLength": 320, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"]}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr"]}}}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI"]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["ClrSysId", "MmbId"]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"]}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI"]}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf"]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine"]}}}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine"]}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd"]}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes"]}, "CreditTransferTransaction39__1": {"type": "object", "description": "Provides further details specific to the individual transaction(s) included in the message.", "additionalProperties": false, "properties": {"PmtId": {"description": "Set of elements used to reference a payment instruction.", "$ref": "#/definitions/PaymentIdentification7__1", "name": "PaymentIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId"]}, "PmtTpInf": {"description": "Set of elements used to further specify the type of transaction.", "$ref": "#/definitions/PaymentTypeInformation28__1", "name": "PaymentTypeInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf"]}, "IntrBkSttlmAmt": {"description": "Amount of money moved between the instructing agent and the instructed agent.", "$ref": "#/definitions/CBPR_Amount__1", "name": "InterbankSettlementAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt"]}, "IntrBkSttlmDt": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.", "$ref": "#/definitions/ISODate", "name": "InterbankSettlementDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt"]}, "SttlmPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.", "$ref": "#/definitions/Priority3Code", "name": "SettlementPriority", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty"]}, "SttlmTmIndctn": {"description": "Provides information on the occurred settlement time(s) of the payment transaction.", "$ref": "#/definitions/SettlementDateTimeIndication1__1", "name": "SettlementTimeIndication", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn"]}, "SttlmTmReq": {"description": "Provides information on the requested settlement time(s) of the payment instruction.", "$ref": "#/definitions/SettlementTimeRequest2__1", "name": "SettlementTimeRequest", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq"]}, "InstdAmt": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party. Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1", "name": "InstructedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt"]}, "XchgRate": {"description": "Factor used to convert an amount from one currency into another. This reflects the price at which one currency was bought with another currency.", "$ref": "#/definitions/BaseOneRate", "name": "ExchangeRate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate"]}, "ChrgBr": {"description": "Specifies which party/parties will bear the charges associated with the processing of the payment transaction.", "$ref": "#/definitions/ChargeBearerType1Code__1", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr"]}, "ChrgsInf": {"type": "array", "description": "Provides information on the charges to be paid by the charge bearer(s) related to the payment transaction.", "items": {"$ref": "#/definitions/Charges7__1"}, "name": "ChargesInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf"]}, "PrvsInstgAgt1": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent1", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1"]}, "PrvsInstgAgt1Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent1Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct"]}, "PrvsInstgAgt2": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent2", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2"]}, "PrvsInstgAgt2Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent2Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct"]}, "PrvsInstgAgt3": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent3", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3"]}, "PrvsInstgAgt3Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent3Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct"]}, "InstgAgt": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructingAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt"]}, "InstdAgt": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructedAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt"]}, "IntrmyAgt1": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent1", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1"]}, "IntrmyAgt1Acct": {"description": "Unambiguous identification of the account of the intermediary agent 1 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent1Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct"]}, "IntrmyAgt2": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent2", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2"]}, "IntrmyAgt2Acct": {"description": "Unambiguous identification of the account of the intermediary agent 2 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent2Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct"]}, "IntrmyAgt3": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent3", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3"]}, "IntrmyAgt3Acct": {"description": "Unambiguous identification of the account of the intermediary agent 3 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent3Account", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct"]}, "UltmtDbtr": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/PartyIdentification135__1", "name": "UltimateDebtor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr"]}, "InitgPty": {"description": "Party that initiates the payment. Usage: This can be either the debtor or a party that initiates the credit transfer on behalf of the debtor.", "$ref": "#/definitions/PartyIdentification135__1", "name": "Initiating<PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty"]}, "Dbtr": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/PartyIdentification135__2", "name": "Debtor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr"]}, "DbtrAcct": {"description": "Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct"]}, "DbtrAgt": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "DebtorAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt"]}, "DbtrAgtAcct": {"description": "Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAgentAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct"]}, "CdtrAgt": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3", "name": "CreditorAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt"]}, "CdtrAgtAcct": {"description": "Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAgentAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct"]}, "Cdtr": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/PartyIdentification135__3", "name": "Creditor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr"]}, "CdtrAcct": {"description": "Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAccount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct"]}, "UltmtCdtr": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/PartyIdentification135__1", "name": "UltimateCreditor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr"]}, "InstrForCdtrAgt": {"type": "array", "maxItems": 2, "description": "Further information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor agent.", "items": {"$ref": "#/definitions/InstructionForCreditorAgent1__1"}, "name": "InstructionForCreditorAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt"]}, "InstrForNxtAgt": {"type": "array", "maxItems": 6, "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the next agent.   Usage: The next agent may not be the creditor agent. The instruction can relate to a level of service, can be an instruction that has to be executed by the agent, or can be information required by the next agent.", "items": {"$ref": "#/definitions/InstructionForNextAgent1__1"}, "name": "InstructionForNextAgent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt"]}, "Purp": {"description": "Underlying reason for the payment transaction. Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/Purpose2Choice__1", "name": "Purpose", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp"]}, "RgltryRptg": {"type": "array", "maxItems": 10, "description": "Information needed due to regulatory and statutory requirements.", "items": {"$ref": "#/definitions/RegulatoryReporting3__1"}, "name": "RegulatoryReporting", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg"]}, "RltdRmtInf": {"description": "Provides information related to the handling of the remittance information by any of the agents in the transaction processing chain.", "$ref": "#/definitions/RemittanceLocation7__1", "name": "RelatedRemittanceInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf"]}, "RmtInf": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.", "$ref": "#/definitions/RemittanceInformation16__1", "name": "RemittanceInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf"]}}, "required": ["PmtId", "IntrBkSttlmAmt", "IntrBkSttlmDt", "ChrgBr", "InstgAgt", "InstdAgt", "Dbtr", "DbtrAgt", "CdtrAgt", "Cdtr"]}, "PaymentIdentification7__1": {"type": "object", "description": "Provides further means of referencing a payment transaction.", "additionalProperties": false, "properties": {"InstrId": {"description": "Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the instruction.  Usage: The instruction identification is a point to point reference that can be used between the instructing party and the instructed party to refer to the individual instruction. It can be included in several messages related to the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text", "name": "InstructionIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId"]}, "EndToEndId": {"description": "Unique identification, as assigned by the initiating party, to unambiguously identify the transaction. This identification is passed on, unchanged, throughout the entire end-to-end chain.  Usage: The end-to-end identification can be used for reconciliation or to link tasks relating to the transaction. It can be included in several messages related to the transaction.  Usage: In case there are technical limitations to pass on multiple references, the end-to-end identification must be passed on throughout the entire end-to-end chain.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "EndToEndIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId"]}, "TxId": {"description": "Unique identification, as assigned by the first instructing agent, to unambiguously identify the transaction that is passed on, unchanged, throughout the entire interbank chain.  Usage: The transaction identification can be used for reconciliation, tracking or to link tasks relating to the transaction on the interbank level.  Usage: The instructing agent has to make sure that the transaction identification is unique for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "TransactionIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId"]}, "UETR": {"description": "Universally unique identifier to provide an end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier", "name": "UETR", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR"]}, "ClrSysRef": {"description": "Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "ClearingSystemReference", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef"]}}, "required": ["InstrId", "EndToEndId", "UETR"]}, "CBPR_RestrictedFINXMax16Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId"]}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR"]}, "PaymentTypeInformation28__1": {"type": "object", "description": "Provides further details of the type of payment.", "additionalProperties": false, "properties": {"InstrPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.", "$ref": "#/definitions/Priority2Code", "name": "InstructionPriority", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"]}, "ClrChanl": {"description": "Specifies the clearing channel to be used to process the payment instruction.", "$ref": "#/definitions/ClearingChannel2Code", "name": "ClearingChannel", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"]}, "SvcLvl": {"type": "array", "maxItems": 3, "description": "Agreement under which or rules under which the transaction should be processed.", "items": {"$ref": "#/definitions/ServiceLevel8Choice__1"}, "name": "ServiceLevel", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl"]}, "LclInstrm": {"description": "User community specific instrument.  Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.", "$ref": "#/definitions/LocalInstrument2Choice__1", "name": "LocalInstrument", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm"]}, "CtgyPurp": {"description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories. Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/CategoryPurpose1Choice__1", "name": "CategoryPurpose", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp"]}}}, "Priority2Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["HIGH", "NORM"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"]}, "ClearingChannel2Code": {"type": "string", "description": "Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.\n*`RTGS`-Clearing channel is a real-time gross settlement system.\n*`RTNS`-Clearing channel is a real-time net settlement system.\n*`MPNS`-Clearing channel is a mass payment net settlement system.\n*`BOOK`-Payment through internal book transfer.", "enum": ["RTGS", "RTNS", "MPNS", "BOOK"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"]}, "ServiceLevel8Choice__1": {"type": "object", "description": "Specifies the service level of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.", "$ref": "#/definitions/ExternalServiceLevel1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies a pre-agreed service or level of service between the parties, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalServiceLevel1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external service level code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"]}, "LocalInstrument2Choice__1": {"type": "object", "description": "Set of elements that further identifies the type of local instruments being requested by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the local instrument, as published in an external local instrument code list.", "$ref": "#/definitions/ExternalLocalInstrument1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the local instrument, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalLocalInstrument1Code": {"type": "string", "minLength": 1, "maxLength": 35, "description": "Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"]}, "CategoryPurpose1Choice__1": {"type": "object", "description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories.\nUsage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Category purpose, as published in an external category purpose code list.", "$ref": "#/definitions/ExternalCategoryPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Category purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCategoryPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the category purpose, as published in an external category purpose code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"]}, "CBPR_Amount__1": {"type": "object", "additionalProperties": false, "properties": {"Ccy": {"$ref": "#/definitions/ActiveCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy"]}, "amount": {"type": "string", "maxLength": 15, "pattern": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$", "name": "amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount"]}}, "required": ["Ccy", "amount"]}, "ActiveCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy"]}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt"]}, "Priority3Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`URGT`-Priority level is urgent (highest priority possible).\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["URGT", "HIGH", "NORM"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty"]}, "SettlementDateTimeIndication1__1": {"type": "object", "description": "Information on the occurred settlement time(s) of the payment transaction.", "additionalProperties": false, "properties": {"DbtDtTm": {"description": "Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "DebitDateTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm"]}, "CdtDtTm": {"description": "Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "CreditDateTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"]}}}, "SettlementTimeRequest2__1": {"type": "object", "description": "Provides information on the requested settlement time(s) of the payment instruction.", "additionalProperties": false, "properties": {"CLSTm": {"description": "Time by which the amount of money must be credited, with confirmation, to the CLS Bank's account at the central bank. Usage: Time must be expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_Time", "name": "CLSTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm"]}, "TillTm": {"description": "Time until when the payment may be settled.", "$ref": "#/definitions/CBPR_Time", "name": "TillTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm"]}, "FrTm": {"description": "Time as from when the payment may be settled.", "$ref": "#/definitions/CBPR_Time", "name": "FromTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm"]}, "RjctTm": {"description": "Time by when the payment must be settled to avoid rejection.", "$ref": "#/definitions/CBPR_Time", "name": "RejectTime", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"]}}}, "CBPR_Time": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory time component, expressed in local time with UTC offset format (hh:mm:ss.sss+/-hh:mm). \r\n\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 19:20:30.45+01:00", "pattern": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"]}, "BaseOneRate": {"type": "string", "description": "Rate expressed as a decimal, for example, 0.7 is 7/10 and 70%.", "maxLength": 12, "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate"]}, "ChargeBearerType1Code__1": {"type": "string", "description": "Specifies which party(ies) will pay charges due for processing of the instruction.\n*`DEBT`-All transaction charges are to be borne by the debtor.\n*`CRED`-All transaction charges are to be borne by the creditor.\n*`SHAR`-In a credit transfer context, means that transaction charges on the sender side are to be borne by the debtor, transaction charges on the receiver side are to be borne by the creditor. In a direct debit context, means that transaction charges on the sender side are to be borne by the creditor, transaction charges on the receiver side are to be borne by the debtor.", "enum": ["DEBT", "CRED", "SHAR"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr"]}, "Charges7__1": {"type": "object", "description": "Provides information on the charges related to the payment transaction.", "additionalProperties": false, "properties": {"Amt": {"description": "Transaction charges to be paid by the charge bearer.", "$ref": "#/definitions/CBPR_Amount__1", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt"]}, "Agt": {"description": "Agent that takes the transaction charges or to which the transaction charges are due.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Agent", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt"]}}, "required": ["Amt", "Agt"]}, "BranchAndFinancialInstitutionIdentification6__2": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__2", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__2": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI"]}}, "required": ["BICFI"]}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes"]}}}, "PostalAddress24__2": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 2, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine"]}}, "required": ["TwnNm", "Ctry"]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1", "name": "PrivateIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr"]}}}, "AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd"]}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr"]}}}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"BirthDt": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate", "name": "BirthDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"]}, "PrvcOfBirth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "ProvinceOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"]}, "CityOfBirth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CityOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"]}, "CtryOfBirth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode", "name": "CountryOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"]}}, "required": ["BirthDt", "CityOfBirth", "CtryOfBirth"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd"]}, "PartyIdentification135__2": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__2", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes"]}}}, "Party38Choice__2": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__2", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__2", "name": "PrivateIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__2": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__2"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr"]}}}, "GenericOrganisationIdentification1__2": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__2", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr"]}}, "required": ["Id", "SchmeNm"]}, "OrganisationIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, "PersonIdentification13__2": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__2"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr"]}}}, "GenericPersonIdentification1__2": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__2", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id", "SchmeNm"]}, "PersonIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, "BranchAndFinancialInstitutionIdentification6__3": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId"]}, "BrnchId": {"description": "Identifies a specific branch of a financial institution.  Usage: This component should be used in case the identification information in the financial institution component does not provide identification up to branch level.", "$ref": "#/definitions/BranchData3__1", "name": "BranchIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId"]}}, "required": ["FinInstnId"]}, "BranchData3__1": {"type": "object", "description": "Information that locates and identifies a specific branch of a financial institution.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a branch of a financial institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id"]}}}, "PartyIdentification135__3": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes"]}}}, "InstructionForCreditorAgent1__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the creditor's agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the creditor's agent.", "additionalProperties": false, "properties": {"Cd": {"description": "Coded information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor's agent.", "$ref": "#/definitions/Instruction3Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"]}, "InstrInf": {"description": "Further information complementing the coded instruction or instruction to the creditor's agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text", "name": "InstructionInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf"]}}}, "Instruction3Code": {"type": "string", "description": "Specifies further instructions concerning the processing of a payment instruction, provided by the sending clearing agent to the next agent(s).\n*`CHQB`-(Ultimate) creditor must be paid by cheque.\n*`HOLD`-Amount of money must be held for the (ultimate) creditor, who will call. Pay on identification.\n*`PHOB`-Please advise/contact (ultimate) creditor/claimant by phone.\n*`TELB`-Please advise/contact (ultimate) creditor/claimant by the most efficient means of telecommunication.", "enum": ["CHQB", "HOLD", "PHOB", "TELB"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"]}, "CBPR_RestrictedFINXMax140Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm"]}, "InstructionForNextAgent1__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by an other agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the other agent.", "additionalProperties": false, "properties": {"InstrInf": {"description": "Further information complementing the coded instruction or instruction to the next agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "InstructionInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf"]}}}, "Purpose2Choice__1": {"type": "object", "description": "Specifies the underlying reason for the payment transaction.\nUsage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Underlying reason for the payment transaction, as published in an external purpose code list.", "$ref": "#/definitions/ExternalPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external purpose code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd"]}, "RegulatoryReporting3__1": {"type": "object", "description": "Information needed due to regulatory and/or statutory requirements.", "additionalProperties": false, "properties": {"DbtCdtRptgInd": {"description": "Identifies whether the regulatory reporting information applies to the debit side, to the credit side or to both debit and credit sides of the transaction.", "$ref": "#/definitions/RegulatoryReportingType1Code", "name": "DebitCreditReportingIndicator", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd"]}, "Authrty": {"description": "Entity requiring the regulatory reporting information.", "$ref": "#/definitions/RegulatoryAuthority2__1", "name": "Authority", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty"]}, "Dtls": {"type": "array", "description": "Set of elements used to provide details on the regulatory reporting information.", "items": {"$ref": "#/definitions/StructuredRegulatoryReporting3__1"}, "name": "Details", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls"]}}}, "RegulatoryReportingType1Code": {"type": "string", "description": "Identifies whether the regulatory reporting information applies to the debit side, to the credit side or to both debit and credit sides of the transaction.\n*`CRED`-Regulatory information applies to the credit side.\n*`DEBT`-Regulatory information applies to the debit side.\n*`BOTH`-Regulatory information applies to both credit and debit sides.", "enum": ["CRED", "DEBT", "BOTH"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd"]}, "RegulatoryAuthority2__1": {"type": "object", "description": "Entity requiring the regulatory reporting information.", "additionalProperties": false, "properties": {"Nm": {"description": "Name of the entity requiring the regulatory reporting information.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm"]}, "Ctry": {"description": "Country of the entity that requires the regulatory reporting information.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry"]}}}, "StructuredRegulatoryReporting3__1": {"type": "object", "description": "Information needed due to regulatory and statutory requirements.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the information supplied in the regulatory reporting details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp"]}, "Dt": {"description": "Date related to the specified type of regulatory reporting details.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt"]}, "Ctry": {"description": "Country related to the specified type of regulatory reporting details.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry"]}, "Cd": {"description": "Specifies the nature, purpose, and reason for the transaction to be reported for regulatory and statutory requirements in a coded form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax10Text", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd"]}, "Amt": {"description": "Amount of money to be reported for regulatory and statutory requirements.", "$ref": "#/definitions/CBPR_Amount__1", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt"]}, "Inf": {"type": "array", "description": "Additional details that cater for specific domestic regulatory requirements.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "name": "Information", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf"]}}}, "CBPR_RestrictedFINXMax10Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 10 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 10, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd"]}, "RemittanceLocation7__1": {"type": "object", "description": "Provides information on the remittance advice.", "additionalProperties": false, "properties": {"RmtId": {"description": "Unique identification, as assigned by the initiating party, to unambiguously identify the remittance information sent separately from the payment instruction, such as a remittance advice.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "RemittanceIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId"]}, "RmtLctnDtls": {"type": "array", "maxItems": 2, "description": "Set of elements used to provide information on the location and/or delivery of the remittance information.", "items": {"$ref": "#/definitions/RemittanceLocationData1__1"}, "name": "RemittanceLocationDetails", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls"]}}}, "RemittanceLocationData1__1": {"type": "object", "description": "Provides additional details on the remittance advice.", "additionalProperties": false, "properties": {"Mtd": {"description": "Method used to deliver the remittance advice information.", "$ref": "#/definitions/RemittanceLocationMethod2Code", "name": "Method", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd"]}, "ElctrncAdr": {"description": "Electronic address to which an agent is to send the remittance information.", "$ref": "#/definitions/CBPR_RestrictedFINXMax2048Text_Extended", "name": "ElectronicAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr"]}, "PstlAdr": {"description": "Postal address to which an agent is to send the remittance information.", "$ref": "#/definitions/NameAndAddress16__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr"]}}, "required": ["Mtd"]}, "RemittanceLocationMethod2Code": {"type": "string", "description": "Specifies the method used to deliver the remittance advice information.\n*`FAXI`-Remittance advice information must be faxed.\n*`EDIC`-Remittance advice information must be sent through Electronic Data Interchange (EDI).\n*`URID`-Remittance advice information needs to be sent to a Uniform Resource Identifier (URI). URI is a compact string of characters that uniquely identify an abstract or physical resource. URI's are the super-set of identifiers, such as URLs, email addresses, ftp sites, etc, and as such, provide the syntax for all of the identification schemes.\n*`EMAL`-Remittance advice information must be sent through e-mail.\n*`POST`-Remittance advice information must be sent through postal services.\n*`SMSM`-Remittance advice information must be sent through by phone as a short message service (SMS).", "enum": ["FAXI", "EDIC", "URID", "EMAL", "POST", "SMSM"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd"]}, "CBPR_RestrictedFINXMax2048Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 2048 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 2048, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr"]}, "NameAndAddress16__1": {"type": "object", "description": "Information that locates and identifies a party.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm"]}, "Adr": {"description": "Postal address of a party.", "$ref": "#/definitions/PostalAddress24__1", "name": "Address", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr"]}}, "required": ["Nm", "<PERSON><PERSON>"]}, "RemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system.", "additionalProperties": false, "properties": {"Ustrd": {"description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in an unstructured form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Unstructured", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd"]}, "Strd": {"type": "array", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "items": {"$ref": "#/definitions/StructuredRemittanceInformation16__1"}, "name": "Structured", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd"]}}}, "StructuredRemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "additionalProperties": false, "properties": {"RfrdDocInf": {"type": "array", "description": "Provides the identification and the content of the referred document.", "items": {"$ref": "#/definitions/ReferredDocumentInformation7__1"}, "name": "ReferredDocumentInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf"]}, "RfrdDocAmt": {"description": "Provides details on the amounts of the referred document.", "$ref": "#/definitions/RemittanceAmount2__1", "name": "ReferredDocumentAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt"]}, "CdtrRefInf": {"description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "$ref": "#/definitions/CreditorReferenceInformation2__1", "name": "CreditorReferenceInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf"]}, "Invcr": {"description": "Identification of the organisation issuing the invoice, when it is different from the creditor or ultimate creditor.", "$ref": "#/definitions/PartyIdentification135__4", "name": "Invoicer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr"]}, "Invcee": {"description": "Identification of the party to whom an invoice is issued, when it is different from the debtor or ultimate debtor.", "$ref": "#/definitions/PartyIdentification135__4", "name": "Invoicee", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee"]}, "TaxRmt": {"description": "Provides remittance information about a payment made for tax-related purposes.", "$ref": "#/definitions/TaxInformation7__1", "name": "TaxRemittance", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt"]}, "GrnshmtRmt": {"description": "Provides remittance information about a payment for garnishment-related purposes.", "$ref": "#/definitions/Garnishment3__1", "name": "GarnishmentRemittance", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt"]}, "AddtlRmtInf": {"type": "array", "maxItems": 3, "description": "Additional information, in free text form, to complement the structured remittance information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "name": "AdditionalRemittanceInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf"]}}}, "ReferredDocumentInformation7__1": {"type": "object", "description": "Set of elements used to identify the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of referred document.", "$ref": "#/definitions/ReferredDocumentType4__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp"]}, "Nb": {"description": "Unique and unambiguous identification of the referred document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Number", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb"]}, "RltdDt": {"description": "Date associated with the referred document.", "$ref": "#/definitions/ISODate", "name": "RelatedDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt"]}, "LineDtls": {"type": "array", "description": "Set of elements used to provide the content of the referred document line.", "items": {"$ref": "#/definitions/DocumentLineInformation1__1"}, "name": "LineDetails", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls"]}}}, "ReferredDocumentType4__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the referred document.", "$ref": "#/definitions/ReferredDocumentType3Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the reference document type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "ReferredDocumentType3Choice__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Document type in a coded form.", "$ref": "#/definitions/DocumentType6Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "DocumentType6Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`MSIN`-Document is an invoice claiming payment for the supply of metered services, for example gas or electricity supplied to a fixed meter.\n*`CNFA`-Document is a credit note for the final amount settled for a commercial transaction.\n*`DNFA`-Document is a debit note for the final amount settled for a commercial transaction.\n*`CINV`-Document is an invoice.\n*`CREN`-Document is a credit note.\n*`DEBN`-Document is a debit note.\n*`HIRI`-Document is an invoice for the hiring of human resources or renting goods or equipment.\n*`SBIN`-Document is an invoice issued by the debtor.\n*`CMCN`-Document is an agreement between the parties, stipulating the terms and conditions of the delivery of goods or services.\n*`SOAC`-Document is a statement of the transactions posted to the debtor's account at the supplier.\n*`DISP`-Document is a dispatch advice.\n*`BOLD`-Document is a shipping notice.\n*`VCHR`-Document is an electronic payment document.\n*`AROI`-Document is a payment that applies to a specific source document.\n*`TSUT`-Document is a transaction identifier as assigned by the Trade Services Utility.\n*`PUOR`-Document is a purchase order.", "enum": ["MSIN", "CNFA", "DNFA", "CINV", "CREN", "DEBN", "HIRI", "SBIN", "CMCN", "SOAC", "DISP", "BOLD", "VCHR", "AROI", "TSUT", "PUOR"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd"]}, "DocumentLineInformation1__1": {"type": "object", "description": "Provides document line information.\r\n", "additionalProperties": false, "properties": {"Id": {"type": "array", "description": "Provides identification of the document line.", "items": {"$ref": "#/definitions/DocumentLineIdentification1__1"}, "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id"]}, "Desc": {"description": "Description associated with the document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Description", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc"]}, "Amt": {"description": "Provides details on the amounts of the document line.", "$ref": "#/definitions/RemittanceAmount3__1", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt"]}}, "required": ["Id"]}, "DocumentLineIdentification1__1": {"type": "object", "description": "Identifies the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of referred document line identification.", "$ref": "#/definitions/DocumentLineType1__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp"]}, "Nb": {"description": "Identification of the type specified for the referred document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Number", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb"]}, "RltdDt": {"description": "Date associated with the referred document line.", "$ref": "#/definitions/ISODate", "name": "RelatedDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt"]}}}, "DocumentLineType1__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the referred document line identification.", "$ref": "#/definitions/DocumentLineType1Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the reference document line identificationtype.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "DocumentLineType1Choice__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Line identification type in a coded form.", "$ref": "#/definitions/ExternalDocumentLineType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalDocumentLineType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the document line type as published in an external document type code list.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd"]}, "RemittanceAmount3__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"DuePyblAmt": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "DuePayableAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt"]}, "DscntApldAmt": {"type": "array", "description": "Amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}, "name": "DiscountAppliedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt"]}, "CdtNoteAmt": {"description": "Amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "CreditNoteAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt"]}, "TaxAmt": {"type": "array", "description": "Amount of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}, "name": "TaxAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt"]}, "AdjstmntAmtAndRsn": {"type": "array", "description": "Specifies detailed information on the amount and reason of the adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}, "name": "AdjustmentAmountAndReason", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn"]}, "RmtdAmt": {"description": "Amount of money remitted.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt"]}}}, "ActiveOrHistoricCurrencyAndAmount": {"type": "object", "description": "A number of monetary units specified in an active or a historic currency where the unit of currency is explicit and compliant with ISO 4217.", "additionalProperties": false, "properties": {"Ccy": {"$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy"]}, "amount": {"type": "string", "maxLength": 19, "pattern": "^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$", "name": "amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount"]}}, "required": ["Ccy", "amount"]}, "DiscountAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt"]}}, "required": ["Amt"]}, "DiscountAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalDiscountAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd"]}, "TaxAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp"]}, "Amt": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt"]}}, "required": ["Amt"]}, "TaxAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalTaxAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd"]}, "DocumentAdjustment1__1": {"type": "object", "description": "Set of elements used to provide information on the amount and reason of the document adjustment.", "additionalProperties": false, "properties": {"Amt": {"description": "Amount of money of the document adjustment.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt"]}, "CdtDbtInd": {"description": "Specifies whether the adjustment must be subtracted or added to the total amount.", "$ref": "#/definitions/CreditDebitCode", "name": "CreditDebitIndicator", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd"]}, "Rsn": {"description": "Specifies the reason for the adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax4Text_Extended", "name": "Reason", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn"]}, "AddtlInf": {"description": "Provides further details on the document adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "AdditionalInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf"]}}, "required": ["Amt"]}, "CreditDebitCode": {"type": "string", "description": "Specifies if an operation is an increase or a decrease.\n*`CRDT`-Operation is an increase.\n*`DBIT`-Operation is a decrease.", "enum": ["CRDT", "DBIT"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd"]}, "CBPR_RestrictedFINXMax4Text_Extended": {"type": "string", "description": "Specifies a character string witha minimum length of 1, and a maximum length of 4 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 4, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn"]}, "RemittanceAmount2__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"DuePyblAmt": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "DuePayableAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt"]}, "DscntApldAmt": {"type": "array", "description": "Amount specified for the referred document is the amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}, "name": "DiscountAppliedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt"]}, "CdtNoteAmt": {"description": "Amount specified for the referred document is the amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "CreditNoteAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt"]}, "TaxAmt": {"type": "array", "description": "Quantity of cash resulting from the calculation of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}, "name": "TaxAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt"]}, "AdjstmntAmtAndRsn": {"type": "array", "description": "Specifies detailed information on the amount and reason of the document adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}, "name": "AdjustmentAmountAndReason", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn"]}, "RmtdAmt": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt"]}}}, "CreditorReferenceInformation2__1": {"type": "object", "description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of creditor reference.", "$ref": "#/definitions/CreditorReferenceType2__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp"]}, "Ref": {"description": "Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.  Usage: If available, the initiating party should provide this reference in the structured remittance information, to enable reconciliation by the creditor upon receipt of the amount of money.  If the business context requires the use of a creditor reference or a payment remit identification, and only one identifier can be passed through the end-to-end chain, the creditor's reference or payment remittance identification should be quoted in the end-to-end transaction identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Reference", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref"]}}}, "CreditorReferenceType2__1": {"type": "object", "description": "Specifies the type of creditor reference.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Coded or proprietary format creditor reference type.", "$ref": "#/definitions/CreditorReferenceType1Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry"]}, "Issr": {"description": "Entity that assigns the credit reference type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "CreditorReferenceType1Choice__1": {"type": "object", "description": "Specifies the type of document referred by the creditor.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Type of creditor reference, in a coded form.", "$ref": "#/definitions/DocumentType3Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Creditor reference type, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "DocumentType3Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`RADM`-Document is a remittance advice sent separately from the current transaction.\n*`RPIN`-Document is a linked payment instruction to which the current payment instruction is related, for example, in a cover scenario.\n*`FXDR`-Document is a pre-agreed or pre-arranged foreign exchange transaction to which the payment transaction refers.\n*`DISP`-Document is a dispatch advice.\n*`PUOR`-Document is a purchase order.\n*`SCOR`-Document is a structured communication reference provided by the creditor to identify the referred transaction.", "enum": ["RADM", "RPIN", "FXDR", "DISP", "PUOR", "SCOR"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd"]}, "PartyIdentification135__4": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"Nm": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2", "name": "PostalAddress", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr"]}, "Id": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__3", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id"]}, "CtryOfRes": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode", "name": "CountryOfResidence", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes"]}}}, "Party38Choice__3": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"OrgId": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__3", "name": "OrganisationIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId"]}}, "required": ["OrgId"]}, {"type": "object", "additionalProperties": false, "properties": {"PrvtId": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__3", "name": "PrivateIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId"]}}, "required": ["PrvtId"]}]}, "OrganisationIdentification29__3": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"AnyBIC": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier", "name": "AnyBIC", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC"]}, "LEI": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__3"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr"]}}}, "GenericOrganisationIdentification1__3": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__3", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr"]}}, "required": ["Id"]}, "OrganisationIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "PersonIdentification13__3": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"DtAndPlcOfBirth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1", "name": "DateAndPlaceOfBirth", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth"]}, "Othr": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__3"}, "name": "Other", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr"]}}}, "GenericPersonIdentification1__3": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__3", "name": "SchemeName", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr"]}}, "required": ["Id"]}, "PersonIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "TaxInformation7__1": {"type": "object", "description": "Details about tax paid, or to be paid, to the government in accordance with the law, including pre-defined parameters such as thresholds and type of account.", "additionalProperties": false, "properties": {"Cdtr": {"description": "Party on the credit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty1__1", "name": "Creditor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr"]}, "Dbtr": {"description": "Identifies the party on the debit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty2__1", "name": "Debtor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr"]}, "UltmtDbtr": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the taxing authority.", "$ref": "#/definitions/TaxParty2__1", "name": "UltimateDebtor", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr"]}, "AdmstnZone": {"description": "Territorial part of a country to which the tax payment is related.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "AdministrationZone", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone"]}, "RefNb": {"description": "Tax reference information that is specific to a taxing agency.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "ReferenceNumber", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb"]}, "Mtd": {"description": "Method used to indicate the underlying business or how the tax is paid.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Method", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd"]}, "TtlTaxblBaseAmt": {"description": "Total amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalTaxableBaseAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt"]}, "TtlTaxAmt": {"description": "Total amount of money as result of the calculation of the tax.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalTaxAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt"]}, "Dt": {"description": "Date by which tax is due.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt"]}, "SeqNb": {"description": "Sequential number of the tax report.", "$ref": "#/definitions/Number", "name": "SequenceNumber", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb"]}, "Rcrd": {"type": "array", "description": "Record of tax details.", "items": {"$ref": "#/definitions/TaxRecord2__1"}, "name": "Record", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd"]}}}, "TaxParty1__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"TaxId": {"description": "Tax identification number of the creditor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId"]}, "RegnId": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "RegistrationIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId"]}, "TaxTp": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxType", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp"]}}}, "TaxParty2__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"TaxId": {"description": "Tax identification number of the debtor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId"]}, "RegnId": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "RegistrationIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId"]}, "TaxTp": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TaxType", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp"]}, "Authstn": {"description": "Details of the authorised tax paying party.", "$ref": "#/definitions/TaxAuthorisation1__1", "name": "Authorisation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn"]}}}, "TaxAuthorisation1__1": {"type": "object", "description": "Details of the authorised tax paying party.", "additionalProperties": false, "properties": {"Titl": {"description": "Title or position of debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Title", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl"]}, "Nm": {"description": "Name of the debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm"]}}}, "Number": {"type": "string", "description": "Number of objects represented as an integer.", "maxLength": 19, "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb"]}, "TaxRecord2__1": {"type": "object", "description": "Set of elements used to define the tax record.", "additionalProperties": false, "properties": {"Tp": {"description": "High level code to identify the type of tax details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp"]}, "Ctgy": {"description": "Specifies the tax code as published by the tax authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Category", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy"]}, "CtgyDtls": {"description": "Provides further details of the category tax code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CategoryDetails", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls"]}, "DbtrSts": {"description": "Code provided by local authority to identify the status of the party that has drawn up the settlement document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DebtorS<PERSON>us", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts"]}, "CertId": {"description": "Identification number of the tax report as assigned by the taxing authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CertificateIdentification", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId"]}, "FrmsCd": {"description": "Identifies, in a coded form, on which template the tax report is to be provided.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "FormsCode", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd"]}, "Prd": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2", "name": "Period", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd"]}, "TaxAmt": {"description": "Set of elements used to provide information on the amount of the tax record.", "$ref": "#/definitions/TaxAmount2", "name": "TaxAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt"]}, "AddtlInf": {"description": "Further details of the tax record.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "AdditionalInformation", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf"]}}}, "TaxPeriod2": {"type": "object", "description": "Period of time details related to the tax payment.", "additionalProperties": false, "properties": {"Yr": {"description": "Year related to the tax payment.", "$ref": "#/definitions/ISODate", "name": "Year", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr"]}, "Tp": {"description": "Identification of the period related to the tax payment.", "$ref": "#/definitions/TaxRecordPeriod1Code", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp"]}, "FrToDt": {"description": "Range of time between a start date and an end date for which the tax report is provided.", "$ref": "#/definitions/DatePeriod2", "name": "FromToDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt"]}}}, "TaxRecordPeriod1Code": {"type": "string", "description": "Specifies the period related to the tax payment.\n*`MM01`-Tax is related to the second month of the period.\n*`MM02`-Tax is related to the first month of the period.\n*`MM03`-Tax is related to the third month of the period.\n*`MM04`-Tax is related to the fourth month of the period.\n*`MM05`-Tax is related to the fifth month of the period.\n*`MM06`-Tax is related to the sixth month of the period.\n*`MM07`-Tax is related to the seventh month of the period.\n*`MM08`-Tax is related to the eighth month of the period.\n*`MM09`-Tax is related to the ninth month of the period.\n*`MM10`-Tax is related to the tenth month of the period.\n*`MM11`-Tax is related to the eleventh month of the period.\n*`MM12`-Tax is related to the twelfth month of the period.\n*`QTR1`-Tax is related to the first quarter of the period.\n*`QTR2`-Tax is related to the second quarter of the period.\n*`QTR3`-Tax is related to the third quarter of the period.\n*`QTR4`-Tax is related to the forth quarter of the period.\n*`HLF1`-Tax is related to the first half of the period.\n*`HLF2`-Tax is related to the second half of the period.", "enum": ["MM01", "MM02", "MM03", "MM04", "MM05", "MM06", "MM07", "MM08", "MM09", "MM10", "MM11", "MM12", "QTR1", "QTR2", "QTR3", "QTR4", "HLF1", "HLF2"], "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp"]}, "DatePeriod2": {"type": "object", "description": "Range of time defined by a start date and an end date.", "additionalProperties": false, "properties": {"FrDt": {"description": "Start date of the range.", "$ref": "#/definitions/ISODate", "name": "FromDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt"]}, "ToDt": {"description": "End date of the range.", "$ref": "#/definitions/ISODate", "name": "ToDate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt"]}}, "required": ["FrDt", "ToDt"]}, "TaxAmount2": {"type": "object", "description": "Set of elements used to provide information on the tax amount(s) of tax record.", "additionalProperties": false, "properties": {"Rate": {"description": "Rate used to calculate the tax.", "$ref": "#/definitions/PercentageRate", "name": "Rate", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate"]}, "TaxblBaseAmt": {"description": "Amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TaxableBaseAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt"]}, "TtlAmt": {"description": "Total amount that is the result of the calculation of the tax for the record.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "TotalAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt"]}, "Dtls": {"type": "array", "description": "Set of elements used to provide details on the tax period and amount.", "items": {"$ref": "#/definitions/TaxRecordDetails2"}, "name": "Details", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls"]}}}, "PercentageRate": {"type": "string", "description": "Rate expressed as a percentage, that is, in hundredths, for example, 0.7 is 7/10 of a percent, and 7.0 is 7%.", "maxLength": 12, "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate"]}, "TaxRecordDetails2": {"type": "object", "description": "Provides information on the individual tax amount(s) per period of the tax record.", "additionalProperties": false, "properties": {"Prd": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2", "name": "Period", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd"]}, "Amt": {"description": "Underlying tax amount related to the specified period.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "Amount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt"]}}, "required": ["Amt"]}, "Garnishment3__1": {"type": "object", "description": "Provides remittance information about a payment for garnishment-related purposes.", "additionalProperties": false, "properties": {"Tp": {"description": "Specifies the type of garnishment.", "$ref": "#/definitions/GarnishmentType1__1", "name": "Type", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp"]}, "Grnshee": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the garnisher.", "$ref": "#/definitions/PartyIdentification135__4", "name": "Garnishee", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-G<PERSON>hee"]}, "GrnshmtAdmstr": {"description": "Party on the credit side of the transaction who administers the garnishment on behalf of the ultimate beneficiary.", "$ref": "#/definitions/PartyIdentification135__4", "name": "GarnishmentAdministrator", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr"]}, "RefNb": {"description": "Reference information that is specific to the agency receiving the garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "ReferenceNumber", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb"]}, "Dt": {"description": "Date of payment which garnishment was taken from.", "$ref": "#/definitions/ISODate", "name": "Date", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt"]}, "RmtdAmt": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount", "name": "RemittedAmount", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt"]}, "FmlyMdclInsrncInd": {"description": "Indicates if the person to whom the garnishment applies (that is, the ultimate debtor) has family medical insurance coverage available.", "$ref": "#/definitions/TrueFalseIndicator", "name": "FamilyMedicalInsuranceIndicator", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd"]}, "MplyeeTermntnInd": {"description": "Indicates if the employment of the person to whom the garnishment applies (that is, the ultimate debtor) has been terminated.", "$ref": "#/definitions/TrueFalseIndicator", "name": "EmployeeTerminationIndicator", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd"]}}, "required": ["Tp"]}, "GarnishmentType1__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": false, "properties": {"CdOrPrtry": {"description": "Provides the type details of the garnishment.", "$ref": "#/definitions/GarnishmentType1Choice__1", "name": "CodeOrProprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry"]}, "Issr": {"description": "Identification of the issuer of the garnishment type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Issuer", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr"]}}, "required": ["CdOrPrtry"]}, "GarnishmentType1Choice__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Garnishment type in a coded form. Would suggest this to be an External Code List to contain: GNCS    Garnishment from a third party payer for Child Support GNDP    Garnishment from a Direct Payer for Child Support GTPP     Garnishment from a third party payer to taxing agency.", "$ref": "#/definitions/ExternalGarnishmentType1Code", "name": "Code", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Proprietary identification of the type of garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "Proprietary", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalGarnishmentType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the garnishment type as published in an external document type code list.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd"]}, "TrueFalseIndicator": {"type": "boolean", "description": "A flag indicating a True or False value.", "nestedFieldNames": ["Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd", "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd"]}}}