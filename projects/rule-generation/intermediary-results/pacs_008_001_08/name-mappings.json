{"fullName": "Document", "abbrName": "Document", "nestedAbbrName": "Document", "isArray": false, "children": [{"fullName": "FIToFICustomerCreditTransferV08", "abbrName": "FIToFICstmrCdtTrf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf", "isArray": false, "children": [{"fullName": "GroupHeader", "abbrName": "GrpHdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr", "isArray": false, "children": [{"fullName": "MessageIdentification", "abbrName": "MsgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-MsgId", "isArray": false, "children": []}, {"fullName": "CreationDateTime", "abbrName": "CreDtTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-CreDtTm", "isArray": false, "children": []}, {"fullName": "NumberOfTransactions", "abbrName": "NbOfTxs", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-NbOfTxs", "isArray": false, "children": []}, {"fullName": "SettlementInformation", "abbrName": "SttlmInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf", "isArray": false, "children": [{"fullName": "SettlementMethod", "abbrName": "SttlmMtd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd", "isArray": false, "children": []}, {"fullName": "SettlementAccount", "abbrName": "SttlmAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "InstructingReimbursementAgent", "abbrName": "InstgRmbrsmntAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "InstructingReimbursementAgentAccount", "abbrName": "InstgRmbrsmntAgtAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "InstructedReimbursementAgent", "abbrName": "InstdRmbrsmntAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "InstructedReimbursementAgentAccount", "abbrName": "InstdRmbrsmntAgtAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "ThirdReimbursementAgent", "abbrName": "ThrdRmbrsmntAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "ThirdReimbursementAgentAccount", "abbrName": "ThrdRmbrsmntAgtAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}]}]}, {"fullName": "CreditTransferTransactionInformation", "abbrName": "CdtTrfTxInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf", "isArray": false, "children": [{"fullName": "PaymentIdentification", "abbrName": "PmtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId", "isArray": false, "children": [{"fullName": "InstructionIdentification", "abbrName": "InstrId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId", "isArray": false, "children": []}, {"fullName": "EndToEndIdentification", "abbrName": "EndToEndId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId", "isArray": false, "children": []}, {"fullName": "TransactionIdentification", "abbrName": "TxId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId", "isArray": false, "children": []}, {"fullName": "UETR", "abbrName": "UETR", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR", "isArray": false, "children": []}, {"fullName": "ClearingSystemReference", "abbrName": "ClrSysRef", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef", "isArray": false, "children": []}]}, {"fullName": "PaymentTypeInformation", "abbrName": "PmtTpInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf", "isArray": false, "children": [{"fullName": "InstructionPriority", "abbrName": "InstrPrty", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty", "isArray": false, "children": []}, {"fullName": "ClearingChannel", "abbrName": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl", "isArray": false, "children": []}, {"fullName": "ServiceLevel", "abbrName": "SvcLvl", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl", "isArray": true, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "isArray": false, "children": []}]}, {"fullName": "LocalInstrument", "abbrName": "LclInstrm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "isArray": false, "children": []}]}, {"fullName": "CategoryPurpose", "abbrName": "CtgyPurp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "isArray": false, "children": []}]}]}, {"fullName": "InterbankSettlementAmount", "abbrName": "IntrBkSttlmAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "InterbankSettlementDate", "abbrName": "IntrBkSttlmDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt", "isArray": false, "children": []}, {"fullName": "SettlementPriority", "abbrName": "SttlmPrty", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty", "isArray": false, "children": []}, {"fullName": "SettlementTimeIndication", "abbrName": "SttlmTmIndctn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn", "isArray": false, "children": [{"fullName": "DebitDateTime", "abbrName": "DbtDtTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "isArray": false, "children": []}, {"fullName": "CreditDateTime", "abbrName": "CdtDtTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm", "isArray": false, "children": []}]}, {"fullName": "SettlementTimeRequest", "abbrName": "SttlmTmReq", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq", "isArray": false, "children": [{"fullName": "CLSTime", "abbrName": "CLSTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm", "isArray": false, "children": []}, {"fullName": "TillTime", "abbrName": "TillTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm", "isArray": false, "children": []}, {"fullName": "FromTime", "abbrName": "FrTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm", "isArray": false, "children": []}, {"fullName": "RejectTime", "abbrName": "RjctTm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm", "isArray": false, "children": []}]}, {"fullName": "InstructedAmount", "abbrName": "InstdAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "ExchangeRate", "abbrName": "XchgRate", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "ChrgBr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr", "isArray": false, "children": []}, {"fullName": "ChargesInformation", "abbrName": "ChrgsInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf", "isArray": true, "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy", "isArray": false, "children": []}]}, {"fullName": "Agent", "abbrName": "Agt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}]}, {"fullName": "PreviousInstructingAgent1", "abbrName": "PrvsInstgAgt1", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent1Account", "abbrName": "PrvsInstgAgt1Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "PreviousInstructingAgent2", "abbrName": "PrvsInstgAgt2", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent2Account", "abbrName": "PrvsInstgAgt2Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "PreviousInstructingAgent3", "abbrName": "PrvsInstgAgt3", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent3Account", "abbrName": "PrvsInstgAgt3Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "InstructingAgent", "abbrName": "InstgAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}, {"fullName": "InstructedAgent", "abbrName": "InstdAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent1", "abbrName": "IntrmyAgt1", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent1Account", "abbrName": "IntrmyAgt1Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent2", "abbrName": "IntrmyAgt2", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent2Account", "abbrName": "IntrmyAgt2Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent3", "abbrName": "IntrmyAgt3", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent3Account", "abbrName": "IntrmyAgt3Acct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "UltimateDebtor", "abbrName": "UltmtDbtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "Initiating<PERSON><PERSON><PERSON>", "abbrName": "InitgPty", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "Debtor", "abbrName": "Dbtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "DebtorAccount", "abbrName": "DbtrAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "DebtorAgent", "abbrName": "DbtrAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "DebtorAgentAccount", "abbrName": "DbtrAgtAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "CreditorAgent", "abbrName": "CdtrAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}, {"fullName": "BranchIdentification", "abbrName": "BrnchId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id", "isArray": false, "children": []}]}]}, {"fullName": "CreditorAgentAccount", "abbrName": "CdtrAgtAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "Creditor", "abbrName": "Cdtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "CreditorAccount", "abbrName": "CdtrAcct", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "UltimateCreditor", "abbrName": "UltmtCdtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "InstructionForCreditorAgent", "abbrName": "InstrForCdtrAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt", "isArray": true, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd", "isArray": false, "children": []}, {"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf", "isArray": false, "children": []}]}, {"fullName": "InstructionForNextAgent", "abbrName": "InstrForNxtAgt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt", "isArray": true, "children": [{"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf", "isArray": false, "children": []}]}, {"fullName": "Purpose", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry", "isArray": false, "children": []}]}, {"fullName": "RegulatoryReporting", "abbrName": "RgltryRptg", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg", "isArray": true, "children": [{"fullName": "DebitCreditReportingIndicator", "abbrName": "DbtCdtRptgInd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd", "isArray": false, "children": []}, {"fullName": "Authority", "abbrName": "<PERSON><PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry", "isArray": false, "children": []}]}, {"fullName": "Details", "abbrName": "Dtls", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp", "isArray": false, "children": []}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry", "isArray": false, "children": []}, {"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd", "isArray": false, "children": []}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy", "isArray": false, "children": []}]}, {"fullName": "Information", "abbrName": "Inf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf", "isArray": true, "children": []}]}]}, {"fullName": "RelatedRemittanceInformation", "abbrName": "RltdRmtInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf", "isArray": false, "children": [{"fullName": "RemittanceIdentification", "abbrName": "RmtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId", "isArray": false, "children": []}, {"fullName": "RemittanceLocationDetails", "abbrName": "RmtLctnDtls", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls", "isArray": true, "children": [{"fullName": "Method", "abbrName": "Mtd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd", "isArray": false, "children": []}, {"fullName": "ElectronicAddress", "abbrName": "ElctrncAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm", "isArray": false, "children": []}, {"fullName": "Address", "abbrName": "<PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine", "isArray": true, "children": []}]}]}]}]}, {"fullName": "RemittanceInformation", "abbrName": "RmtInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf", "isArray": false, "children": [{"fullName": "Unstructured", "abbrName": "Ustrd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd", "isArray": false, "children": []}, {"fullName": "Structured", "abbrName": "Strd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd", "isArray": true, "children": [{"fullName": "ReferredDocumentInformation", "abbrName": "RfrdDocInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp", "isArray": false, "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr", "isArray": false, "children": []}]}, {"fullName": "Number", "abbrName": "Nb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb", "isArray": false, "children": []}, {"fullName": "RelatedDate", "abbrName": "RltdDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt", "isArray": false, "children": []}, {"fullName": "LineDetails", "abbrName": "LineDtls", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp", "isArray": false, "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr", "isArray": false, "children": []}]}, {"fullName": "Number", "abbrName": "Nb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb", "isArray": false, "children": []}, {"fullName": "RelatedDate", "abbrName": "RltdDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt", "isArray": false, "children": []}]}, {"fullName": "Description", "abbrName": "Desc", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc", "isArray": false, "children": []}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt", "isArray": false, "children": [{"fullName": "DuePayableAmount", "abbrName": "DuePyblAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "DiscountAppliedAmount", "abbrName": "DscntApldAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "isArray": false, "children": []}]}]}, {"fullName": "CreditNoteAmount", "abbrName": "CdtNoteAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "isArray": false, "children": []}]}]}, {"fullName": "AdjustmentAmountAndReason", "abbrName": "AdjstmntAmtAndRsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn", "isArray": true, "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "isArray": false, "children": []}]}, {"fullName": "CreditDebitIndicator", "abbrName": "CdtDbtInd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "isArray": false, "children": []}, {"fullName": "Reason", "abbrName": "Rsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "isArray": false, "children": []}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "isArray": false, "children": []}]}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "isArray": false, "children": []}]}]}]}]}, {"fullName": "ReferredDocumentAmount", "abbrName": "RfrdDocAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt", "isArray": false, "children": [{"fullName": "DuePayableAmount", "abbrName": "DuePyblAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "DiscountAppliedAmount", "abbrName": "DscntApldAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "isArray": false, "children": []}]}]}, {"fullName": "CreditNoteAmount", "abbrName": "CdtNoteAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "isArray": false, "children": []}]}]}, {"fullName": "AdjustmentAmountAndReason", "abbrName": "AdjstmntAmtAndRsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn", "isArray": true, "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "isArray": false, "children": []}]}, {"fullName": "CreditDebitIndicator", "abbrName": "CdtDbtInd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd", "isArray": false, "children": []}, {"fullName": "Reason", "abbrName": "Rsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn", "isArray": false, "children": []}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf", "isArray": false, "children": []}]}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "isArray": false, "children": []}]}]}, {"fullName": "CreditorReferenceInformation", "abbrName": "CdtrRefInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp", "isArray": false, "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr", "isArray": false, "children": []}]}, {"fullName": "Reference", "abbrName": "Ref", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref", "isArray": false, "children": []}]}, {"fullName": "Invoicer", "abbrName": "Invcr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "Invoicee", "abbrName": "Invcee", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "TaxRemittance", "abbrName": "TaxRmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt", "isArray": false, "children": [{"fullName": "Creditor", "abbrName": "Cdtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr", "isArray": false, "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId", "isArray": false, "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId", "isArray": false, "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp", "isArray": false, "children": []}]}, {"fullName": "Debtor", "abbrName": "Dbtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr", "isArray": false, "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "isArray": false, "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "isArray": false, "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "isArray": false, "children": []}, {"fullName": "Authorisation", "abbrName": "Authstn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn", "isArray": false, "children": [{"fullName": "Title", "abbrName": "Titl", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "isArray": false, "children": []}]}]}, {"fullName": "UltimateDebtor", "abbrName": "UltmtDbtr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr", "isArray": false, "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId", "isArray": false, "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId", "isArray": false, "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp", "isArray": false, "children": []}, {"fullName": "Authorisation", "abbrName": "Authstn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn", "isArray": false, "children": [{"fullName": "Title", "abbrName": "Titl", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm", "isArray": false, "children": []}]}]}, {"fullName": "AdministrationZone", "abbrName": "AdmstnZone", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone", "isArray": false, "children": []}, {"fullName": "ReferenceNumber", "abbrName": "RefNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb", "isArray": false, "children": []}, {"fullName": "Method", "abbrName": "Mtd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd", "isArray": false, "children": []}, {"fullName": "TotalTaxableBaseAmount", "abbrName": "TtlTaxblBaseAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "TotalTaxAmount", "abbrName": "TtlTaxAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt", "isArray": false, "children": []}, {"fullName": "SequenceNumber", "abbrName": "SeqNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb", "isArray": false, "children": []}, {"fullName": "Record", "abbrName": "Rcrd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd", "isArray": true, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp", "isArray": false, "children": []}, {"fullName": "Category", "abbrName": "Ctgy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy", "isArray": false, "children": []}, {"fullName": "CategoryDetails", "abbrName": "CtgyDtls", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls", "isArray": false, "children": []}, {"fullName": "DebtorS<PERSON>us", "abbrName": "DbtrSts", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts", "isArray": false, "children": []}, {"fullName": "CertificateIdentification", "abbrName": "CertId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId", "isArray": false, "children": []}, {"fullName": "FormsCode", "abbrName": "FrmsCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd", "isArray": false, "children": []}, {"fullName": "Period", "abbrName": "Prd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd", "isArray": false, "children": [{"fullName": "Year", "abbrName": "Yr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "isArray": false, "children": []}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "isArray": false, "children": []}, {"fullName": "FromToDate", "abbrName": "FrToDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt", "isArray": false, "children": [{"fullName": "FromDate", "abbrName": "FrDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "isArray": false, "children": []}, {"fullName": "ToDate", "abbrName": "ToDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "isArray": false, "children": []}]}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt", "isArray": false, "children": [{"fullName": "Rate", "abbrName": "Rate", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate", "isArray": false, "children": []}, {"fullName": "TaxableBaseAmount", "abbrName": "TaxblBaseAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "TotalAmount", "abbrName": "TtlAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "Details", "abbrName": "Dtls", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls", "isArray": true, "children": [{"fullName": "Period", "abbrName": "Prd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd", "isArray": false, "children": [{"fullName": "Year", "abbrName": "Yr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr", "isArray": false, "children": []}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp", "isArray": false, "children": []}, {"fullName": "FromToDate", "abbrName": "FrToDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt", "isArray": false, "children": [{"fullName": "FromDate", "abbrName": "FrDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt", "isArray": false, "children": []}, {"fullName": "ToDate", "abbrName": "ToDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt", "isArray": false, "children": []}]}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "isArray": false, "children": []}]}]}]}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf", "isArray": false, "children": []}]}]}, {"fullName": "GarnishmentRemittance", "abbrName": "GrnshmtRmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp", "isArray": false, "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr", "isArray": false, "children": []}]}, {"fullName": "Garnishee", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-G<PERSON>hee", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "GarnishmentAdministrator", "abbrName": "GrnshmtAdmstr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr", "isArray": false, "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine", "isArray": true, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id", "isArray": false, "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId", "isArray": false, "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC", "isArray": false, "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId", "isArray": false, "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth", "isArray": false, "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "isArray": false, "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "isArray": false, "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "isArray": false, "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "isArray": false, "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr", "isArray": true, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr", "isArray": false, "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes", "isArray": false, "children": []}]}, {"fullName": "ReferenceNumber", "abbrName": "RefNb", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb", "isArray": false, "children": []}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt", "isArray": false, "children": []}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "FamilyMedicalInsuranceIndicator", "abbrName": "FmlyMdclInsrncInd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd", "isArray": false, "children": []}, {"fullName": "EmployeeTerminationIndicator", "abbrName": "MplyeeTermntnInd", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd", "isArray": false, "children": []}]}, {"fullName": "AdditionalRemittanceInformation", "abbrName": "AddtlRmtInf", "nestedAbbrName": "Document-FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf", "isArray": true, "children": []}]}]}]}]}]}