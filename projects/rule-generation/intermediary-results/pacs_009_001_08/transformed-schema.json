{"$comment": {"legalNotices": "SWIFT SCRL@2023. All rights reserved.\n\nThis schema is a component of MyStandards, the SWIFT collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with SWIFT SCRL, the user has no right to:\n - authorise external end users to use this component for other purposes than their internal use.\n - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n - re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual \nproperty rights of whatever nature in this component will remain the exclusive property of SWIFT or its \nlicensors.\n\nTrademarks\nSWIFT is the trade name of S.W.I.F.T. SCRL.\nThe following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.009.001.08_FinancialInstitutionCreditTransfer", "baseMessage": "pacs.009.001.08", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0rCoJpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1. AGENTS IDENTIFICATION - Textual Rules\r\n\r\n-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n\r\n2. Single transactions only are allowed.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema FinancialInstitutionCreditTransferV08 (pacs.009.001.08) Generated by SWIFT MyStandards 2025-04-20 02:18:07", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.009.001.08"}, "FICdtTrf": {"$ref": "#/definitions/FinancialInstitutionCreditTransferV08", "name": "FinancialInstitutionCreditTransferV08", "nestedFieldNames": ["Document-FICdtTrf"]}}, "required": ["FICdtTrf"], "definitions": {"FinancialInstitutionCreditTransferV08": {"type": "object", "description": "Scope\r\nThe FinancialInstitutionCreditTransfer message is sent by a debtor financial institution to a creditor financial institution, directly or through other agents and/or a payment clearing and settlement system.\r\nIt is used to move funds from a debtor account to a creditor, where both debtor and creditor are financial institutions.\r\nUsage\r\nThe FinancialInstitutionCreditTransfer message is exchanged between agents and can contain one or more credit transfer instructions where debtor and creditor are both financial institutions.\r\nThe FinancialInstitutionCreditTransfer message does not allow for grouping: a CreditTransferTransactionInformation block must be present for each credit transfer transaction.\r\nThe FinancialInstitutionCreditTransfer message can be used in domestic and cross-border scenarios.", "additionalProperties": false, "properties": {"GrpHdr": {"description": "Set of characteristics shared by all individual transactions included in the message.", "$ref": "#/definitions/GroupHeader93__1", "name": "GroupHeader", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr"]}, "CdtTrfTxInf": {"description": "Set of elements providing information specific to the individual credit transfer(s).", "$ref": "#/definitions/CreditTransferTransaction36__1", "name": "CreditTransferTransactionInformation", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf"]}}, "required": ["GrpHdr", "CdtTrfTxInf"]}, "GroupHeader93__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"MsgId": {"description": "Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "MessageIdentification", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-MsgId"]}, "CreDtTm": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime", "name": "CreationDateTime", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-CreDtTm"]}, "NbOfTxs": {"description": "Number of individual transactions contained in the message.", "$ref": "#/definitions/Max15NumericText_fixed", "name": "NumberOfTransactions", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-NbOfTxs"]}, "SttlmInf": {"description": "Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.", "$ref": "#/definitions/SettlementInstruction7__1", "name": "SettlementInformation", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf"]}}, "required": ["MsgId", "CreDtTm", "NbOfTxs", "SttlmInf"]}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-MsgId", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId", "Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId", "Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf", "Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-CreDtTm", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"]}, "Max15NumericText_fixed": {"type": "string", "description": "\n*`1`-null", "enum": ["1"], "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-NbOfTxs"]}, "SettlementInstruction7__1": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"SttlmMtd": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__1", "name": "SettlementMethod", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd"]}, "SttlmAcct": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "SettlementAccount", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct"]}}, "required": ["SttlmMtd"]}, "SettlementMethod1Code__1": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.", "enum": ["INDA", "INGA"], "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd"]}, "CashAccount38__1": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"Id": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1", "name": "Identification", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id"]}, "Tp": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1", "name": "Type", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp"]}, "Ccy": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy"]}, "Nm": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text", "name": "Name", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm"]}, "Prxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1", "name": "Proxy", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy"]}}, "required": ["Id"]}, "AccountIdentification4Choice__1": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"IBAN": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier", "name": "IBAN", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"]}}, "required": ["IBAN"]}, {"type": "object", "additionalProperties": false, "properties": {"Othr": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1__1", "name": "Other", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "IBAN2007Identifier": {"type": "string", "description": "The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard \"Banking and related financial services - International Bank Account Number (IBAN)\" and replaced by the more recent edition of the standard.", "pattern": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"]}, "GenericAccountIdentification1__1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"Id": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax34Text", "name": "Identification", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"]}, "SchmeNm": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice__1", "name": "SchemeName", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm"]}, "Issr": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Issuer", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr"]}}, "required": ["Id"]}, "CBPR_RestrictedFINXMax34Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash \"/\" at the beginning and end of line and double slash \"//\" within the line.", "minLength": 1, "maxLength": 34, "pattern": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"]}, "AccountSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalAccountIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"]}, "CashAccountType2Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCashAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"]}, "ActiveOrHistoricCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy"]}, "CBPR_RestrictedFINXMax70Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm"]}, "ProxyAccountIdentification1__1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"Tp": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice__1", "name": "Type", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp"]}, "Id": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/CBPR_RestrictedFINXMax320Text_Extended", "name": "Identification", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"]}}, "required": ["Id"]}, "ProxyAccountType1Choice__1": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalProxyAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external proxy account type code, as published in the proxy account type external code set.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"]}, "CBPR_RestrictedFINXMax320Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]\r\n", "minLength": 1, "maxLength": 320, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"]}, "CreditTransferTransaction36__1": {"type": "object", "description": "Provide further details specific to the individual transaction(s) included in the message.", "additionalProperties": false, "properties": {"PmtId": {"description": "Set of elements used to reference a payment instruction.", "$ref": "#/definitions/PaymentIdentification7__1", "name": "PaymentIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId"]}, "PmtTpInf": {"description": "Set of elements used to further specify the type of transaction.", "$ref": "#/definitions/PaymentTypeInformation28__1", "name": "PaymentTypeInformation", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf"]}, "IntrBkSttlmAmt": {"description": "Amount of money moved between the instructing agent and the instructed agent.", "$ref": "#/definitions/CBPR_Amount", "name": "InterbankSettlementAmount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt"]}, "IntrBkSttlmDt": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.", "$ref": "#/definitions/ISODate", "name": "InterbankSettlementDate", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt"]}, "SttlmPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.", "$ref": "#/definitions/Priority3Code", "name": "SettlementPriority", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmPrty"]}, "SttlmTmIndctn": {"description": "Provides information on the occurred settlement time(s) of the payment transaction.", "$ref": "#/definitions/SettlementDateTimeIndication1__1", "name": "SettlementTimeIndication", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn"]}, "SttlmTmReq": {"description": "Provides information on the requested settlement time(s) of the payment instruction.", "$ref": "#/definitions/SettlementTimeRequest2__1", "name": "SettlementTimeRequest", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq"]}, "PrvsInstgAgt1": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent1", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1"]}, "PrvsInstgAgt1Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent1Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct"]}, "PrvsInstgAgt2": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent2", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2"]}, "PrvsInstgAgt2Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent2Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct"]}, "PrvsInstgAgt3": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "PreviousInstructingAgent3", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3"]}, "PrvsInstgAgt3Acct": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "PreviousInstructingAgent3Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct"]}, "InstgAgt": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructingAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstgAgt"]}, "InstdAgt": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2", "name": "InstructedAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstdAgt"]}, "IntrmyAgt1": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent1", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1"]}, "IntrmyAgt1Acct": {"description": "Unambiguous identification of the account of the intermediary agent 1 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent1Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct"]}, "IntrmyAgt2": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent2", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2"]}, "IntrmyAgt2Acct": {"description": "Unambiguous identification of the account of the intermediary agent 2 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent2Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct"]}, "IntrmyAgt3": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "IntermediaryAgent3", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3"]}, "IntrmyAgt3Acct": {"description": "Unambiguous identification of the account of the intermediary agent 3 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "IntermediaryAgent3Account", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct"]}, "Dbtr": {"description": "Financial institution that owes an amount of money to the (ultimate) financial institutional creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Debtor", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Dbtr"]}, "DbtrAcct": {"description": "Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAccount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-DbtrAcct"]}, "DbtrAgt": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "DebtorAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-DbtrAgt"]}, "DbtrAgtAcct": {"description": "Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1", "name": "DebtorAgentAccount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct"]}, "CdtrAgt": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "CreditorAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-CdtrAgt"]}, "CdtrAgtAcct": {"description": "Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAgentAccount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct"]}, "Cdtr": {"description": "Financial institution that receives an amount of money from the financial institutional debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1", "name": "Creditor", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Cdtr"]}, "CdtrAcct": {"description": "Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1", "name": "CreditorAccount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-CdtrAcct"]}, "InstrForCdtrAgt": {"type": "array", "maxItems": 2, "description": "Further information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor agent.", "items": {"$ref": "#/definitions/InstructionForCreditorAgent2__1"}, "name": "InstructionForCreditorAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt"]}, "InstrForNxtAgt": {"type": "array", "maxItems": 6, "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the next agent.   Usage: The next agent may not be the creditor agent. The instruction can relate to a level of service, can be an instruction that has to be executed by the agent, or can be information required by the next agent.", "items": {"$ref": "#/definitions/InstructionForNextAgent1__1"}, "name": "InstructionForNextAgent", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt"]}, "Purp": {"description": "Underlying reason for the payment transaction. Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/Purpose2Choice__1", "name": "Purpose", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Purp"]}, "RmtInf": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.", "$ref": "#/definitions/RemittanceInformation2__1", "name": "RemittanceInformation", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-RmtInf"]}}, "required": ["PmtId", "IntrBkSttlmAmt", "IntrBkSttlmDt", "InstgAgt", "InstdAgt", "Dbtr", "Cdtr"]}, "PaymentIdentification7__1": {"type": "object", "description": "Provides further means of referencing a payment transaction.", "additionalProperties": false, "properties": {"InstrId": {"description": "Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the instruction.  Usage: The instruction identification is a point to point reference that can be used between the instructing party and the instructed party to refer to the individual instruction. It can be included in several messages related to the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text", "name": "InstructionIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId"]}, "EndToEndId": {"description": "Unique identification, as assigned by the initiating party, to unambiguously identify the transaction. This identification is passed on, unchanged, throughout the entire end-to-end chain.  Usage: The end-to-end identification can be used for reconciliation or to link tasks relating to the transaction. It can be included in several messages related to the transaction.  Usage: In case there are technical limitations to pass on multiple references, the end-to-end identification must be passed on throughout the entire end-to-end chain.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "EndToEndIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId"]}, "TxId": {"description": "Unique identification, as assigned by the first instructing agent, to unambiguously identify the transaction that is passed on, unchanged, throughout the entire interbank chain.  Usage: The transaction identification can be used for reconciliation, tracking or to link tasks relating to the transaction on the interbank level.  Usage: The instructing agent has to make sure that the transaction identification is unique for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "TransactionIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId"]}, "UETR": {"description": "Universally unique identifier to provide an end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier", "name": "UETR", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR"]}, "ClrSysRef": {"description": "Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "ClearingSystemReference", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef"]}}, "required": ["InstrId", "EndToEndId", "UETR"]}, "CBPR_RestrictedFINXMax16Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId"]}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR"]}, "PaymentTypeInformation28__1": {"type": "object", "description": "Provides further details of the type of payment.", "additionalProperties": false, "properties": {"InstrPrty": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.", "$ref": "#/definitions/Priority2Code", "name": "InstructionPriority", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"]}, "ClrChanl": {"description": "Specifies the clearing channel to be used to process the payment instruction.", "$ref": "#/definitions/ClearingChannel2Code", "name": "ClearingChannel", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"]}, "SvcLvl": {"type": "array", "maxItems": 3, "description": "Agreement under which or rules under which the transaction should be processed.", "items": {"$ref": "#/definitions/ServiceLevel8Choice__1"}, "name": "ServiceLevel", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl"]}, "LclInstrm": {"description": "User community specific instrument.  Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.", "$ref": "#/definitions/LocalInstrument2Choice__1", "name": "LocalInstrument", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm"]}, "CtgyPurp": {"description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories. Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/CategoryPurpose1Choice__1", "name": "CategoryPurpose", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp"]}}}, "Priority2Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["HIGH", "NORM"], "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"]}, "ClearingChannel2Code": {"type": "string", "description": "Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.\n*`RTGS`-Clearing channel is a real-time gross settlement system.\n*`RTNS`-Clearing channel is a real-time net settlement system.\n*`MPNS`-Clearing channel is a mass payment net settlement system.\n*`BOOK`-Payment through internal book transfer.", "enum": ["RTGS", "RTNS", "MPNS", "BOOK"], "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"]}, "ServiceLevel8Choice__1": {"type": "object", "description": "Specifies the service level of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.", "$ref": "#/definitions/ExternalServiceLevel1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies a pre-agreed service or level of service between the parties, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalServiceLevel1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external service level code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"]}, "LocalInstrument2Choice__1": {"type": "object", "description": "Set of elements that further identifies the type of local instruments being requested by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Specifies the local instrument, as published in an external local instrument code list.", "$ref": "#/definitions/ExternalLocalInstrument1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Specifies the local instrument, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalLocalInstrument1Code": {"type": "string", "minLength": 1, "maxLength": 35, "description": "Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"]}, "CategoryPurpose1Choice__1": {"type": "object", "description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories.\nUsage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Category purpose, as published in an external category purpose code list.", "$ref": "#/definitions/ExternalCategoryPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Category purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalCategoryPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the category purpose, as published in an external category purpose code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"]}, "CBPR_Amount": {"type": "object", "additionalProperties": false, "properties": {"Ccy": {"$ref": "#/definitions/ActiveCurrencyCode", "name": "<PERSON><PERSON><PERSON><PERSON>", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy"]}, "amount": {"type": "string", "maxLength": 15, "pattern": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$", "name": "amount", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount"]}}, "required": ["Ccy", "amount"]}, "ActiveCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\".", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy"]}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt"]}, "Priority3Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`URGT`-Priority level is urgent (highest priority possible).\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["URGT", "HIGH", "NORM"], "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmPrty"]}, "SettlementDateTimeIndication1__1": {"type": "object", "description": "Information on the occurred settlement time(s) of the payment transaction.", "additionalProperties": false, "properties": {"DbtDtTm": {"description": "Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "DebitDateTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm"]}, "CdtDtTm": {"description": "Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime", "name": "CreditDateTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"]}}}, "SettlementTimeRequest2__1": {"type": "object", "description": "Provides information on the requested settlement time(s) of the payment instruction.", "additionalProperties": false, "properties": {"CLSTm": {"description": "Time by which the amount of money must be credited, with confirmation, to the CLS Bank's account at the central bank. Usage: Time must be expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_Time", "name": "CLSTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm"]}, "TillTm": {"description": "Time until when the payment may be settled.", "$ref": "#/definitions/CBPR_Time", "name": "TillTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm"]}, "FrTm": {"description": "Time as from when the payment may be settled.", "$ref": "#/definitions/CBPR_Time", "name": "FromTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm"]}, "RjctTm": {"description": "Time by when the payment must be settled to avoid rejection.", "$ref": "#/definitions/CBPR_Time", "name": "RejectTime", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"]}}}, "CBPR_Time": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory time component, expressed in local time with UTC offset format (hh:mm:ss.sss+/-hh:mm). \r\n\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 19:20:30.45+01:00", "pattern": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm", "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"]}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI"]}, "Nm": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Name", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm"]}, "PstlAdr": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1", "name": "PostalAddress", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr"]}}}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI"]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"ClrSysId": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1", "name": "ClearingSystemIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId"]}, "MmbId": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text", "name": "MemberIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId"]}}, "required": ["ClrSysId", "MmbId"]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": false, "properties": {"Cd": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}}, "required": ["Cd"]}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"]}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId"]}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI"]}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm", "Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd"]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"Dept": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Department", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept"]}, "SubDept": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "SubDepartment", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept"]}, "StrtNm": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "StreetName", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm"]}, "BldgNb": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "BuildingNumber", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb"]}, "BldgNm": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "BuildingName", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm"]}, "Flr": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Floor", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr"]}, "PstBx": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostBox", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx"]}, "Room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended", "name": "Room", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room"]}, "PstCd": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended", "name": "PostCode", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd"]}, "TwnNm": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownName", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm"]}, "TwnLctnNm": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "TownLocationName", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm"]}, "DstrctNm": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "DistrictName", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm"]}, "CtrySubDvsn": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended", "name": "CountrySubDivision", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn"]}, "Ctry": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode", "name": "Country", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry"]}, "AdrLine": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "name": "AddressLine", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine"]}}}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine"]}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd"]}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry"]}, "BranchAndFinancialInstitutionIdentification6__2": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"FinInstnId": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__2", "name": "FinancialInstitutionIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId"]}}, "required": ["FinInstnId"]}, "FinancialInstitutionIdentification18__2": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"BICFI": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier", "name": "BICFI", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI"]}, "ClrSysMmbId": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1", "name": "ClearingSystemMemberIdentification", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId"]}, "LEI": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier", "name": "LEI", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI"]}}, "required": ["BICFI"]}, "InstructionForCreditorAgent2__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the creditor's agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the creditor's agent.", "additionalProperties": false, "properties": {"Cd": {"description": "Coded information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor's agent.", "$ref": "#/definitions/Instruction5Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"]}, "InstrInf": {"description": "Further information complementing the coded instruction or instruction to the creditor's agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text", "name": "InstructionInformation", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf"]}}}, "Instruction5Code": {"type": "string", "description": "Specifies further instructions concerning the processing of a payment instruction, provided by the sending clearing agent to the next agent(s).\n*`PHOB`-Please advise/contact (ultimate) creditor/claimant by phone.\n*`TELB`-Please advise/contact (ultimate) creditor/claimant by the most efficient means of telecommunication.", "enum": ["PHOB", "TELB"], "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"]}, "CBPR_RestrictedFINXMax140Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf"]}, "InstructionForNextAgent1__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by an other agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the other agent.", "additionalProperties": false, "properties": {"InstrInf": {"description": "Further information complementing the coded instruction or instruction to the next agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "InstructionInformation", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf"]}}}, "Purpose2Choice__1": {"type": "object", "description": "Specifies the underlying reason for the payment transaction.\nUsage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"Cd": {"description": "Underlying reason for the payment transaction, as published in an external purpose code list.", "$ref": "#/definitions/ExternalPurpose1Code", "name": "Code", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Purp-Cd"]}}, "required": ["Cd"]}, {"type": "object", "additionalProperties": false, "properties": {"Prtry": {"description": "Purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text", "name": "Proprietary", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry"]}}, "required": ["<PERSON><PERSON><PERSON>"]}]}, "ExternalPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external purpose code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org.", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-Purp-Cd"]}, "RemittanceInformation2__1": {"type": "object", "description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle.", "additionalProperties": false, "properties": {"Ustrd": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, for example, commercial invoices in an accounts' receivable system in an unstructured form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended", "name": "Unstructured", "nestedFieldNames": ["Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd"]}}}}}