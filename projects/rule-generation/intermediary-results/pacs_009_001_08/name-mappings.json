{"fullName": "Document", "abbrName": "Document", "nestedAbbrName": "Document", "isArray": false, "children": [{"fullName": "FinancialInstitutionCreditTransferV08", "abbrName": "FICdtTrf", "nestedAbbrName": "Document-FICdtTrf", "isArray": false, "children": [{"fullName": "GroupHeader", "abbrName": "GrpHdr", "nestedAbbrName": "Document-FICdtTrf-GrpHdr", "isArray": false, "children": [{"fullName": "MessageIdentification", "abbrName": "MsgId", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-MsgId", "isArray": false, "children": []}, {"fullName": "CreationDateTime", "abbrName": "CreDtTm", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-CreDtTm", "isArray": false, "children": []}, {"fullName": "NumberOfTransactions", "abbrName": "NbOfTxs", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-NbOfTxs", "isArray": false, "children": []}, {"fullName": "SettlementInformation", "abbrName": "SttlmInf", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf", "isArray": false, "children": [{"fullName": "SettlementMethod", "abbrName": "SttlmMtd", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd", "isArray": false, "children": []}, {"fullName": "SettlementAccount", "abbrName": "SttlmAcct", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "isArray": false, "children": []}]}]}]}]}, {"fullName": "CreditTransferTransactionInformation", "abbrName": "CdtTrfTxInf", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf", "isArray": false, "children": [{"fullName": "PaymentIdentification", "abbrName": "PmtId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId", "isArray": false, "children": [{"fullName": "InstructionIdentification", "abbrName": "InstrId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId", "isArray": false, "children": []}, {"fullName": "EndToEndIdentification", "abbrName": "EndToEndId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId", "isArray": false, "children": []}, {"fullName": "TransactionIdentification", "abbrName": "TxId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId", "isArray": false, "children": []}, {"fullName": "UETR", "abbrName": "UETR", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR", "isArray": false, "children": []}, {"fullName": "ClearingSystemReference", "abbrName": "ClrSysRef", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef", "isArray": false, "children": []}]}, {"fullName": "PaymentTypeInformation", "abbrName": "PmtTpInf", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf", "isArray": false, "children": [{"fullName": "InstructionPriority", "abbrName": "InstrPrty", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty", "isArray": false, "children": []}, {"fullName": "ClearingChannel", "abbrName": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl", "isArray": false, "children": []}, {"fullName": "ServiceLevel", "abbrName": "SvcLvl", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl", "isArray": true, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "isArray": false, "children": []}]}, {"fullName": "LocalInstrument", "abbrName": "LclInstrm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "isArray": false, "children": []}]}, {"fullName": "CategoryPurpose", "abbrName": "CtgyPurp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "isArray": false, "children": []}]}]}, {"fullName": "InterbankSettlementAmount", "abbrName": "IntrBkSttlmAmt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt", "isArray": false, "children": [{"fullName": "amount", "abbrName": "amount", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount", "isArray": false, "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "isArray": false, "children": []}]}, {"fullName": "InterbankSettlementDate", "abbrName": "IntrBkSttlmDt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt", "isArray": false, "children": []}, {"fullName": "SettlementPriority", "abbrName": "SttlmPrty", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmPrty", "isArray": false, "children": []}, {"fullName": "SettlementTimeIndication", "abbrName": "SttlmTmIndctn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn", "isArray": false, "children": [{"fullName": "DebitDateTime", "abbrName": "DbtDtTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "isArray": false, "children": []}, {"fullName": "CreditDateTime", "abbrName": "CdtDtTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm", "isArray": false, "children": []}]}, {"fullName": "SettlementTimeRequest", "abbrName": "SttlmTmReq", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq", "isArray": false, "children": [{"fullName": "CLSTime", "abbrName": "CLSTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm", "isArray": false, "children": []}, {"fullName": "TillTime", "abbrName": "TillTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm", "isArray": false, "children": []}, {"fullName": "FromTime", "abbrName": "FrTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm", "isArray": false, "children": []}, {"fullName": "RejectTime", "abbrName": "RjctTm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm", "isArray": false, "children": []}]}, {"fullName": "PreviousInstructingAgent1", "abbrName": "PrvsInstgAgt1", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent1Account", "abbrName": "PrvsInstgAgt1Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "PreviousInstructingAgent2", "abbrName": "PrvsInstgAgt2", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent2Account", "abbrName": "PrvsInstgAgt2Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "PreviousInstructingAgent3", "abbrName": "PrvsInstgAgt3", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent3Account", "abbrName": "PrvsInstgAgt3Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "InstructingAgent", "abbrName": "InstgAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}, {"fullName": "InstructedAgent", "abbrName": "InstdAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent1", "abbrName": "IntrmyAgt1", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent1Account", "abbrName": "IntrmyAgt1Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent2", "abbrName": "IntrmyAgt2", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent2Account", "abbrName": "IntrmyAgt2Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "IntermediaryAgent3", "abbrName": "IntrmyAgt3", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "IntermediaryAgent3Account", "abbrName": "IntrmyAgt3Acct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "Debtor", "abbrName": "Dbtr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "DebtorAccount", "abbrName": "DbtrAcct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "DebtorAgent", "abbrName": "DbtrAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "DebtorAgentAccount", "abbrName": "DbtrAgtAcct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "CreditorAgent", "abbrName": "CdtrAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "CreditorAgentAccount", "abbrName": "CdtrAgtAcct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "Creditor", "abbrName": "Cdtr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr", "isArray": false, "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId", "isArray": false, "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI", "isArray": false, "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId", "isArray": false, "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "isArray": false, "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId", "isArray": false, "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm", "isArray": false, "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr", "isArray": false, "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept", "isArray": false, "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept", "isArray": false, "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm", "isArray": false, "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb", "isArray": false, "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm", "isArray": false, "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr", "isArray": false, "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx", "isArray": false, "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room", "isArray": false, "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd", "isArray": false, "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm", "isArray": false, "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm", "isArray": false, "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm", "isArray": false, "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn", "isArray": false, "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry", "isArray": false, "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine", "isArray": true, "children": []}]}]}]}, {"fullName": "CreditorAccount", "abbrName": "CdtrAcct", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id", "isArray": false, "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN", "isArray": false, "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr", "isArray": false, "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id", "isArray": false, "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "isArray": false, "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "isArray": false, "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "isArray": false, "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm", "isArray": false, "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy", "isArray": false, "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "isArray": false, "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id", "isArray": false, "children": []}]}]}, {"fullName": "InstructionForCreditorAgent", "abbrName": "InstrForCdtrAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt", "isArray": true, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd", "isArray": false, "children": []}, {"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf", "isArray": false, "children": []}]}, {"fullName": "InstructionForNextAgent", "abbrName": "InstrForNxtAgt", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt", "isArray": true, "children": [{"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf", "isArray": false, "children": []}]}, {"fullName": "Purpose", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Purp", "isArray": false, "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Purp-Cd", "isArray": false, "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry", "isArray": false, "children": []}]}, {"fullName": "RemittanceInformation", "abbrName": "RmtInf", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-RmtInf", "isArray": false, "children": [{"fullName": "Unstructured", "abbrName": "Ustrd", "nestedAbbrName": "Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd", "isArray": false, "children": []}]}]}]}]}