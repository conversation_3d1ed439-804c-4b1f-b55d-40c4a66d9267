[{"id": "generated__Document-FICdtTrf-GrpHdr-MsgId__required", "description": "GrpHdr-MsgId is required.", "descriptionTranslationProposal": "GrpHdr-MsgId ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-MsgId"}, {"id": "generated__Document-FICdtTrf-GrpHdr-MsgId__maxLength", "description": "GrpHdr-MsgId must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-MsgId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-GrpHdr-MsgId"}, {"id": "generated__Document-FICdtTrf-GrpHdr-MsgId__pattern__86207ffe", "description": "GrpHdr-MsgId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-<PERSON>gId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-MsgId"}, {"id": "generated__Document-FICdtTrf-GrpHdr-CreDtTm__pattern__e2ce1f8b", "description": "GrpHdr-CreDtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "GrpHdr-CreDtTm muss dem <PERSON> ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-GrpHdr-CreDtTm"}, {"id": "generated__Document-FICdtTrf-GrpHdr-CreDtTm__required", "description": "GrpHdr-CreDtTm is required.", "descriptionTranslationProposal": "GrpHdr-CreDtTm ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-CreDtTm"}, {"id": "generated__Document-FICdtTrf-GrpHdr-NbOfTxs__pattern__d234cbf0", "description": "GrpHdr-NbOfTxs must have the value 1.", "descriptionTranslationProposal": "GrpHdr-NbOfTxs muss den Wert 1 haben.", "type": "pattern", "value": "^(1)?$", "target": "Document-FICdtTrf-GrpHdr-NbOfTxs"}, {"id": "generated__Document-FICdtTrf-GrpHdr-NbOfTxs__required", "description": "GrpHdr-NbOfTxs is required.", "descriptionTranslationProposal": "GrpHdr-NbOfTxs ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-NbOfTxs"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd__pattern__db338c5e", "description": "GrpHdr-SttlmInf-SttlmMtd must be one of the following values: INDA, INGA.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmMtd muss einer der folgenden Werte sein: INDA, INGA.", "type": "pattern", "value": "^(INDA|INGA)?$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd__required", "description": "GrpHdr-SttlmInf-SttlmMtd is required.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmMtd ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmMtd"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__pattern__4f91c237", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__pattern__b9be618b", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id muss dem <PERSON> ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__pattern__86207ffe", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__conditional-required__79845f19", "description": "If any of the fields GrpHdr-SttlmInf-SttlmAcct-Id-Othr-(SchmeNm or Issr) is present, then GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist GrpHdr-SttlmInf-SttlmAcct-Id-Othr-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__conditional-required__79845f19+Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__required-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id is required if any of the fields GrpHdr-SttlmInf-SttlmAcct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__pattern__86207ffe", "description": "GrpHdr-SttlmInf-SttlmAcct-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy__pattern__a4e1d645", "description": "GrpHdr-SttlmInf-SttlmAcct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__pattern__86207ffe", "description": "GrpHdr-SttlmInf-SttlmAcct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__maxLength", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__pattern__ffd2e254", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__conditional-required__4cbba7e3", "description": "If GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp is present, then GrpHdr-SttlmInf-SttlmAcct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp vorhanden ist, dann ist GrpHdr-SttlmInf-SttlmAcct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__conditional-required__4cbba7e3+Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__required-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id is required if GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp is present.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Prxy-Id ist erford<PERSON>, wenn GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id__conditional-required__e088366d", "description": "If any of the fields GrpHdr-SttlmInf-SttlmAcct-(Tp or Ccy or Nm or Prxy) is present, then GrpHdr-SttlmInf-SttlmAcct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist GrpHdr-SttlmInf-SttlmAcct-Id erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id__conditional-required__e088366d+Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__required-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN is required if any of the fields GrpHdr-SttlmInf-SttlmAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id__conditional-required__e088366d+Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__required-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id is required if any of the fields GrpHdr-SttlmInf-SttlmAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder GrpHdr-SttlmInf-SttlmAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId__required", "description": "CdtTrfTxInf-PmtId-InstrId is required.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-InstrId ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId__maxLength", "description": "CdtTrfTxInf-PmtId-InstrId must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-InstrId darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId__pattern__86207ffe", "description": "CdtTrfTxInf-PmtId-InstrId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-InstrId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-InstrId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId__required", "description": "CdtTrfTxInf-PmtId-EndToEndId is required.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-EndToEndId ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId__maxLength", "description": "CdtTrfTxInf-PmtId-EndToEndId must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-EndToEndId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId__pattern__86207ffe", "description": "CdtTrfTxInf-PmtId-EndToEndId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-EndToEndId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-EndToEndId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId__maxLength", "description": "CdtTrfTxInf-PmtId-TxId must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-TxId darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId__pattern__86207ffe", "description": "CdtTrfTxInf-PmtId-TxId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-TxId muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-TxId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR__pattern__2652818e", "description": "CdtTrfTxInf-PmtId-UETR must match the pattern ^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-UETR muss dem <PERSON> ^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$ entsprechen.", "type": "pattern", "value": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR__required", "description": "CdtTrfTxInf-PmtId-UETR is required.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-UETR ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-UETR"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef__maxLength", "description": "CdtTrfTxInf-PmtId-ClrSysRef must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-ClrSysRef darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef__pattern__86207ffe", "description": "CdtTrfTxInf-PmtId-ClrSysRef must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-ClrSysRef muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtId-ClrSysRef"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty__pattern__4d90026d", "description": "CdtTrfTxInf-PmtTpInf-InstrPrty must be one of the following values: HIGH, NORM.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-InstrPrty muss einer der folgenden Werte sein: HIGH, NORM.", "type": "pattern", "value": "^(HIGH|NORM)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl__pattern__4b3519fd", "description": "CdtTrfTxInf-PmtTpInf-ClrChanl must be one of the following values: RTGS, RTNS, MPNS, BOOK.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-Clr<PERSON><PERSON><PERSON> muss einer der folgenden Werte sein: RTGS, RTNS, MPNS, BOOK.", "type": "pattern", "value": "^(RTGS|RTNS|MPNS|BOOK)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl__maxItems", "description": "CdtTrfTxInf-PmtTpInf-SvcLvl must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-SvcLvl darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd__maxLength", "description": "CdtTrfTxInf-PmtTpInf-SvcLvl-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-SvcLvl-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry__maxLength", "description": "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-SvcLvl-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-SvcLvl-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd__maxLength", "description": "CdtTrfTxInf-PmtTpInf-LclInstrm-Cd must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-LclInstrm-Cd darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry__maxLength", "description": "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-LclInstrm-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-LclInstrm-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd__maxLength", "description": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry__maxLength", "description": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-CtgyPurp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtTpInf-CtgyPurp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-IntrBkSttlmAmt-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmAmt-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy__required", "description": "CdtTrfTxInf-IntrBkSttlmAmt-Ccy is required.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmAmt-Ccy ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__maxLength", "description": "CdtTrfTxInf-IntrBkSttlmAmt-amount must be at most 15 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmAmt-amount darf höchstens 15 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 15, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__pattern__6df9f186", "description": "CdtTrfTxInf-IntrBkSttlmAmt-amount must match the pattern ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmAmt-amount muss dem <PERSON> ^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$ entsprechen.", "type": "pattern", "value": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount__required", "description": "CdtTrfTxInf-IntrBkSttlmAmt-amount is required.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmAmt-amount ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt__pattern__8854bc02", "description": "CdtTrfTxInf-IntrBkSttlmDt must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmDt muss dem <PERSON>er ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt__required", "description": "CdtTrfTxInf-IntrBkSttlmDt is required.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrBkSttlmDt ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrBkSttlmDt"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmPrty__pattern__88007fa5", "description": "CdtTrfTxInf-SttlmPrty must be one of the following values: URGT, HIGH, NORM.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmPrty muss einer der folgenden Werte sein: URGT, HIGH, NORM.", "type": "pattern", "value": "^(URGT|HIGH|NORM)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmPrty"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm__pattern__e2ce1f8b", "description": "CdtTrfTxInf-SttlmTmIndctn-DbtDtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmIndctn-DbtDtTm muss dem Muster ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm__pattern__e2ce1f8b", "description": "CdtTrfTxInf-SttlmTmIndctn-CdtDtTm must match the pattern ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmIndctn-CdtDtTm muss dem <PERSON>er ^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm__pattern__34906ab2", "description": "CdtTrfTxInf-SttlmTmReq-CLSTm must match the pattern ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmReq-CLSTm muss dem <PERSON> ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm__pattern__34906ab2", "description": "CdtTrfTxInf-SttlmTmReq-TillTm must match the pattern ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmReq-TillTm muss dem <PERSON> ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm__pattern__34906ab2", "description": "CdtTrfTxInf-SttlmTmReq-FrTm must match the pattern ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmReq-FrTm muss dem <PERSON> ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm__pattern__34906ab2", "description": "CdtTrfTxInf-SttlmTmReq-RjctTm must match the pattern ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$.", "descriptionTranslationProposal": "CdtTrfTxInf-SttlmTmReq-RjctTm muss dem <PERSON> ^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$ entsprechen.", "type": "pattern", "value": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__a325dfed", "description": "If CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__a325dfed+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__4685be01", "description": "If CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__4685be01+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>, wenn CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept darf höchstens 70 Z<PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Adr<PERSON>ine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__conditional-required__445829d3", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__conditional-required__445829d3+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__conditional-required__f56a0da3", "description": "If CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp is present, then CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id <PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__conditional-required__f56a0da3+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id is required if CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id ist erford<PERSON>lich, wenn CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id__conditional-required__4f918827", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-PrvsInstgAgt1Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt1Acct-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id__conditional-required__4f918827+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id__conditional-required__4f918827+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__500b904e", "description": "If CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__500b904e+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__92b8c4e2", "description": "If CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__92b8c4e2+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Adr<PERSON>ine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__conditional-required__b2705430", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__conditional-required__b2705430+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__conditional-required__6755dc83", "description": "If CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp is present, then CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id <PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__conditional-required__6755dc83+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id is required if CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id ist erford<PERSON>lich, wenn CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id__conditional-required__134b0e44", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-PrvsInstgAgt2Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt2Acct-<PERSON>d er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id__conditional-required__134b0e44+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id__conditional-required__134b0e44+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__3b2fd02f", "description": "If CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__3b2fd02f+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erforderlich, wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__95159743", "description": "If CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__95159743+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Adr<PERSON>ine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__conditional-required__91d07811", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__conditional-required__91d07811+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd darf höchstens 4 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__conditional-required__25cf85e3", "description": "If CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp is present, then CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__conditional-required__25cf85e3+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id is required if CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id ist erford<PERSON>lich, wenn CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id__conditional-required__49ddcbe5", "description": "If any of the fields CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-PrvsInstgAgt3Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-PrvsInstgAgt3Acct-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id__conditional-required__49ddcbe5+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN ist erforderlich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id__conditional-required__49ddcbe5+Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-PrvsInstgAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI__required", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI is required.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-BICFI ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__d5b4d5b", "description": "If CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__d5b4d5b+Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__33404cf7", "description": "If CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__33404cf7+Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-InstgAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstgAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI__required", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI is required.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-BICFI ist erforderlich.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6876e758", "description": "If CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6876e758+Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__513a274", "description": "If CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__513a274+Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-InstdAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstdAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__7139e998", "description": "If CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__7139e998+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__83c26634", "description": "If CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__conditional-required__83c26634+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-L<PERSON> muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-C<PERSON> muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__conditional-required__f0453806", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__conditional-required__f0453806+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-<PERSON><PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-P<PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__conditional-required__c6f0d603", "description": "If CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp is present, then CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__conditional-required__c6f0d603+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id is required if CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id__conditional-required__93befd72", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt1Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-IntrmyAgt1Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt1Acct-Id erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id__conditional-required__93befd72+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-IntrmyAgt1Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id__conditional-required__93befd72+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt1Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt1Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__5877dbbb", "description": "If CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__5877dbbb+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__57efd2d7", "description": "If CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__conditional-required__57efd2d7+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-L<PERSON> muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-C<PERSON> muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__conditional-required__f4fc405", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__conditional-required__f4fc405+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-<PERSON><PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-P<PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__conditional-required__bab71c83", "description": "If CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp is present, then CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__conditional-required__bab71c83+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id is required if CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id__conditional-required__10e0def1", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt2Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-IntrmyAgt2Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt2Acct-Id erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id__conditional-required__10e0def1+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-IntrmyAgt2Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id__conditional-required__10e0def1+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt2Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt2Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6d539bda", "description": "If CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6d539bda+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__55930076", "description": "If CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__conditional-required__55930076+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-L<PERSON> muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON><PERSON> lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd darf höchstens 16 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-C<PERSON> muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Adr<PERSON>ine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id darf höchstens 34 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__conditional-required__9c1b8c04", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__conditional-required__9c1b8c04+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-<PERSON><PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__conditional-required__4d862383", "description": "If CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp is present, then CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__conditional-required__4d862383+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id is required if CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id__conditional-required__d22fb70", "description": "If any of the fields CdtTrfTxInf-IntrmyAgt3Acct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-IntrmyAgt3Acct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-IntrmyAgt3Acct-Id erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id__conditional-required__d22fb70+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN is required if any of the fields CdtTrfTxInf-IntrmyAgt3Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id__conditional-required__d22fb70+Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-IntrmyAgt3Acct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-IntrmyAgt3Acct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-Dbtr-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b740f90e", "description": "If CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__b740f90e+Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId__conditional-required__477f2c22", "description": "If CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId__conditional-required__477f2c22+Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-Dbtr-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-L<PERSON> muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd darf höchstens 16 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry muss dem Muster ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Dbtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-DbtrAcct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id muss dem <PERSON> ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Is<PERSON>r muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__conditional-required__20c1e150", "description": "If any of the fields CdtTrfTxInf-DbtrAcct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-DbtrAcct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-DbtrAcct-Id-Othr-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__conditional-required__20c1e150+Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-DbtrAcct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAcct-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-DbtrAcct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAcct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__conditional-required__3ec14b43", "description": "If CdtTrfTxInf-DbtrAcct-Prxy-Tp is present, then CdtTrfTxInf-DbtrAcct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-DbtrAcct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-DbtrAcct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__conditional-required__3ec14b43+Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAcct-Prxy-Id is required if CdtTrfTxInf-DbtrAcct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-DbtrAcct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id__conditional-required__71b4ca64", "description": "If any of the fields CdtTrfTxInf-DbtrAcct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-DbtrAcct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-DbtrAcct-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id__conditional-required__71b4ca64+Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-DbtrAcct-Id-IBAN is required if any of the fields CdtTrfTxInf-DbtrAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id__conditional-required__71b4ca64+Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-DbtrAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAcct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6debb05c", "description": "If CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__6debb05c+Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__ad115f70", "description": "If CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__ad115f70+Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__conditional-required__d59759a2", "description": "If any of the fields CdtTrfTxInf-DbtrAgtAcct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-DbtrAgtAcct-Id-Othr-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__conditional-required__d59759a2+Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-DbtrAgtAcct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgtAcct-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-DbtrAgtAcct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgtAcct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__conditional-required__64d9b703", "description": "If CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp is present, then CdtTrfTxInf-DbtrAgtAcct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-DbtrAgtAcct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__conditional-required__64d9b703+Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id is required if CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id__conditional-required__d51d0f96", "description": "If any of the fields CdtTrfTxInf-DbtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-DbtrAgtAcct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-DbtrAgtAcct-Id er<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id__conditional-required__d51d0f96+Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN is required if any of the fields CdtTrfTxInf-DbtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id__conditional-required__d51d0f96+Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-DbtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-DbtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__d76b3d3d", "description": "If CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__d76b3d3d+Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__e00440d1", "description": "If CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__conditional-required__e00440d1+Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId ist erford<PERSON>lich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-LEI muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room muss dem Muster ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry muss dem <PERSON> ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id muss dem <PERSON>er ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__conditional-required__42369903", "description": "If any of the fields CdtTrfTxInf-CdtrAgtAcct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-CdtrAgtAcct-Id-Othr-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__conditional-required__42369903+Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-CdtrAgtAcct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgtAcct-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-CdtrAgtAcct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgtAcct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__conditional-required__4112ae23", "description": "If CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp is present, then CdtTrfTxInf-CdtrAgtAcct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-CdtrAgtAcct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__conditional-required__4112ae23+Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id is required if CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id__conditional-required__b534adb7", "description": "If any of the fields CdtTrfTxInf-CdtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-CdtrAgtAcct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-CdtrAgtAcct-Id er<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id__conditional-required__b534adb7+Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN is required if any of the fields CdtTrfTxInf-CdtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id__conditional-required__b534adb7+Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-CdtrAgtAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAgtAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI__pattern__20cc4331", "description": "CdtTrfTxInf-Cdtr-FinInstnId-BICFI must match the pattern ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-BICFI muss dem <PERSON> ^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-BICFI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd must be at most 5 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd darf höchstens 5 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 5, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId must be at most 28 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId darf höchstens 28 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 28, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId__pattern__86207ffe", "description": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__2186a1cf", "description": "If CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId is present, then CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId vorhanden ist, dann ist CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId erforderlich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId__conditional-required__2186a1cf+Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd__required-conditional", "description": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is required if CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId is present.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd ist erford<PERSON>lich, wenn CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId__conditional-required__82e17163", "description": "If CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present, then CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist, dann ist CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId erford<PERSON>lich.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId__conditional-required__82e17163+Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId__required-conditional", "description": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId is required if CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd is present.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId ist erforderlich, wenn CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-ClrSysId-Cd vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-ClrSysMmbId-MmbId"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI__pattern__dc4f5bc9", "description": "CdtTrfTxInf-Cdtr-FinInstnId-LEI must match the pattern ^[A-Z0-9]{18,18}[0-9]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-L<PERSON> muss dem <PERSON> ^[A-Z0-9]{18,18}[0-9]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z0-9]{18,18}[0-9]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-LEI"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-Nm must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-Nm darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Dept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept darf höchstens 70 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-SubDept"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-StrtNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNb"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-BldgNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Flr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstBx"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Room"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd must be at most 16 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd darf höchstens 16 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 16, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-PstCd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-DstrctNm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn muss dem <PERSON>er ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry__pattern__a4aca9c5", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry must match the pattern ^[A-Z]{2,2}$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry muss dem <PERSON>er ^[A-Z]{2,2}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-Ctry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine__maxItems", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine must have at most 3 items.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine darf höchstens 3 Elemente haben.", "type": "maxItems", "value": 3, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine__maxLength", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine darf höchstens 70 <PERSON><PERSON>chen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine__pattern__ffd2e254", "description": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Cdtr-FinInstnId-PstlAdr-AdrLine"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN__pattern__4f91c237", "description": "CdtTrfTxInf-CdtrAcct-Id-IBAN must match the pattern ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-IBAN muss dem <PERSON> ^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id must be at most 34 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id darf höchstens 34 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 34, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__pattern__b9be618b", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id must match the pattern ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id muss dem <PERSON> ^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$ entsprechen.", "type": "pattern", "value": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-P<PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-P<PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Issr must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Is<PERSON>r muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__conditional-required__eafb6ff1", "description": "If any of the fields CdtTrfTxInf-CdtrAcct-Id-Othr-(SchmeNm or Issr) is present, then CdtTrfTxInf-CdtrAcct-Id-Othr-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist, dann ist CdtTrfTxInf-CdtrAcct-Id-Othr-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__conditional-required__eafb6ff1+Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-CdtrAcct-Id-Othr-(SchmeNm or Issr) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-Id-Othr-(SchmeNm oder Issr) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAcct-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy__pattern__a4e1d645", "description": "CdtTrfTxInf-CdtrAcct-Ccy must match the pattern ^[A-Z]{3,3}$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-<PERSON><PERSON> muss dem <PERSON> ^[A-Z]{3,3}$ entsprechen.", "type": "pattern", "value": "^[A-Z]{3,3}$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Nm must be at most 70 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Nm darf höchstens 70 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 70, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAcct-Nm must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Nm muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-P<PERSON>ry must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Tp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__maxLength", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Id must be at most 320 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Id darf höchstens 320 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 320, "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__pattern__ffd2e254", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Id must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Id muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__conditional-required__de7422c3", "description": "If CdtTrfTxInf-CdtrAcct-Prxy-Tp is present, then CdtTrfTxInf-CdtrAcct-Prxy-Id is required.", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAcct-Prxy-Tp vorhanden ist, dann ist CdtTrfTxInf-CdtrAcct-Prxy-Id er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__conditional-required__de7422c3+Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAcct-Prxy-Id is required if CdtTrfTxInf-CdtrAcct-Prxy-Tp is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Prxy-Id ist erford<PERSON>, wenn CdtTrfTxInf-CdtrAcct-Prxy-Tp vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id__conditional-required__5c5d4e85", "description": "If any of the fields CdtTrfTxInf-CdtrAcct-(Tp or Ccy or Nm or Prxy) is present, then CdtTrfTxInf-CdtrAcct-Id is required.", "descriptionTranslationProposal": "Wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist, dann ist CdtTrfTxInf-CdtrAcct-<PERSON><PERSON> er<PERSON>.", "type": "condition", "conditions": [{"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Nm", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "type": "present", "value": true}, {"field": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id", "type": "present", "value": true}], "conditionsConnector": "or", "rules": [{"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id__conditional-required__5c5d4e85+Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN__required-conditional", "description": "CdtTrfTxInf-CdtrAcct-Id-IBAN is required if any of the fields CdtTrfTxInf-CdtrAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-IBAN ist erford<PERSON>lich, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id__conditional-required__5c5d4e85+Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id__required-conditional", "description": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id is required if any of the fields CdtTrfTxInf-CdtrAcct-(Tp or Ccy or Nm or Prxy) is present.", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAcct-Id-Othr-Id ist erford<PERSON>, wenn mindestens eines der Felder CdtTrfTxInf-CdtrAcct-(Tp oder Ccy oder Nm oder Prxy) vorhanden ist.", "type": "required", "target": "Document-FICdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"}], "rulesConnector": "or"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt__maxItems", "description": "CdtTrfTxInf-InstrForCdtrAgt must have at most 2 items.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt darf höchstens 2 Elemente haben.", "type": "maxItems", "value": 2, "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__pattern__177b11d7", "description": "CdtTrfTxInf-InstrForCdtrAgt-Cd must be one of the following values: PHOB, TELB.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss einer der folgenden Werte sein: PHOB, TELB.", "type": "pattern", "value": "^(PHOB|TELB)?$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf__maxLength", "description": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf__pattern__86207ffe", "description": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt-InstrInf muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt__maxItems", "description": "CdtTrfTxInf-InstrForNxtAgt must have at most 6 items.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForNxtAgt darf höchstens 6 Elemente haben.", "type": "maxItems", "value": 6, "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf__maxLength", "description": "CdtTrfTxInf-InstrForNxtAgt-InstrInf must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForNxtAgt-InstrInf darf höchstens 35 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf__pattern__86207ffe", "description": "CdtTrfTxInf-InstrForNxtAgt-InstrInf must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForNxtAgt-InstrInf muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Purp-Cd__maxLength", "description": "CdtTrfTxInf-Purp-Cd must be at most 4 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Purp-Cd darf höchstens 4 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 4, "target": "Document-FICdtTrf-CdtTrfTxInf-Purp-Cd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry__maxLength", "description": "CdtTrfTxInf-Purp-<PERSON><PERSON>ry must be at most 35 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-Purp-<PERSON><PERSON><PERSON> darf höchstens 35 <PERSON>eichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry__pattern__86207ffe", "description": "CdtTrfTxInf-Purp-<PERSON><PERSON><PERSON> must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-Purp-<PERSON><PERSON><PERSON> muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-Purp-Prtry"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd__maxLength", "description": "CdtTrfTxInf-RmtInf-Ustrd must be at most 140 characters long.", "descriptionTranslationProposal": "CdtTrfTxInf-RmtInf-Ustrd darf höchstens 140 Zeichen lang sein.", "type": "max<PERSON><PERSON><PERSON>", "value": 140, "target": "Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd"}, {"id": "generated__Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd__pattern__ffd2e254", "description": "CdtTrfTxInf-RmtInf-Ustrd must match the pattern ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$.", "descriptionTranslationProposal": "CdtTrfTxInf-RmtInf-Ustrd muss dem <PERSON> ^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$ entsprechen.", "type": "pattern", "value": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$", "target": "Document-FICdtTrf-CdtTrfTxInf-RmtInf-Ustrd"}]