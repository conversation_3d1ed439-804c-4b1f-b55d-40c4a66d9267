<mxfile host="65bd71144e">
    <diagram name="Page-1" id="IBvD7A2SzJTeRsfK9zGP">
        <mxGraphModel dx="2949" dy="427" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-1" target="tcG0_RoeCLQiU8ru-Yyd-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-1" target="tcG0_RoeCLQiU8ru-Yyd-5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-1" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-360" y="200" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-3" value="required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-510" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-5" target="tcG0_RoeCLQiU8ru-Yyd-7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-5" target="tcG0_RoeCLQiU8ru-Yyd-9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-5" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-240" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-7" value="option1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-370" y="530" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-9" target="tcG0_RoeCLQiU8ru-Yyd-11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-9" target="tcG0_RoeCLQiU8ru-Yyd-12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-9" value="option2 oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-130" y="530" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-11" value="option2.1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-190" y="660" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-12" value="option2.2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-10" y="660" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-17" target="tcG0_RoeCLQiU8ru-Yyd-18" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-17" target="tcG0_RoeCLQiU8ru-Yyd-21" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-17" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-980" y="220" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-18" value="required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1130" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-21" target="tcG0_RoeCLQiU8ru-Yyd-22" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-21" target="tcG0_RoeCLQiU8ru-Yyd-25" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-21" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-860" y="420" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-22" value="option1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-990" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-25" value="option2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-750" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-30" target="tcG0_RoeCLQiU8ru-Yyd-31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-30" target="tcG0_RoeCLQiU8ru-Yyd-34" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-30" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-980" y="680" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-31" value="required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1130" y="870" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-34" target="tcG0_RoeCLQiU8ru-Yyd-35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-34" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-860" y="870" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-35" value="no requirements on either side" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-860" y="1010" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-39" target="tcG0_RoeCLQiU8ru-Yyd-43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-39" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2080" y="240" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-43" target="tcG0_RoeCLQiU8ru-Yyd-44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-43" target="tcG0_RoeCLQiU8ru-Yyd-45" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-43" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2080" y="380" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-44" value="option1 required rule" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2190" y="560" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-45" value="option2 required rule" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-1990" y="550" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-47" target="tcG0_RoeCLQiU8ru-Yyd-50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-47" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2075" y="700" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#60a917;strokeColor=#2D7600;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-50" target="tcG0_RoeCLQiU8ru-Yyd-52" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-50" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2075" y="840" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-52" value="no requirements on either side" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" parent="1" vertex="1">
                    <mxGeometry x="-2085" y="1000" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-54" target="tcG0_RoeCLQiU8ru-Yyd-57" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-54" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1620" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-57" target="tcG0_RoeCLQiU8ru-Yyd-58" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-57" target="tcG0_RoeCLQiU8ru-Yyd-59" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-57" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1620" y="360" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-58" value="option1 requiredRule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1730" y="540" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-59" target="tcG0_RoeCLQiU8ru-Yyd-60" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-63" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-59" target="tcG0_RoeCLQiU8ru-Yyd-62" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-59" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1530" y="530" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-60" value="option2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1585" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-65" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-62" target="tcG0_RoeCLQiU8ru-Yyd-64" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-62" target="tcG0_RoeCLQiU8ru-Yyd-66" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-62" value="option2 required oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1430" y="640" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-64" value="option2.1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1500" y="750" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-66" value="option2.2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="-1340" y="750" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-68" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-70" target="tcG0_RoeCLQiU8ru-Yyd-71" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-69" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-70" target="tcG0_RoeCLQiU8ru-Yyd-74" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-70" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="200" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-71" value="required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="130" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-74" target="tcG0_RoeCLQiU8ru-Yyd-75" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="tcG0_RoeCLQiU8ru-Yyd-74" target="tcG0_RoeCLQiU8ru-Yyd-76" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-74" value="required oneOf" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="400" y="390" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-75" value="option1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="270" y="530" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="tcG0_RoeCLQiU8ru-Yyd-76" value="option2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="510" y="530" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="6">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="oneOf branch" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1615" y="850" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="oneOf" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1615" y="990" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;rounded=0;curved=0;" edge="1" parent="1" source="7" target="17">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=0;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=classic;" edge="1" parent="1" source="7" target="19">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="object" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1725" y="1170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="option2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1525" y="1160" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="option1.1 required rule" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1810" y="1300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="option1.2 required rule" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-1640" y="1300" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;Considerations for nested required oneOfs&lt;/h1&gt;&lt;p&gt;See &lt;font style=&quot;color: rgb(153, 204, 255); background-color: rgb(0, 0, 0);&quot; face=&quot;Courier New&quot;&gt;getConditionalRulesForRequiredOneOf()&lt;/font&gt; in generate-rules.ts. There, the green-colored diagrams are currently implemented. If any of the other options appear, an error is thrown. This is currently not the case for the pacs.008 JSON schema.&lt;/p&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="-2420" y="40" width="550" height="120" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>