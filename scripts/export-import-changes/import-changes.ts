#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

interface FileChange {
  path: string;
  status: 'A' | 'M' | 'D';
  content?: string;
}

interface CommitData {
  hash: string;
  message: string;
  timestamp: number;
  files: FileChange[];
}

interface ExportData {
  generatedAt: number;
  commits: CommitData[];
  uncommittedChanges?: FileChange[];
}

function executeGitCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf8' }).trim();
  } catch (error) {
    throw new Error(`Git command failed: ${command}\n${error}`);
  }
}

function getLatestCommitTimestamp(): number | null {
  try {
    const timestampStr = executeGitCommand('git log -1 --pretty=format:"%ct"');
    return parseInt(timestampStr) * 1000; // Convert to milliseconds
  } catch (error) {
    // No commits in repository
    return null;
  }
}

function ensureDirectoryExists(filePath: string): void {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function applyFileChanges(files: FileChange[]): void {
  for (const file of files) {
    console.info(
      `  ${file.status === 'A' ? '➕' : file.status === 'M' ? '📝' : '🗑️'} ${
        file.path
      }`
    );

    switch (file.status) {
      case 'D':
        // Delete file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
        break;

      case 'A':
      case 'M':
        // Create or modify file
        if (file.content !== undefined) {
          ensureDirectoryExists(file.path);
          fs.writeFileSync(file.path, file.content, 'utf8');
        } else {
          console.warn(`  ⚠️  Warning: No content provided for ${file.path}`);
        }
        break;
    }
  }
}

function stageFiles(files: FileChange[]): void {
  for (const file of files) {
    try {
      if (file.status === 'D') {
        executeGitCommand(`git rm "${file.path}"`);
      } else {
        executeGitCommand(`git add "${file.path}"`);
      }
    } catch (error) {
      console.warn(`  ⚠️  Warning: Could not stage ${file.path}: ${error}`);
    }
  }
}

function validateTimestamps(exportData: ExportData): void {
  const latestLocalCommit = getLatestCommitTimestamp();

  if (exportData.commits.length > 0) {
    const oldestImportCommit = exportData.commits[0].timestamp;

    if (latestLocalCommit && oldestImportCommit <= latestLocalCommit) {
      throw new Error(
        `Import validation failed: The oldest commit in the import (${new Date(
          oldestImportCommit
        ).toISOString()}) ` +
          `is not newer than the latest local commit (${new Date(
            latestLocalCommit
          ).toISOString()})`
      );
    }
  }

  // If there are only uncommitted changes, check generation time against latest commit
  if (exportData.commits.length === 0 && exportData.uncommittedChanges) {
    if (latestLocalCommit && exportData.generatedAt <= latestLocalCommit) {
      throw new Error(
        `Import validation failed: The export generation time (${new Date(
          exportData.generatedAt
        ).toISOString()}) ` +
          `is not newer than the latest local commit (${new Date(
            latestLocalCommit
          ).toISOString()})`
      );
    }
  }
}

function main() {
  const importFilePath = path.join(process.cwd(), 'git-changes-export.json');

  if (!fs.existsSync(importFilePath)) {
    console.error(`❌ Import file not found: ${importFilePath}`);
    console.error(
      'Please ensure you have copied the git-changes-export.json file to the project root.'
    );
    process.exit(1);
  }

  try {
    // Read and parse the import file
    const importContent = fs.readFileSync(importFilePath, 'utf8');
    const exportData: ExportData = JSON.parse(importContent);

    console.info(
      `📥 Importing changes generated at: ${new Date(
        exportData.generatedAt
      ).toISOString()}`
    );
    console.info(
      `📊 Found ${exportData.commits.length} commits and ${
        exportData.uncommittedChanges?.length || 0
      } uncommitted changes`
    );

    // Validate timestamps
    validateTimestamps(exportData);

    // Check for uncommitted changes in current repo
    const hasUncommittedChanges =
      executeGitCommand('git status --porcelain').length > 0;
    if (hasUncommittedChanges) {
      console.error(
        '❌ Import failed: You have uncommitted changes. Please commit or stash them first.'
      );
      process.exit(1);
    }

    // Apply commits in order
    for (let i = 0; i < exportData.commits.length; i++) {
      const commit = exportData.commits[i];
      console.info(
        `\n🔄 Applying commit ${i + 1}/${exportData.commits.length}: "${
          commit.message
        }"`
      );
      console.info(
        `📅 Original timestamp: ${new Date(commit.timestamp).toISOString()}`
      );

      // Apply file changes
      applyFileChanges(commit.files);

      // Stage files
      stageFiles(commit.files);

      // Create commit with original timestamp
      const commitDate = new Date(commit.timestamp).toISOString();
      try {
        executeGitCommand(
          `git commit -m "${commit.message.replace(
            /"/g,
            '\\"'
          )}" --date="${commitDate}"`
        );
        console.info(`✅ Commit created successfully`);
      } catch (error) {
        console.error(`❌ Failed to create commit: ${error}`);
        throw error;
      }
    }

    // Apply uncommitted changes if any
    if (
      exportData.uncommittedChanges &&
      exportData.uncommittedChanges.length > 0
    ) {
      console.info(
        `\n🔄 Applying ${exportData.uncommittedChanges.length} uncommitted changes:`
      );
      applyFileChanges(exportData.uncommittedChanges);
      console.info(`✅ Uncommitted changes applied successfully`);
    }

    console.info(`\n🎉 Import completed successfully!`);
    console.info(`📝 Applied ${exportData.commits.length} commits`);

    if (
      exportData.uncommittedChanges &&
      exportData.uncommittedChanges.length > 0
    ) {
      console.info(
        `📝 Applied ${exportData.uncommittedChanges.length} uncommitted file changes`
      );
      console.info(
        `💡 Don't forget to commit the uncommitted changes when ready!`
      );
    }

    // Clean up - remove the contents of the import file
    fs.writeFileSync(importFilePath, '');
    console.info(`🧹 Cleaned up import file`);
  } catch (error) {
    console.error(
      '❌ Import failed:',
      error instanceof Error ? error.message : error
    );
    process.exit(1);
  }
}

main();
