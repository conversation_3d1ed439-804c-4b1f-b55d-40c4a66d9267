#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface FileChange {
  path: string;
  status: 'A' | 'M' | 'D'; // Added, Modified, Deleted
  content?: string; // undefined for deleted files
}

interface CommitData {
  hash: string;
  message: string;
  timestamp: number;
  files: FileChange[];
}

interface ExportData {
  generatedAt: number;
  commits: CommitData[];
  uncommittedChanges?: FileChange[];
}

function executeGitCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf8' }).trim();
  } catch (error) {
    throw new Error(`Git command failed: ${command}\n${error}`);
  }
}

function getFileContent(filePath: string): string | undefined {
  try {
    if (!fs.existsSync(filePath)) {
      return undefined;
    }
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.warn(`Warning: Could not read file ${filePath}: ${error}`);
    return undefined;
  }
}

function getAllFilesInDirectory(dirPath: string): string[] {
  const files: string[] = [];

  function traverse(currentPath: string) {
    const items = fs.readdirSync(currentPath);

    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        traverse(fullPath);
      } else {
        files.push(fullPath);
      }
    }
  }

  traverse(dirPath);
  return files;
}

// Simple ignore mechanism similar to a very small subset of .gitignore.
// For now we only support file-name patterns (no directories), one per line.
// Matching rule: a file is ignored if its basename equals the pattern OR
// the full normalized path contains the pattern as a substring.
const EXPORT_IGNORE_FILE = path.join(__dirname, '.export-ignore');

function loadExportIgnorePatterns(): string[] {
  try {
    if (!fs.existsSync(EXPORT_IGNORE_FILE)) return [];
    const content = fs.readFileSync(EXPORT_IGNORE_FILE, 'utf8');
    return content
      .split('\n')
      .map((l) => l.trim())
      .filter((l) => l && !l.startsWith('#'));
  } catch (e) {
    console.warn(`Warning: Could not read .export-ignore: ${e}`);
    return [];
  }
}

function isFileIgnored(filePath: string, patterns: string[]): boolean {
  const normalized = filePath.replace(/\\/g, '/');
  const base = path.basename(normalized);
  return patterns.some((p) => base === p || normalized.includes(p));
}

// Load ignore patterns once
const EXPORT_IGNORE_PATTERNS = loadExportIgnorePatterns();

function parseGitStatus(statusOutput: string): FileChange[] {
  const changes: FileChange[] = [];
  const lines = statusOutput
    .split('\n')
    .filter((line) => line.trim())
    .map((line) => line.trim());

  for (const line of lines) {
    if (line.length < 3) continue;

    const status = line.substring(0, 1);
    const filePath = line.substring(2);

    // Handle directory entries by finding all files within them
    if (fs.existsSync(filePath) && fs.statSync(filePath).isDirectory()) {
      if (status.includes('??')) {
        // Untracked directory
        const filesInDir = getAllFilesInDirectory(filePath);
        for (const file of filesInDir) {
          if (isFileIgnored(file, EXPORT_IGNORE_PATTERNS)) continue;
          const content = getFileContent(file);
          if (content !== undefined) {
            changes.push({
              path: file.replace(/\\/g, '/'), // Normalize path separators
              status: 'A',
              content,
            });
          }
        }
      }
      continue;
    }

    if (isFileIgnored(filePath, EXPORT_IGNORE_PATTERNS)) {
      continue;
    }

    let changeStatus: 'A' | 'M' | 'D';
    if (status.includes('D')) {
      changeStatus = 'D';
    } else if (status.includes('A') || status.includes('??')) {
      changeStatus = 'A';
    } else {
      changeStatus = 'M';
    }

    const content = changeStatus === 'D' ? undefined : getFileContent(filePath);

    // Only add if we have content for non-deleted files, or if it's a deletion
    if (changeStatus === 'D' || content !== undefined) {
      changes.push({
        path: filePath.replace(/\\/g, '/'), // Normalize path separators
        status: changeStatus,
        content,
      });
    }
  }

  return changes;
}

function getCommitChanges(commitHash: string): FileChange[] {
  const changes: FileChange[] = [];

  // Get list of changed files for this commit
  const filesOutput = executeGitCommand(
    `git diff-tree --no-commit-id --name-status -r ${commitHash}`
  );
  const lines = filesOutput.split('\n').filter((line) => line.trim());

  for (const line of lines) {
    const [status, filePath] = line.split('\t');

    if (isFileIgnored(filePath, EXPORT_IGNORE_PATTERNS)) {
      continue;
    }

    let changeStatus: 'A' | 'M' | 'D';
    if (status === 'D') {
      changeStatus = 'D';
    } else if (status === 'A') {
      changeStatus = 'A';
    } else {
      changeStatus = 'M';
    }

    let content: string | undefined;
    if (changeStatus !== 'D') {
      try {
        content = executeGitCommand(`git show ${commitHash}:${filePath}`);
      } catch (error) {
        console.warn(
          `Warning: Could not get content for ${filePath} at ${commitHash}`
        );
        content = undefined;
      }
    }

    changes.push({
      path: filePath,
      status: changeStatus,
      content,
    });
  }

  return changes;
}

function getCommitInfo(commitHash: string): {
  message: string;
  timestamp: number;
} {
  const message = executeGitCommand(
    `git log -1 --pretty=format:"%s" ${commitHash}`
  );
  const timestampStr = executeGitCommand(
    `git log -1 --pretty=format:"%ct" ${commitHash}`
  );
  const timestamp = parseInt(timestampStr) * 1000; // Convert to milliseconds

  return { message, timestamp };
}

function main() {
  const args = process.argv.slice(2);
  const commitsToInclude = args.length > 0 ? parseInt(args[0]) : 0;

  if (args.length > 0 && (isNaN(commitsToInclude) || commitsToInclude < 0)) {
    console.error(
      'Error: Please provide a valid number of commits (or no argument for uncommitted changes only)'
    );
    process.exit(1);
  }

  try {
    const exportData: ExportData = {
      generatedAt: Date.now(),
      commits: [],
    };

    // Get past commits if requested
    if (commitsToInclude > 0) {
      const commitHashes = executeGitCommand(
        `git log -${commitsToInclude} --pretty=format:"%H"`
      ).split('\n');

      // Process commits in chronological order (oldest first)
      for (let i = commitHashes.length - 1; i >= 0; i--) {
        const hash = commitHashes[i];
        const { message, timestamp } = getCommitInfo(hash);
        const files = getCommitChanges(hash);

        if (files.length > 0) {
          exportData.commits.push({
            hash,
            message,
            timestamp,
            files,
          });
        }
      }
    }

    // Get uncommitted changes
    const statusOutput = executeGitCommand('git status --porcelain');
    if (statusOutput) {
      exportData.uncommittedChanges = parseGitStatus(statusOutput);
    }

    // Write to JSON file
    const outputPath = path.join(process.cwd(), 'git-changes-export.json');
    fs.writeFileSync(outputPath, JSON.stringify(exportData, null, 2));

    console.info(`✅ Export completed successfully!`);
    console.info(`📁 Output file: ${outputPath}`);
    console.info(
      `📊 Exported ${exportData.commits.length} commits and ${
        exportData.uncommittedChanges?.length || 0
      } uncommitted changes`
    );

    if (exportData.commits.length > 0) {
      console.info(
        `📅 Commit range: ${new Date(
          exportData.commits[0].timestamp
        ).toISOString()} to ${new Date(
          exportData.commits[exportData.commits.length - 1].timestamp
        ).toISOString()}`
      );
    }
  } catch (error) {
    console.error(
      '❌ Export failed:',
      error instanceof Error ? error.message : error
    );
    process.exit(1);
  }
}

main();
