{
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Rule Generation Script",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/tsx/dist/cli.mjs",
      "args": [
        "--tsconfig",
        "projects/rule-generation/tsconfig.rule-generation.json",
        "projects/rule-generation/scripts/main.ts",
        "projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.json",
        "projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.xsd",
        "projects/rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/bah.xsd",
        "projects/rule-generation/input/pacs.008/form-setup.json",
        "--output-folder",
        "projects/iso20022-lib/rules/generated"
      ],
      "cwd": "${workspaceFolder}"
    }
  ]
}
